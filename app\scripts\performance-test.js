#!/usr/bin/env node

/**
 * Comprehensive Performance Test Suite
 * Tests both React application and trading system performance optimizations
 */

const {execSync} = require('child_process');
const fs = require('fs');
const path = require('path');

class PerformanceTestSuite {
    constructor() {
        // this.results = {
        reactApp: {
        }
    ,
        tradingSystem: {
        }
    ,
        overall: {
        }
    ,
        recommendations
    };
}

/**
 * Run comprehensive performance tests
 */
async
run()
{
    console.log('🚀 Starting Comprehensive Performance Test Suite...\n');

    try {
        // Test React application performance
        await this.testReactPerformance();

        // Test trading system performance
        await this.testTradingSystemPerformance();

        // Generate overall assessment
        // this.generateOverallAssessment();

        // Generate report
        const report = this.generateReport();
        await this.saveReport(report);

        console.log('\n✅ Performance Test Suite Complete!');
        // this.displayResults(report);

    } catch (error) {
        console.error('❌ Error during performance testing:', error);
        process.exit(1);
    }
}

/**
 * Test React application performance
 */
async
testReactPerformance()
{
    console.log('⚛️ Testing React Application Performance...');

    try {
        // Build the application
        console.log('  Building application...');
        const buildStart = Date.now();
        execSync('npm run build', {stdio: 'pipe'});
        const buildTime = Date.now() - buildStart;

        // Analyze bundle size
        const buildDir = path.join(__dirname, '..', 'build');
        const bundleAnalysis = this.analyzeBundleSize(buildDir);

        // Run build optimization analysis
        console.log('  Running build optimization analysis...');
        const optimizationStart = Date.now();
        execSync('npm run optimize-build', {stdio: 'pipe'});
        const optimizationTime = Date.now() - optimizationStart;

        // Read optimization report
        const reportPath = path.join(__dirname, '..', 'build-optimization-report.json');
        let optimizationReport = {};
        if (fs.existsSync(reportPath)) {
            optimizationReport = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
        }

        // this.results.reactApp = {
        buildTime,
            buildTimeFormatted(buildTime),
            bundleAnalysis,
            optimizationTime,
            optimizationReport,
            performance
    :
        {
            bundleSize,
            optimizationScore?.optimizationScore || 0,
            recommendations || []
        }
    }
    ;

    console.log(`  ✅ Build completed in ${this.formatTime(buildTime)}`);
    console.log(`  📦 Bundle size: ${bundleAnalysis.totalSize}`);
    console.log(`  📊 Optimization score: ${optimizationReport.summary?.optimizationScore || 0}/100\n`);

}
catch
(error)
{
    console.error('  ❌ React performance test failed:', error.message);
    // this.results.reactApp = {errorror.message};
}
}

/**
 * Analyze bundle size from build directory
 */
analyzeBundleSize(buildDir)
{
    const analysis = {
        totalSize: '0 MB',
        files,
        chunks
    };

    try {
        const staticDir = path.join(buildDir, 'static');

        if (fs.existsSync(staticDir)) {
            const jsDir = path.join(staticDir, 'js');
            const cssDir = path.join(staticDir, 'css');

            let totalBytes = 0;

            // Analyze JavaScript files
            if (fs.existsSync(jsDir)) {
                const jsFiles = fs.readdirSync(jsDir);
                jsFiles.forEach(file => {
                    const filePath = path.join(jsDir, file);
                    const stats = fs.statSync(filePath);
                    totalBytes += stats.size;

                    analysis.files.push({
                        name,
                        size(stats.size),
                        type
                :
                    'javascript'
                })
                    ;
                });
            }

            // Analyze CSS files
            if (fs.existsSync(cssDir)) {
                const cssFiles = fs.readdirSync(cssDir);
                cssFiles.forEach(file => {
                    const filePath = path.join(cssDir, file);
                    const stats = fs.statSync(filePath);
                    totalBytes += stats.size;

                    analysis.files.push({
                        name,
                        size(stats.size),
                        type
                :
                    'css'
                })
                    ;
                });
            }

            analysis.totalSize = this.formatBytes(totalBytes);
        }
    } catch (error) {
        console.warn('  ⚠️ Could not analyze bundle size:', error.message);
    }

    return analysis;
}

/**
 * Test trading system performance
 */
async
testTradingSystemPerformance()
{
    console.log('📈 Testing Trading System Performance...');

    try {
        // Run trading system performance optimizer
        console.log('  Running trading system optimizer...');
        const optimizerStart = Date.now();
        execSync('npm run optimize', {stdio: 'pipe'});
        const optimizerTime = Date.now() - optimizerStart;

        // Read trading system optimization report
        const reportPath = path.join(__dirname, '..', 'trading', 'performance-optimization-report.json');
        let tradingReport = {};
        if (fs.existsSync(reportPath)) {
            tradingReport = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
        }

        // this.results.tradingSystem = {
        optimizerTime,
            optimizerTimeFormatted(optimizerTime),
            report,
            performance
    :
        {
            optimizationScore?.optimizationScore || 0,
            totalTests?.totalTests || 0,
            recommendations || []
        }
    }
    ;

    console.log(`  ✅ Trading system analysis completed in ${this.formatTime(optimizerTime)}`);
    console.log(`  📊 Optimization score: ${tradingReport.summary?.optimizationScore || 0}/100`);
    console.log(`  🧪 Total tests: ${tradingReport.summary?.totalTests || 0}\n`);

}
catch
(error)
{
    console.error('  ❌ Trading system performance test failed:', error.message);
    // this.results.tradingSystem = {errorror.message};
}
}

/**
 * Generate overall performance assessment
 */
generateOverallAssessment()
{
    console.log('📋 Generating Overall Performance Assessment...');

    const reactScore = this.results.reactApp.performance?.optimizationScore || 0;
    const tradingScore = this.results.tradingSystem.performance?.optimizationScore || 0;
    const overallScore = (reactScore + tradingScore) / 2;

    const recommendations = [];

    // React app recommendations
    if (reactScore < 80) {
        recommendations.push({
            category: 'React Application',
            priority: 'high',
            issue: `Low optimization score: ${reactScore}/100`,
            solution: 'Implement recommended bundle optimizations and lazy loading improvements'
        });
    }

    // Trading system recommendations
    if (tradingScore < 80) {
        recommendations.push({
            category: 'Trading System',
            priority: 'high',
            issue: `Low optimization score: ${tradingScore}/100`,
            solution: 'Implement connection pooling and database query optimizations'
        });
    }

    // Bundle size recommendations
    const bundleSize = this.results.reactApp.bundleAnalysis?.totalSize;
    if (bundleSize && parseFloat(bundleSize) > 2) {
        recommendations.push({
            category: 'Bundle Size',
            priority: 'medium',
            issue: `Large bundle size: ${bundleSize}`,
            solution: 'Implement more aggressive code splitting and tree shaking'
        });
    }

    // Performance recommendations
    if (overallScore < 70) {
        recommendations.push({
            category: 'Overall Performance',
            priority: 'high',
            issue: 'System performance below acceptable threshold',
            solution: 'Prioritize high-impact optimizations and implement performance monitoring'
        });
    }

    // this.results.overall = {
    score,
        grade(overallScore),
        reactScore,
        tradingScore,
        recommendations
}
;

// this.results.recommendations = recommendations;

console.log(`  📊 Overall Performance Score: ${overallScore.toFixed(1)}/100 (${this.getPerformanceGrade(overallScore)})`);
console.log(`  ⚛️ React App Score: ${reactScore}/100`);
console.log(`  📈 Trading System Score: ${tradingScore}/100\n`);
}

/**
 * Get performance grade based on score
 */
getPerformanceGrade(score)
{
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B';
    if (score >= 60) return 'C';
    if (score >= 50) return 'D';
    return 'F';
}

/**
 * Generate comprehensive performance report
 */
generateReport()
{
    return {
        timestamp Date().toISOString(),
        summary: {
            overallScore,
            grade,
            reactScore,
            tradingScore,
            totalRecommendations
        },
        reactApplication,
        tradingSystem,
        overall,
        recommendations,
        actionItems()
    };
}

/**
 * Generate prioritized action items
 */
generateActionItems()
{
    const actionItems = [];

    // High priority items
    const highPriorityRecs = this.results.recommendations.filter(r => r.priority === 'high');
    highPriorityRecs.forEach(rec => {
        actionItems.push({
            priority: 'high',
            action,
            category,
            impact: 'High performance improvement expected'
        });
    });

    // Medium priority items
    const mediumPriorityRecs = this.results.recommendations.filter(r => r.priority === 'medium');
    mediumPriorityRecs.forEach(rec => {
        actionItems.push({
            priority: 'medium',
            action,
            category,
            impact: 'Moderate performance improvement expected'
        });
    });

    // General improvements
    actionItems.push({
        priority: 'low',
        action: 'Set up continuous performance monitoring',
        category: 'Monitoring',
        impact: 'Proactive performance issue detection'
    });

    actionItems.push({
        priority: 'low',
        action: 'Schedule regular performance optimization reviews',
        category: 'Process',
        impact: 'Maintain optimal performance over time'
    });

    return actionItems;
}

/**
 * Save performance report
 */
async
saveReport(report)
{
    const reportPath = path.join(__dirname, '..', 'comprehensive-performance-report.json');

    try {
        await fs.promises.writeFile(reportPath, JSON.stringify(report, null, 2));
        console.log(`📄 Comprehensive report saved: ${reportPath}`);
    } catch (error) {
        console.error('Failed to save report:', error);
    }
}

/**
 * Display test results
 */
displayResults(report)
{
    console.log('\n📊 Performance Test Results:');
    console.log(`Overall Score: ${report.summary.overallScore.toFixed(1)}/100 (${report.summary.grade})`);
    console.log(`React App: ${report.summary.reactScore}/100`);
    console.log(`Trading System: ${report.summary.tradingScore}/100`);

    if (report.recommendations.length > 0) {
        console.log('\n💡 Top Recommendations:');
        report.recommendations.slice(0, 3).forEach((rec, index) => {
            console.log(`${index + 1}. [${rec.priority.toUpperCase()}] ${rec.category}: ${rec.issue}`);
            console.log(`   Solution: ${rec.solution}\n`);
        });
    }

    if (report.actionItems.length > 0) {
        console.log('🎯 Next Action Items:');
        report.actionItems.slice(0, 3).forEach((item, index) => {
            console.log(`${index + 1}. [${item.priority.toUpperCase()}] ${item.action}`);
        });
    }

    // Performance grade interpretation
    console.log('\n📈 Performance Grade Guide:');
    console.log('A+ (90-100)cellent performance, minimal optimizations needed');
    console.log('A  (80-89)od performance, minor optimizations recommended');
    console.log('B  (70-79)ceptable performance, some optimizations needed');
    console.log('C  (60-69)low average, significant optimizations required');
    console.log('D  (50-59)or performance, major optimizations critical');
    console.log('F  (0-49)acceptable performance, immediate action required');
}

/**
 * Format time duration
 */
formatTime(ms)
{
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
}

/**
 * Format bytes to human readable format
 */
formatBytes(bytes)
{
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
}

// Run performance tests if called directly
if (require.main === module) {
    const testSuite = new PerformanceTestSuite();
    testSuite.run().catch(console.error);
}

module.exports = PerformanceTestSuite;