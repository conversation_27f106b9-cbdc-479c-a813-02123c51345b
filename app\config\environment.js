/**
 * @fileoverview Environment-specific configuration loader
 * Handles loading configuration based on NODE_ENV and provides
 * environment-specific settings for production deployment
 */

const path = require('path');
const fs = require('fs');

class EnvironmentConfig {
    constructor() {
        this.environment = process.env.NODE_ENV || 'development';
        this.isDevelopment = this.environment === 'development';
        this.isProduction = this.environment === 'production';
        this.isTesting = this.environment === 'test';
        
        this.config = this.loadEnvironmentConfig();
    }

    loadEnvironmentConfig() {
        const baseConfig = {
            app: {
                name: 'Meme Coin Trader',
                version: process.env.npm_package_version || '1.0.0',
                buildTime: new Date().toISOString(),
                environment: this.environment
            },
            electron: {
                isDev: this.isDevelopment,
                devTools: this.isDevelopment,
                contextIsolation: true,
                nodeIntegration: false,
                webSecurity: true
            },
            logging: {
                level: this.isDevelopment ? 'debug' : 'error',
                console: this.isDevelopment,
                file: this.isProduction,
                maxFiles: 5,
                maxSize: '10m'
            },
            performance: {
                enableMetrics: this.isProduction,
                enableProfiling: this.isDevelopment,
                memoryLimit: '512mb',
                cpuThreshold: 80
            },
            security: {
                enableCSP: this.isProduction,
                enableHSTS: this.isProduction,
                enableCORS: false,
                allowedOrigins: this.isDevelopment ? ['http://localhost:3000'] : []
            },
            features: {
                hotReload: this.isDevelopment,
                autoUpdate: this.isProduction,
                errorReporting: this.isProduction,
                analytics: this.isProduction,
                debugMode: this.isDevelopment
            },
            build: {
                optimization: this.isProduction,
                minification: this.isProduction,
                compression: this.isProduction,
                sourceMap: true,
                bundleAnalysis: false
            }
        };

        // Load environment-specific overrides
        const envConfigPath = path.join(__dirname, `${this.environment}.json`);
        if (fs.existsSync(envConfigPath)) {
            try {
                const envConfig = JSON.parse(fs.readFileSync(envConfigPath, 'utf8'));
                return this.mergeConfig(baseConfig, envConfig);
            } catch (error) {
                console.warn(`Failed to load environment config: ${error.message}`);
            }
        }

        return baseConfig;
    }

    mergeConfig(base, override) {
        const result = { ...base };
        
        for (const key in override) {
            if (override.hasOwnProperty(key)) {
                if (typeof override[key] === 'object' && !Array.isArray(override[key])) {
                    result[key] = this.mergeConfig(base[key] || {}, override[key]);
                } else {
                    result[key] = override[key];
                }
            }
        }
        
        return result;
    }

    get(path) {
        const keys = path.split('.');
        let current = this.config;
        
        for (const key of keys) {
            if (current && typeof current === 'object' && key in current) {
                current = current[key];
            } else {
                return undefined;
            }
        }
        
        return current;
    }

    getEnvironmentVariables() {
        return {
            NODE_ENV: this.environment,
            ELECTRON_IS_DEV: this.isDevelopment ? '1' : '0',
            REACT_APP_VERSION: this.config.app.version,
            REACT_APP_BUILD_TIME: this.config.app.buildTime,
            REACT_APP_ENVIRONMENT: this.environment,
            DEBUG: this.isDevelopment ? 'true' : 'false',
            LOG_LEVEL: this.config.logging.level,
            ENABLE_METRICS: this.config.performance.enableMetrics ? 'true' : 'false',
            ENABLE_ERROR_REPORTING: this.config.features.errorReporting ? 'true' : 'false'
        };
    }

    validateEnvironment() {
        const required = [
            'NODE_ENV'
        ];

        const missing = required.filter(key => !process.env[key]);
        
        if (missing.length > 0) {
            console.warn(`Missing required environment variables: ${missing.join(', ')}`);
            return false;
        }

        return true;
    }

    setupEnvironment() {
        // Set environment variables
        const envVars = this.getEnvironmentVariables();
        for (const [key, value] of Object.entries(envVars)) {
            if (!process.env[key]) {
                process.env[key] = value;
            }
        }

        // Configure Node.js environment
        if (this.isProduction) {
            process.env.NODE_OPTIONS = '--max-old-space-size=512';
        }

        return this.validateEnvironment();
    }
}

// Environment-specific configurations
const developmentConfig = {
    logging: {
        level: 'debug',
        console: true,
        file: false
    },
    features: {
        hotReload: true,
        autoUpdate: false,
        errorReporting: false,
        analytics: false,
        debugMode: true
    },
    build: {
        optimization: false,
        minification: false,
        compression: false,
        bundleAnalysis: true
    },
    electron: {
        devTools: true,
        webSecurity: false
    }
};

const productionConfig = {
    logging: {
        level: 'error',
        console: false,
        file: true
    },
    features: {
        hotReload: false,
        autoUpdate: true,
        errorReporting: true,
        analytics: true,
        debugMode: false
    },
    build: {
        optimization: true,
        minification: true,
        compression: true,
        bundleAnalysis: false
    },
    electron: {
        devTools: false,
        webSecurity: true
    },
    security: {
        enableCSP: true,
        enableHSTS: true,
        enableCORS: false
    }
};

const testConfig = {
    logging: {
        level: 'silent',
        console: false,
        file: false
    },
    features: {
        hotReload: false,
        autoUpdate: false,
        errorReporting: false,
        analytics: false,
        debugMode: false
    },
    build: {
        optimization: false,
        minification: false,
        compression: false,
        bundleAnalysis: false
    }
};

// Create and export environment config instance
const environmentConfig = new EnvironmentConfig();

// Setup environment on module load
environmentConfig.setupEnvironment();

module.exports = {
    EnvironmentConfig,
    environmentConfig,
    developmentConfig,
    productionConfig,
    testConfig
};