/**
 * Extended Electron API Type Definitions
 * These types extend the basic ElectronAPI to include event handling methods
 */

declare global {
    interface ElectronAPI {
        on?: (event: string, listener: (...args: any[]) => void) => void;
        removeListener?: (event: string, listener: (...args: any[]) => void) => void;
        emit?: (event: string, ...args: any[]) => void;
        getMarketData?: (symbol?: string) => Promise<{ success: boolean; data: any[] }>;
        fetchPriceHistory?: (symbol?: string, timeframe?: string) => Promise<{ success: boolean; data: any[] }>;

        [x: string]: any;
    }

    interface Window {
        electronAPI?: ElectronAPI;
    }
}

export {};
