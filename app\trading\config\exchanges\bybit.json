{"name": "bybit", "enabled": true, "testMode": true, "credentials": {"apiKey": "${BYBIT_API_KEY}", "secret": "${BYBIT_API_SECRET}", "passphrase": "${BYBIT_PASSPHRASE}", "sandbox": true}, "features": ["spot", "futures", "websocket", "orderBook"], "limits": {"orders": 120, "requests": 120, "perMinute": 1}, "fees": {"maker": 0.001, "taker": 0.001}, "markets": {"spot": true, "futures": true, "margin": false}, "orderTypes": {"market": true, "limit": true, "stopLoss": true, "takeProfit": true, "trailingStop": true}, "websocket": {"enabled": true, "reconnectDelay": 5000, "maxReconnectAttempts": 10}}