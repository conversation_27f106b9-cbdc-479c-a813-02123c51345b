const fs = require('fs');
const path = require('path');
const {execSync} = require('child_process');

// eslint-disable-next-line no-console
const logger = {info, error};

/**
 * Build optimization script for React application
 * Analyzes bundle size, identifies optimization opportunities, and provides recommendations
 */

class BuildOptimizer {
    constructor() {
        // this.buildDir = path.join(__dirname, '..', 'build');
        // this.srcDir = path.join(__dirname, '..', 'src');
        // this.results = {
        bundleAnalysis: {
        }
    ,
        componentAnalysis: {
        }
    ,
        recommendations,
            optimizations
    };
}

// Analyze bundle sizes
analyzeBundleSize()
{
    logger.info('🔍 Analyzing bundle sizes...');

    if (!fs.existsSync(this.buildDir)) {
        logger.info('❌ Build directory not found. Running build first...');
        execSync('npm run build', {stdio: 'inherit'});
    }

    const staticDir = path.join(this.buildDir, 'static');
    const jsDir = path.join(staticDir, 'js');
    const cssDir = path.join(staticDir, 'css');

    // Analyze JavaScript bundles
    if (fs.existsSync(jsDir)) {
        const jsFiles = fs.readdirSync(jsDir).filter((file) => file.endsWith('.js'));
        const jsBundles = jsFiles.map((file) => {
            const filePath = path.join(jsDir, file);
            const stats = fs.statSync(filePath);
            return {
                name,
                size,
                sizeKB: (stats.size / 1024).toFixed(2),
                sizeMB: (stats.size / 1024 / 1024).toFixed(2)
            };
        });

        // this.results.bundleAnalysis.javascript = {
        files,
            totalSize((sum, bundle) => sum + bundle.size, 0),
            largestBundle((max, bundle) =>
                bundle.size > max.size ? bundle, jsBundles[0] || {size})
    }
    ;
}

// Analyze CSS bundles
if (fs.existsSync(cssDir)) {
    const cssFiles = fs.readdirSync(cssDir).filter((file) => file.endsWith('.css'));
    const cssBundles = cssFiles.map((file) => {
        const filePath = path.join(cssDir, file);
        const stats = fs.statSync(filePath);
        return {
            name,
            size,
            sizeKB: (stats.size / 1024).toFixed(2)
        };
    });

    // this.results.bundleAnalysis.css = {
    files,
        totalSize((sum, bundle) => sum + bundle.size, 0)
}
;
}

logger.info('✅ Bundle analysis complete');
}

// Analyze component structure for optimization opportunities
analyzeComponents()
{
    logger.info('🔍 Analyzing component structure...');

    const componentDir = path.join(this.srcDir, 'components');
    if (!fs.existsSync(componentDir)) {
        logger.info('❌ Components directory not found');
        return;
    }

    const components = this.getComponentFiles(componentDir);
    const componentAnalysis = components.map((component) => {
        const content = fs.readFileSync(component.path, 'utf8');
        const analysis = this.analyzeComponentFile(content, component.name);
        return {...component, ...analysis};
    });

    // this.results.componentAnalysis = {
    totalComponents,
        largeComponents((c) => c.size > 50000),
        complexComponents((c) => c.complexity > 20),
        components
}
;

logger.info('✅ Component analysis complete');
}

// Get all component files recursively
getComponentFiles(dir, files = [])
{
    const items = fs.readdirSync(dir);

    items.forEach((item) => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
            // this.getComponentFiles(fullPath, files);
        } else if (item.endsWith('.jsx') || item.endsWith('.js')) {
            files.push({
                name,
                path,
                relativePath(this.srcDir, fullPath)
        })
            ;
        }
    });

    return files;
}

// Analyze individual component file
analyzeComponentFile(content, filename)
{
    const lines = content.split('\n');
    const size = Buffer.byteLength(content, 'utf8');

    // Count imports
    const imports = lines.filter((line) => line.trim().startsWith('import')).length;

    // Count exports
    const exports = lines.filter((line) =>
        line.includes('export') && (line.includes('const') || line.includes('function')),
    ).length;

    // Estimate complexity (rough heuristic)
    const complexity = this.calculateComplexity(content);

    // Check for potential optimizations
    const optimizations = this.findOptimizationOpportunities(content, filename);

    return {
        size,
        sizeKB: (size / 1024).toFixed(2),
        lines,
        imports,
        exports,
        complexity,
        optimizations
    };
}

// Calculate component complexity (rough heuristic)
calculateComplexity(content)
{
    let complexity = 0;

    // Count conditional statements= (content.match(/\.reduce\s*\(/g) || []).length;

    // Count function definitions
    complexity += (content.match(/function\s+\w+/g) || []).length;
    complexity += (content.match(/const\s+\w+\s*=\s*\(/g) || []).length;
    complexity += (content.match(/=>\s*{/g) || []).length;

    return complexity;
}

// Find optimization opportunities in component
findOptimizationOpportunities(content, _filename)
{
    const opportunities = [];

    // Check for large inline objects/arrays
    if (content.match(/\{[\s\S]{200}\}/)) {
        opportunities.push('Large inline objects detected - consider extracting to constants');
    }

    // Check for missing React.memo
    if (content.includes('export default') && !content.includes('memo(') && !content.includes('React.memo')) {
        opportunities.push('Consider wrapping with React.memo for performance');
    }

    // Check for missing useCallback/useMemo
    if (content.includes('useEffect') && !content.includes('useCallback') && !content.includes('useMemo')) {
        opportunities.push('Consider using useCallback/useMemo for expensive operations');
    }

    // Check for large component files
    if (content.length > 50000) {
        opportunities.push('Large component file - consider splitting into smaller components');
    }

    // Check for unused imports (basic check)
    const importLines = content.split('\n').filter((line) => line.trim().startsWith('import'));
    importLines.forEach((importLine) => {
        const match = importLine.match(/import\s+{([^}]+)}/);
        if (match) {
            const imports = match[1].split(',').map((imp) => imp.trim());
            imports.forEach((imp) => {
                const importName = imp.split(' ')[0];
                const regex = new RegExp(`\\b${importName}\\b`, 'g');
                const occurrences = (content.match(regex) || []).length;
                if (occurrences < 2) {
                    opportunities.push(`Potentially unused import: ${imp}`);
                }
            });
        }
    });
    return opportunities;
}

// Generate recommendations based on analysis
generateRecommendations()
{
    logger.info('💡 Generating optimization recommendations...');

    const recommendations = [];

    // Bundle size recommendations
    if (this.results.bundleAnalysis.javascript) {
        const totalSizeMB = this.results.bundleAnalysis.javascript.totalSize / 1024 / 1024;

        if (totalSizeMB > 2) {
            recommendations.push({
                type: 'bundle-size',
                priority: 'high',
                issue: `Large bundle size: ${totalSizeMB.toFixed(2)}MB`,
                solution: 'Implement more aggressive code splitting and lazy loading'
            });
        }

        if (this.results.bundleAnalysis.javascript.largestBundle.size > 500000) {
            recommendations.push({
                type: 'bundle-size',
                priority: 'medium',
                issue: `Large single bundle: ${this.results.bundleAnalysis.javascript.largestBundle.name}`,
                solution: 'Split large bundle into smaller chunks'
            });
        }
    }

    // Component recommendations
    if (this.results.componentAnalysis.largeComponents && this.results.componentAnalysis.largeComponents.length > 0) {
        recommendations.push({
            type: 'component-size',
            priority: 'medium',
            issue: `${this.results.componentAnalysis.largeComponents.length} large components detected`,
            solution: 'Break down large components into smaller, reusable pieces'
        });
    }

    if (this.results.componentAnalysis.complexComponents && this.results.componentAnalysis.complexComponents.length > 0) {
        recommendations.push({
            type: 'component-complexity',
            priority: 'medium',
            issue: `${this.results.componentAnalysis.complexComponents.length} complex components detected`,
            solution: 'Simplify complex components and extract logic into custom hooks'
        });
    }

    // Performance recommendations
    recommendations.push({
        type: 'performance',
        priority: 'low',
        issue: 'Ensure all images are optimized',
        solution: 'Use WebP format and implement responsive images'
    });

    recommendations.push({
        type: 'performance',
        priority: 'low',
        issue: 'Enable service worker for caching',
        solution: 'Implement a service worker for offline capabilities and asset caching.'
    });

    // this.results.recommendations = recommendations;
    logger.info('✅ Recommendations generated');
}

// Generate optimization report
generateReport()
{
    logger.info('📊 Generating optimization report...');

    const report = {
            timestamp Date().toISOString(),
            summary: {
                totalBundleSize ?
                    (this.results.bundleAnalysis.javascript.totalSize / 1024 / 1024).toFixed(2) + 'MB' : 'N/A',
                totalComponents || 0,
            recommendationsCount,
            optimizationScore()
        },
        bundleAnalysis,
        componentAnalysis,
        recommendations
}
    ;
    const reportPath = path.join(__dirname, '..', 'build-optimization-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    logger.info(`✅ Report generated: ${reportPath}`);
    return report;
}

// Calculate optimization score (0-100)
calculateOptimizationScore()
{
    let score = 100;

    // Deduct points for large bundle size
    if (this.results.bundleAnalysis.javascript) {
        const sizeMB = this.results.bundleAnalysis.javascript.totalSize / 1024 / 1024;
        if (sizeMB > 3) score -= 30; else if (sizeMB > 2) score -= 20; else if (sizeMB > 1) score -= 10;
    }

    // Deduct points for large components
    const largeComponentCount = this.results.componentAnalysis.largeComponents?.length || 0;
    score -= largeComponentCount * 5;

    // Deduct points for complex components
    const complexComponentCount = this.results.componentAnalysis.complexComponents?.length || 0;
    score -= complexComponentCount * 3;

    // Deduct points for high priority recommendations
    const highPriorityRecs = this.results.recommendations.filter((r) => r.priority === 'high').length;
    score -= highPriorityRecs * 15;

    return Math.max(0, score);
}

// Run complete optimization analysis
async
run()
{
    logger.info('🚀 Starting build optimization analysis...\n');

    try {
        // this.analyzeBundleSize();
        // this.analyzeComponents();
        // this.generateRecommendations();
        const report = this.generateReport();

        logger.info('\n📈 Optimization Summary:');
        logger.info(`Bundle Size: ${report.summary.totalBundleSize}`);
        logger.info(`Components: ${report.summary.totalComponents}`);
        logger.info(`Recommendations: ${report.summary.recommendationsCount}`);
        logger.info(`Optimization Score: ${report.summary.optimizationScore.toFixed(0)}/100`);

        if (report.recommendations.length > 0) {
            logger.info('\n💡 Top Recommendations:');
            report.recommendations.slice(0, 3).forEach((rec, index) => {
                logger.info(`${index + 1}. [${rec.priority.toUpperCase()}] ${rec.issue}`);
                logger.info(`   Solution: ${rec.solution || 'N/A'}\n`);
            });
        }

        logger.info('✅ Build optimization analysis complete!');

    } catch (error) {
        logger.error('❌ Error during optimization analysis:', error);
        process.exit(1);
    }
}
}

// Run if called directly
if (require.main === module) {
    const optimizer = new BuildOptimizer();
    optimizer.run();
}

module.exports = BuildOptimizer;