'use strict';

import logger from './logger.js';
import {useState, useEffect} from 'react';

/**
 * @fileoverview Startup Performance Optimizer
 * @description Analyzes device and network conditions to apply appropriate performance optimizations during application startup.
 * This includes managing lazy loading, optimizing assets, and providing performance reports.
 *
 * <AUTHOR>
 * @version 1.1.0
 * @since 2025-01-22
 */

/**
 * @typedef {'aggressive' | 'moderate' | 'minimal'} OptimizationStrategy
 */

/**
 * @typedef {Object} PerformanceMetrics
 * @property {number} startTime - The timestamp when monitoring started.
 * @property {Map<string, number>} componentLoadTimes - Load times for individual components.
 * @property {Map<string, Object>} bundleLoadTimes - Metrics for loaded JavaScript bundles.
 * @property {Object} networkMetrics - Network condition metrics.
 * @property {Object} deviceMetrics - Device capability metrics.
 */

class StartupOptimizer {
    constructor() {
        /** @type {PerformanceMetrics} */
        this.metrics = {
            startTime: performance.now(),
            componentLoadTimes: new Map(),
            bundleLoadTimes: new Map(),
            networkMetrics: this.getNetworkMetrics(),
            deviceMetrics: this.getDeviceMetrics(),
        };

        /** @type {Map<string, Function>} */
        this.optimizationStrategies = new Map();
        this.performanceObserver = null;
        this.initializePerformanceMonitoring();
    }

    /**
     * Retrieves network performance metrics from the browser's navigator.
     * @returns {{effectiveType: string, downlink: number, rtt: number, saveData: boolean}}
     */
    getNetworkMetrics() {
        try {
            const nav = typeof navigator !== 'undefined' ? navigator : {};
            // @ts-ignore
            const connection = nav.connection || nav.mozConnection || nav.webkitConnection;
            if (connection) {
                return {
                    effectiveType: connection.effectiveType || 'unknown',
                    downlink: connection.downlink || 0,
                    rtt: connection.rtt || 0,
                    saveData: connection.saveData || false,
                };
            }
        } catch (error) {
            logger.warn('Could not retrieve network metrics.', error);
        }
        return {effectiveType: 'unknown', downlink: 0, rtt: 0, saveData: false};
    }

    /**
     * Retrieves device performance metrics from the browser's navigator.
     * @returns {{deviceMemory: number, hardwareConcurrency: number, maxTouchPoints: number, userAgent: string}}
     */
    getDeviceMetrics() {
        try {
            const nav = typeof navigator !== 'undefined' ? navigator : {};
            return {
                // @ts-ignore
                deviceMemory: nav.deviceMemory || 4,
                hardwareConcurrency: nav.hardwareConcurrency || 4,
                maxTouchPoints: nav.maxTouchPoints || 0,
                userAgent: nav.userAgent || 'unknown',
            };
        } catch (error) {
            logger.warn('Could not retrieve device metrics.', error);
        }
        return {deviceMemory: 4, hardwareConcurrency: 4, maxTouchPoints: 0, userAgent: 'unknown'};
    }

    /**
     * Initializes the PerformanceObserver to monitor resource and component load times.
     */
    initializePerformanceMonitoring() {
        if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
            this.performanceObserver = new PerformanceObserver(list => {
                list.getEntries().forEach(entry => {
                    if (entry.entryType === 'resource') {
                        this.trackResourceLoad(/** @type {PerformanceResourceTiming} */ (entry));
                    } else if (entry.entryType === 'measure') {
                        this.trackCustomMeasure(/** @type {PerformanceMeasure} */ (entry));
                    }
                });
            });
            try {
                this.performanceObserver.observe({
                    entryTypes: ['resource', 'measure', 'navigation'],
                });
            } catch (error) {
                logger.warn('PerformanceObserver initialization failed:', error);
            }
        }
    }

    /**
     * Tracks the loading performance of JavaScript bundles.
     * @param {PerformanceResourceTiming} entry - The performance entry for the resource.
     */
    trackResourceLoad(entry) {
        if (entry.name.includes('.chunk.js') || entry.name.includes('.bundle.js')) {
            const resourceName = entry.name.split('/').pop();
            this.metrics.bundleLoadTimes.set(resourceName, {
                duration: entry.duration,
                size: entry.encodedBodySize,
                startTime: entry.startTime,
                responseEnd: entry.responseEnd,
            });
        }
    }

    /**
     * Tracks custom performance measures, specifically for component loads.
     * @param {PerformanceMeasure} entry - The performance entry for the measure.
     */
    trackCustomMeasure(entry) {
        if (entry.name.startsWith('component-load-')) {
            const componentName = entry.name.replace('component-load-', '');
            this.metrics.componentLoadTimes.set(componentName, entry.duration);
        }
    }

    /**
     * Measures and records the loading time of a specific component.
     * @param {string} componentName - The name of the component.
     * @param {number} startTime - The timestamp when loading began.
     * @returns {number} The duration of the component load in milliseconds.
     */
    measureComponentLoad(componentName, startTime) {
        const endTime = performance.now();
        const duration = endTime - startTime;
        this.metrics.componentLoadTimes.set(componentName, duration);

        try {
            performance.mark(`component-load-${componentName}-end`);
            performance.measure(`component-load-${componentName}`, {
                start: startTime,
                end: endTime,
            });
        } catch (error) {
            logger.debug(`Component ${componentName} loaded in ${duration.toFixed(2)}ms (fallback measure)`);
        }
        return duration;
    }

    /**
     * Determines the optimal performance strategy based on device and network conditions.
     * @returns {OptimizationStrategy} The determined optimization strategy.
     */
    getOptimizationStrategy() {
        const {networkMetrics, deviceMetrics} = this.metrics;
        const isLowEndDevice = deviceMetrics.deviceMemory < 4 || deviceMetrics.hardwareConcurrency < 4;
        const isSlowNetwork = ['slow-2g', '2g'].includes(networkMetrics.effectiveType) || networkMetrics.saveData;

        if (isLowEndDevice && isSlowNetwork) {
            return 'aggressive';
        }
        if (isLowEndDevice || isSlowNetwork) {
            return 'moderate';
        }
        return 'minimal';
    }

    /**
     * Applies startup optimizations based on the determined strategy.
     * @returns {OptimizationStrategy} The applied strategy.
     */
    applyStartupOptimizations() {
        const strategy = this.getOptimizationStrategy();
        switch (strategy) {
            case 'aggressive':
                // this.applyAggressiveOptimizations();
                break;
            case 'moderate':
                // this.applyModerateOptimizations();
                break;
            case 'minimal':
                // this.applyMinimalOptimizations();
                break;
        }
        return strategy;
    }

    /**
     * Applies aggressive optimizations for low-end devices and slow networks.
     */
    applyAggressiveOptimizations() {
        document.documentElement.style.setProperty('--animation-duration', '0s');
        // this.optimizeImages('low');
        // this.deferNonCriticalScripts();
        // this.enableAggressiveLazyLoading();
        logger.info('🔧 Applied aggressive startup optimizations');
    }

    /**
     * Applies moderate optimizations for mid-range conditions.
     */
    applyModerateOptimizations() {
        document.documentElement.style.setProperty('--animation-duration', '0.2s');
        // this.optimizeImages('medium');
        // this.enableStandardLazyLoading();
        logger.info('🔧 Applied moderate startup optimizations');
    }

    /**
     * Applies minimal optimizations for high-end devices and fast networks.
     */
    applyMinimalOptimizations() {
        document.documentElement.style.setProperty('--animation-duration', '0.3s');
        // this.optimizeImages('high');
        // this.enableIntelligentPreloading();
        logger.info('🔧 Applied minimal startup optimizations');
    }

    /**
     * Adjusts image quality based on the optimization strategy.
     * @param {'low' | 'medium' | 'high'} quality - The target image quality.
     */
    optimizeImages(quality) {
        const images = document.querySelectorAll('img[data-src]');
        const qualityMap = {low: 0.5, medium: 0.75, high: 1.0};
        images.forEach(img => {
            const image = /** @type {HTMLImageElement} */ (img);
            if (image.dataset.quality) {
                image.style.filter = `contrast(${qualityMap[quality]})`;
            }
        });
    }

    /**
     * Defers the loading of non-critical scripts.
     */
    deferNonCriticalScripts() {
        const scripts = document.querySelectorAll('script[data-defer]');
        scripts.forEach(script => {
            const scriptElement = /** @type {HTMLScriptElement} */ (script);
            scriptElement.defer = true;
        });
    }

    /**
     * Configures aggressive lazy loading for images and components.
     */
    enableAggressiveLazyLoading() {
        if (typeof window !== 'undefined') {
            /** @type {any} */
            (window).lazyLoadingOptions = {rootMargin: '50px', threshold: 0.01};
        }
    }

    /**
     * Configures standard lazy loading.
     */
    enableStandardLazyLoading() {
        if (typeof window !== 'undefined') {
            /** @type {any} */
            (window).lazyLoadingOptions = {rootMargin: '100px', threshold: 0.01};
        }
    }

    /**
     * Enables preloading of assets likely to be needed soon.
     */
    enableIntelligentPreloading() {
        if (typeof window !== 'undefined') {
            /** @type {any} */
            (window).lazyLoadingOptions = {rootMargin: '200px', threshold: 0.01, enablePreloading: true};
        }
    }

    /**
     * Generates a comprehensive performance report.
     * @returns {Object} The performance report.
     */
    getPerformanceReport() {
        const totalStartupTime = performance.now() - this.metrics.startTime;
        const componentLoadTimes = Object.fromEntries(this.metrics.componentLoadTimes);
        const bundleLoadTimes = Object.fromEntries(this.metrics.bundleLoadTimes);
        const averageComponentLoadTime =
            this.metrics.componentLoadTimes.size > 0
                ? Array.from(this.metrics.componentLoadTimes.values()).reduce((sum, time) => sum + time, 0) /
                  this.metrics.componentLoadTimes.size
                : 0;
        const totalBundleSize = Array.from(this.metrics.bundleLoadTimes.values()).reduce(
            (sum, bundle) => sum + (bundle.size || 0),
            0,
        );

        return {
            totalStartupTime,
            averageComponentLoadTime,
            totalBundleSize: `${(totalBundleSize / 1024 / 1024).toFixed(2)} MB`,
            componentLoadTimes,
            bundleLoadTimes,
            networkMetrics: this.metrics.networkMetrics,
            deviceMetrics: this.metrics.deviceMetrics,
            optimizationStrategy: this.getOptimizationStrategy(),
            recommendations: this.getPerformanceRecommendations(),
        };
    }

    /**
     * Generates performance recommendations based on collected metrics.
     * @returns {Array<Object>} A list of performance recommendations.
     */
    getPerformanceRecommendations() {
        const recommendations = [];
        const {componentLoadTimes, bundleLoadTimes, networkMetrics, deviceMetrics} = this.metrics;

        componentLoadTimes.forEach((time, component) => {
            if (time > 1000) {
                recommendations.push({
                    type: 'component',
                    severity: 'high',
                    message: `Component ${component} is loading slowly (${time.toFixed(2)}ms).`,
                    suggestion: 'Consider code splitting or lazy loading this component.',
                });
            }
        });

        bundleLoadTimes.forEach((bundle, name) => {
            const sizeKB = (bundle.size || 0) / 1024;
            if (sizeKB > 500) {
                recommendations.push({
                    type: 'bundle',
                    severity: 'medium',
                    message: `Bundle ${name} is large (${sizeKB.toFixed(2)} KB).`,
                    suggestion: 'Analyze the bundle and consider further code splitting.',
                });
            }
        });

        if (['slow-2g', '2g'].includes(networkMetrics.effectiveType)) {
            recommendations.push({
                type: 'network',
                severity: 'high',
                message: 'A slow network has been detected.',
                suggestion: 'Enable aggressive optimizations and reduce the initial bundle size.',
            });
        }

        if (deviceMetrics.deviceMemory < 4) {
            recommendations.push({
                type: 'device',
                severity: 'medium',
                message: 'A low-memory device has been detected.',
                suggestion: 'Reduce memory usage and enable aggressive lazy loading.',
            });
        }

        return recommendations;
    }

    /**
     * Cleans up the performance monitoring by disconnecting the observer.
     */
    cleanup() {
        if (this.performanceObserver) {
            // this.performanceObserver.disconnect();
            // this.performanceObserver = null;
        }
    }
}

// --- Singleton and React Hook ---

const startupOptimizerInstance = new StartupOptimizer();

/**
 * Provides access to the singleton instance of the StartupOptimizer.
 * @returns {StartupOptimizer} The singleton instance.
 */
const getStartupOptimizer = () => {
    return startupOptimizerInstance;
};

/**
 * A React hook to apply startup optimizations and access performance data.
 * @returns {{optimizer: StartupOptimizer, strategy: OptimizationStrategy | null, report: Object | null}}
 */
const useStartupOptimization = () => {
    const [optimizer] = useState(() => getStartupOptimizer());
    const [strategy, setStrategy] = useState(null);
    const [report, setReport] = useState(null);

    useEffect(() => {
        const appliedStrategy = optimizer.applyStartupOptimizations();
        setStrategy(appliedStrategy);

        const reportTimer = setTimeout(() => {
            setReport(optimizer.getPerformanceReport());
        }, 5000); // Generate report after a delay to capture metrics

        return () => {
            clearTimeout(reportTimer);
            optimizer.cleanup();
        };
    }, [optimizer]);

    return {optimizer, strategy, report};
};

export {StartupOptimizer, getStartupOptimizer, useStartupOptimization};

export default {
    StartupOptimizer,
    getStartupOptimizer,
    useStartupOptimization,
};