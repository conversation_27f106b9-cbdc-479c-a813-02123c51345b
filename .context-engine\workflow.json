[{"id": "setup", "name": "Project Setup", "description": "Ensure all dependencies are installed and project is ready", "command": "npm install", "completed": false, "estimatedTime": "2-5 minutes", "prerequisites": []}, {"id": "lint", "name": "Code Quality Check", "description": "Run linting and fix any code quality issues", "command": "npm run lint", "completed": false, "estimatedTime": "1-3 minutes", "prerequisites": ["setup"]}, {"id": "test", "name": "Run Tests", "description": "Execute all tests to ensure current functionality works", "command": "npm test", "completed": false, "estimatedTime": "2-10 minutes", "prerequisites": ["setup"]}, {"id": "build", "name": "Build Project", "description": "Compile and build the project", "command": "npm run build", "completed": false, "estimatedTime": "1-5 minutes", "prerequisites": ["lint", "test"]}, {"id": "deploy", "name": "Deploy/Package", "description": "Deploy to staging or package for distribution", "completed": false, "estimatedTime": "5-15 minutes", "prerequisites": ["build"]}]