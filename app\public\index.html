<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta content="#000000" name="theme-color"/>
    <meta content="ElectronTrader - Advanced Cryptocurrency Trading Platform" name="description"/>
    <title>ElectronTrader</title>
    <style>
        /* Initial loading styles */
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
            'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
            sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: #0f0f23;
            color: #ffffff;
            overflow-x: hidden;
        }

        #root {
            min-height: 100vh;
        }

        /* Loading spinner for initial load */
        .initial-loader {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .initial-loader:after {
            content: " ";
            display: block;
            width: 64px;
            height: 64px;
            margin: 8px auto;
            border-radius: 50%;
            border: 6px solid #00eaff;
            border-color: #00eaff transparent #00eaff transparent;
            animation: loader-spin 1.2s linear infinite;
        }

        @keyframes loader-spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        noscript {
            display: block;
            text-align: center;
            padding: 20px;
            background-color: #f44336;
            color: white;
        }
    </style>
</head>
<body>
<noscript>You need to enable JavaScript to run this application.</noscript>
<div id="root">
    <div class="initial-loader"></div>
</div>
</body>
</html>