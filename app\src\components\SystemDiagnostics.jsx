 
'use strict';

Object.defineProperty(exports, '__esModule', {
    value: true
});
exports.default = SystemDiagnostics;
const _react = _interopRequireWildcard(require('react'));
const _propTypes = _interopRequireDefault(require('prop-types'));
const _framerMotion = require('framer-motion');
const _logger = _interopRequireDefault(require('../utils/logger'));
const _material = require('@mui/material');
const _iconsMaterial = require('@mui/icons-material');
const _HolographicCard = _interopRequireDefault(require('./HolographicCard'));
const _ElectronAPITester = require('../utils/ElectronAPITester');
const _PerformanceMonitor = require('../utils/PerformanceMonitor');

function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}

function _interopRequireWildcard(e, t) {
    if ('function' == typeof WeakMap) var r = new WeakMap(),
        n = new WeakMap();
    return (_interopRequireWildcard = function (e, t) {
        if (!t && e && e.__esModule) return e;
        let o,
            i,
            f = {
                __proto__: null,
                default: e
            };
        if (null === e || 'object' != typeof e && 'function' != typeof e) return f;
        if (o = t ? n : r) {
            if (o.has(e)) return o.get(e);
            o.set(e, f);
        }
        for (const t in e) 'default' !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]);
        return f;
    })(e, t);
}

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) : e[r] = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i : i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String : Number)(t);
} // Import logger for consistent logging
// Import testing utilities
/**
 * System Diagnostics Component
 * Comprehensive testing and monitoring interface for the trading application
 */
function SystemDiagnostics({
                               onClose
                           }) {
    const [activeTab, setActiveTab] = (0, _react.useState)(0);
    const [isRunningTests, setIsRunningTests] = (0, _react.useState)(false);
    const [testResults, setTestResults] = (0, _react.useState)(null);
    const [performanceData, setPerformanceData] = (0, _react.useState)(null);
    const [expandedSections, setExpandedSections] = (0, _react.useState)({});
    const [systemHealth, setSystemHealth] = (0, _react.useState)('unknown');
    const performanceMonitor = (0, _PerformanceMonitor.usePerformanceMonitor)();

    /**
     * Load performance monitoring data
     */
    const loadPerformanceData = (0, _react.useCallback)(() => {
        // Safely construct performance data from available properties
        const data = {
            loadTime: 0,
            renderTime: 0,
            memory: 0,
            categories: {}
        };
        // Check if performanceMonitor exists and has expected structure
        if (performanceMonitor) {
            data.loadTime = performanceMonitor.loadTime || 0;
            data.renderTime = performanceMonitor.renderTime || 0;
            data.memory = performanceMonitor.memory || 0;
        }
        setPerformanceData(data);
    }, [performanceMonitor]);

    /**
     * Check overall system health
     */
    const checkSystemHealth = (0, _react.useCallback)(() => {
        const isElectron = !!window['electronAPI'];
        const hasWebSocket = !!window.WebSocket;
        const hasPerformanceAPI = !!window.performance;
        if (isElectron && hasWebSocket && hasPerformanceAPI) {
            setSystemHealth('healthy');
        } else if (isElectron) {
            setSystemHealth('warning');
        } else {
            setSystemHealth('error');
        }
    }, []);

    // Load initial data
    (0, _react.useEffect)(() => {
        loadPerformanceData();
        checkSystemHealth();
    }, [loadPerformanceData, checkSystemHealth]);

    /**
     * Run comprehensive API tests
     */
    const runComprehensiveTests = async callback => {
        setIsRunningTests(true);
        try {
            const results = await (0, _ElectronAPITester.runAPITests)();
            setTestResults(results);
            if (callback) callback(results);
        } catch (error) {
            _logger.default.error('Test execution failed:', error);
            const errorResult = {
                success: false,
                error: error.message,
                totalTests: 0,
                passedTests: 0,
                failedTests: 0
            };
            setTestResults(errorResult);
            if (callback) callback(errorResult);
        } finally {
            setIsRunningTests(false);
        }
    };

    /**
     * Run tests for specific category
     */
    const runCategoryTests = async (category, callback) => {
        setIsRunningTests(true);
        try {
            const results = await (0, _ElectronAPITester.testCategory)(category);
            // Update test results with category-specific data
            setTestResults(prevResults => _objectSpread(_objectSpread({}, prevResults), {}, {
                categories: _objectSpread(_objectSpread({}, prevResults === null || prevResults === void 0 ? void 0 : prevResults.categories), {}, {
                    [category]: results
                })
            }));
            if (callback) callback(results);
        } catch (error) {
            _logger.default.error(`Category test failed for ${category}:`, error);
            if (callback) callback({
                success: false,
                error: error.message
            });
        } finally {
            setIsRunningTests(false);
        }
    };

    /**
     * Toggle section expansion
     */
    const toggleSection = section => {
        setExpandedSections(prevSections => _objectSpread(_objectSpread({}, prevSections), {}, {
            [section]: !prevSections[section]
        }));
    };

    /**
     * Get status color and icon
     */
    const getStatusIndicator = status => {
        switch (status) {
            case 'passed':
            case 'healthy':
                return {
                    color: '#4caf50',
                    icon: _iconsMaterial.CheckCircle
                };
            case 'failed':
            case 'error':
                return {
                    color: '#f44336',
                    icon: _iconsMaterial.Error
                };
            case 'warning':
                return {
                    color: '#ff9800',
                    icon: _iconsMaterial.Warning
                };
            case 'skipped':
            case 'unknown':
            default:
                return {
                    color: '#2196f3',
                    icon: _iconsMaterial.Info
                };
        }
    };

    /**
     * Render test results summary
     */
    const renderTestSummary = () => {
        let _testResults$totalTes, _testResults$passedTe, _testResults$failedTe, _testResults$duration;
        if (!testResults) return null;
        const {
            color,
            icon: StatusIcon
        } = getStatusIndicator(testResults.success ? 'passed' : 'failed');
        return /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
            variant: 'default',
            elevation: 'medium',
            sx: {
                mb: 2,
                border: `1px solid ${color}30`
            }
        }, /*#__PURE__*/_react.default.createElement(_material.CardContent, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                alignItems: 'center',
                mb: 2
            }
        }, /*#__PURE__*/_react.default.createElement(StatusIcon, {
            sx: {
                color,
                mr: 1
            }
        }), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'h6',
            sx: {
                color
            }
        }, 'Test Results Summary')), /*#__PURE__*/_react.default.createElement(_material.Grid, {
            container: true,
            spacing: 2
        }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
            item: true,
            xs: 3
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            color: 'textSecondary'
        }, 'Total Tests'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'h4',
            color: 'primary'
        }, (_testResults$totalTes = testResults.totalTests) !== null && _testResults$totalTes !== void 0 ? _testResults$totalTes : 0)), /*#__PURE__*/_react.default.createElement(_material.Grid, {
            item: true,
            xs: 3
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            color: 'textSecondary'
        }, 'Passed'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'h4',
            sx: {
                color: '#4caf50'
            }
        }, (_testResults$passedTe = testResults.passedTests) !== null && _testResults$passedTe !== void 0 ? _testResults$passedTe : 0)), /*#__PURE__*/_react.default.createElement(_material.Grid, {
            item: true,
            xs: 3
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            color: 'textSecondary'
        }, 'Failed'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'h4',
            sx: {
                color: '#f44336'
            }
        }, (_testResults$failedTe = testResults.failedTests) !== null && _testResults$failedTe !== void 0 ? _testResults$failedTe : 0)), /*#__PURE__*/_react.default.createElement(_material.Grid, {
            item: true,
            xs: 3
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            color: 'textSecondary'
        }, 'Duration'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'h6',
            color: 'textSecondary'
        }, (_testResults$duration = testResults.duration) !== null && _testResults$duration !== void 0 ? _testResults$duration : 0, 'ms')))));
    };

    /**
     * Render category test results
     */
    const renderCategoryResults = () => {
        if (!(testResults !== null && testResults !== void 0 && testResults.categories)) return null;
        return Object.entries(testResults.categories).map(([categoryName, categoryData]) => {
            let _categoryData$failedT;
            const isExpanded = expandedSections[categoryName];
            const {
                color,
                icon: StatusIcon
            } = getStatusIndicator(categoryData.failedTests > 0 ? 'failed' : 'passed');
            return /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
                variant: 'default',
                elevation: 'low',
                key: categoryName,
                sx: {
                    mb: 2
                }
            }, /*#__PURE__*/_react.default.createElement(_material.CardContent, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
                sx: {
                    display: 'flex',
                    alignItems: 'center'
                }
            }, /*#__PURE__*/_react.default.createElement(StatusIcon, {
                sx: {
                    color,
                    mr: 2
                }
            }), /*#__PURE__*/_react.default.createElement(_material.Typography, {
                variant: 'h6',
                sx: {
                    flex: 1,
                    textTransform: 'capitalize',
                    cursor: 'pointer',
                    '&:hover': {
                        color: 'primary.main'
                    }
                },
                onClick: () => toggleSection(categoryName)
            }, categoryName, ' Tests'), /*#__PURE__*/_react.default.createElement(_material.Chip, {
                label: `${categoryData.passedTests}/${categoryData.totalTests}`,
                color: ((_categoryData$failedT = categoryData.failedTests) !== null && _categoryData$failedT !== void 0 ? _categoryData$failedT : 0) === 0 ? 'success' : 'error',
                size: 'small',
                sx: {
                    mr: 1
                }
            }), /*#__PURE__*/_react.default.createElement(_material.IconButton, {
                onClick: () => runCategoryTests(categoryName),
                disabled: isRunningTests,
                size: 'small',
                title: `Re-run ${categoryName} tests`
            }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Refresh, {
                fontSize: 'small'
            })), /*#__PURE__*/_react.default.createElement(_material.IconButton, {
                onClick: () => toggleSection(categoryName),
                size: 'small'
            }, isExpanded ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.ExpandLess, null) : /*#__PURE__*/_react.default.createElement(_iconsMaterial.ExpandMore, null))), /*#__PURE__*/_react.default.createElement(_material.Collapse, {
                in: isExpanded
            }, /*#__PURE__*/_react.default.createElement(_material.Box, {
                sx: {
                    mt: 2
                }
            }, /*#__PURE__*/_react.default.createElement(_material.List, {
                dense: true
            }, Object.entries(categoryData.tests || {}).map(([testName, testResult]) => {
                const {
                    color: testColor,
                    icon: TestIcon
                } = getStatusIndicator(testResult.status);
                return /*#__PURE__*/_react.default.createElement(_material.ListItem, {
                    key: testName,
                    sx: {
                        pl: 0
                    }
                }, /*#__PURE__*/_react.default.createElement(_material.ListItemIcon, null, /*#__PURE__*/_react.default.createElement(TestIcon, {
                    sx: {
                        color: testColor,
                        fontSize: '1.2rem'
                    }
                })), /*#__PURE__*/_react.default.createElement(_material.ListItemText, {
                    primary: /*#__PURE__*/_react.default.createElement(_material.Typography, {
                        variant: 'body2',
                        sx: {
                            color: testColor
                        }
                    }, testName),
                    secondary: /*#__PURE__*/_react.default.createElement(_material.Box, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
                        variant: 'caption',
                        color: 'textSecondary'
                    }, testResult.description), testResult.error && /*#__PURE__*/_react.default.createElement(_material.Typography, {
                        variant: 'caption',
                        sx: {
                            color: '#f44336',
                            display: 'block'
                        }
                    }, 'Error: ', testResult.error), /*#__PURE__*/_react.default.createElement(_material.Typography, {
                        variant: 'caption',
                        color: 'textSecondary'
                    }, 'Duration: ', testResult.duration, 'ms'))
                }));
            }))))));
        });
    };

    /**
     * Render performance metrics
     */
    const renderPerformanceMetrics = () => {
        if (!(performanceData !== null && performanceData !== void 0 && performanceData.categories)) return null;
        return /*#__PURE__*/_react.default.createElement(_material.Grid, {
            container: true,
            spacing: 2
        }, Object.entries(performanceData.categories).map(([category, data]) => {
            let _data$average, _data$min, _data$max;
            return /*#__PURE__*/_react.default.createElement(_material.Grid, {
                item: true,
                xs: 12,
                md: 6,
                key: category
            }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
                variant: 'default',
                elevation: 'medium',
                sx: {
                    border: '1px solid rgba(0, 234, 255, 0.3)'
                }
            }, /*#__PURE__*/_react.default.createElement(_material.CardContent, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
                variant: 'h6',
                sx: {
                    color: '#00eaff',
                    mb: 2,
                    textTransform: 'capitalize'
                }
            }, category, ' Metrics'), data.latest && /*#__PURE__*/_react.default.createElement(_material.Box, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
                variant: 'body2',
                color: 'textSecondary'
            }, 'Latest Value'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
                variant: 'h6',
                color: 'primary'
            }, typeof data.latest === 'object' && data.latest !== null ? /*#__PURE__*/_react.default.createElement('details', null, /*#__PURE__*/_react.default.createElement('summary', null, 'View Details'), /*#__PURE__*/_react.default.createElement('pre', {
                style: {
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-all',
                    margin: 0,
                    fontSize: 13
                }
            }, Object.entries(data.latest).map(([key, value]) => /*#__PURE__*/_react.default.createElement('div', {
                key: key
            }, /*#__PURE__*/_react.default.createElement('strong', null, key, ':'), ' ', String(value))))) : data.latest), /*#__PURE__*/_react.default.createElement(_material.Box, {
                sx: {
                    mt: 1
                }
            }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
                variant: 'caption',
                color: 'textSecondary'
            }, 'Avg: ', (_data$average = data.average) === null || _data$average === void 0 ? void 0 : _data$average.toFixed(2), ' | Min: ', (_data$min = data.min) === null || _data$min === void 0 ? void 0 : _data$min.toFixed(2), ' | Max: ', (_data$max = data.max) === null || _data$max === void 0 ? void 0 : _data$max.toFixed(2)))))));
        }));
    };

    /**
     * Render system status
     */
    const renderSystemStatus = () => {
        const {
            color,
            icon: StatusIcon
        } = getStatusIndicator(systemHealth);
        return /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
            variant: 'default',
            elevation: 'medium',
            sx: {
                mb: 2,
                border: `1px solid ${color}30`
            }
        }, /*#__PURE__*/_react.default.createElement(_material.CardContent, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                alignItems: 'center',
                mb: 2
            }
        }, /*#__PURE__*/_react.default.createElement(StatusIcon, {
            sx: {
                color,
                mr: 1
            }
        }), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'h6',
            sx: {
                color
            }
        }, 'System Status')), /*#__PURE__*/_react.default.createElement(_material.Grid, {
            container: true,
            spacing: 2
        }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
            item: true,
            xs: 4
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            color: 'textSecondary'
        }, 'Electron API'), /*#__PURE__*/_react.default.createElement(_material.Chip, {
            label: window['electronAPI'] ? 'Available' : 'Not Available',
            color: window['electronAPI'] ? 'success' : 'error',
            size: 'small'
        })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
            item: true,
            xs: 4
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            color: 'textSecondary'
        }, 'WebSocket'), /*#__PURE__*/_react.default.createElement(_material.Chip, {
            label: window.WebSocket ? 'Available' : 'Not Available',
            color: window.WebSocket ? 'success' : 'error',
            size: 'small'
        })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
            item: true,
            xs: 4
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            color: 'textSecondary'
        }, 'Performance API'), /*#__PURE__*/_react.default.createElement(_material.Chip, {
            label: window.performance ? 'Available' : 'Not Available',
            color: window.performance ? 'success' : 'error',
            size: 'small'
        })))));
    };
    return /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        exit: {
            opacity: 0,
            y: -20
        },
        transition: {
            duration: 0.3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 3,
            minHeight: '80vh'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        sx: {
            background: 'linear-gradient(45deg, #00eaff 30%, #a259ff 70%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 800
        }
    }, 'System Diagnostics'), /*#__PURE__*/_react.default.createElement(_material.Button, {
        variant: 'outlined',
        onClick: onClose,
        sx: {
            borderColor: '#00eaff',
            color: '#00eaff'
        }
    }, 'Close')), /*#__PURE__*/_react.default.createElement(_material.Tabs, {
        value: activeTab,
        onChange: (e, newValue) => setActiveTab(newValue),
        sx: {
            mb: 3,
            borderBottom: '1px solid rgba(255,255,255,0.1)'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Tab, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Api, null),
        label: 'API Tests'
    }), /*#__PURE__*/_react.default.createElement(_material.Tab, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Speed, null),
        label: 'Performance'
    }), /*#__PURE__*/_react.default.createElement(_material.Tab, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.NetworkCheck, null),
        label: 'System Status'
    }), /*#__PURE__*/_react.default.createElement(_material.Tab, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.BugReport, null),
        label: 'Diagnostics'
    })), /*#__PURE__*/_react.default.createElement(_framerMotion.AnimatePresence, {
        mode: 'wait'
    }, activeTab === 0 && /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        key: 'api-tests',
        initial: {
            opacity: 0,
            x: -20
        },
        animate: {
            opacity: 1,
            x: 0
        },
        exit: {
            opacity: 0,
            x: 20
        },
        transition: {
            duration: 0.3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 3,
            display: 'flex',
            gap: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Button, {
        variant: 'contained',
        startIcon: isRunningTests ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.Stop, null) : /*#__PURE__*/_react.default.createElement(_iconsMaterial.PlayArrow, null),
        onClick: () => runComprehensiveTests(),
        disabled: isRunningTests,
        sx: {
            background: 'linear-gradient(45deg, #00eaff, #a259ff)',
            '&:hover': {
                background: 'linear-gradient(45deg, #a259ff, #00eaff)'
            }
        }
    }, isRunningTests ? 'Running Tests...' : 'Run All Tests'), /*#__PURE__*/_react.default.createElement(_material.Button, {
        variant: 'outlined',
        startIcon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Refresh, null),
        onClick: loadPerformanceData,
        sx: {
            borderColor: '#00eaff',
            color: '#00eaff'
        }
    }, 'Refresh Data')), isRunningTests && /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        sx: {
            backgroundColor: 'rgba(0, 234, 255, 0.2)',
            '& .MuiLinearProgress-bar': {
                backgroundColor: '#00eaff'
            }
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        color: 'textSecondary',
        sx: {
            mt: 1
        }
    }, 'Running comprehensive API tests...')), renderTestSummary(), renderCategoryResults()), activeTab === 1 && /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        key: 'performance',
        initial: {
            opacity: 0,
            x: -20
        },
        animate: {
            opacity: 1,
            x: 0
        },
        exit: {
            opacity: 0,
            x: 20
        },
        transition: {
            duration: 0.3
        }
    }, renderPerformanceMetrics()), activeTab === 2 && /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        key: 'system-status',
        initial: {
            opacity: 0,
            x: -20
        },
        animate: {
            opacity: 1,
            x: 0
        },
        exit: {
            opacity: 0,
            x: 20
        },
        transition: {
            duration: 0.3
        }
    }, renderSystemStatus()), activeTab === 3 && /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        key: 'diagnostics',
        initial: {
            opacity: 0,
            x: -20
        },
        animate: {
            opacity: 1,
            x: 0
        },
        exit: {
            opacity: 0,
            x: 20
        },
        transition: {
            duration: 0.3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Alert, {
        severity: 'info',
        sx: {
            mb: 2
        }
    }, 'Advanced diagnostics and troubleshooting tools')))));
}

SystemDiagnostics.propTypes = {
    onClose: _propTypes.default.func.isRequired
};