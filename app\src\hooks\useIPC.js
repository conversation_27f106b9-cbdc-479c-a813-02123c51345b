import {useCallback, useEffect, useState} from 'react';
import ipcService from '../services/ipcService';

/**
 * Custom hook for IPC communication in React components
 * Provides a simple interface for making IPC calls with loading and error states
 */
export const useIPC = () => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    /**
     * Execute an IPC call with loading and error handling
     */
    const callIPC = useCallback(async (methodName, ...args) => {
        setLoading(true);
        setError(null);
        try {
            const result = await ipcService[methodName](...args);
            if (!result.success) {
                throw new Error(result.error || 'IPC call failed');
            }
            return result.data;
        } catch (err) {
            setError(err.message);
            throw err;
        } finally {
            setLoading(false);
        }
    }, []);

    /**
     * Reset error state
     */
    const clearError = useCallback(() => {
        setError(null);
    }, []);

    return {
        callIPC,
        loading,
        error,
        clearError,
        isElectron
    };
};

/**
 * Hook for fetching data from IPC
 */
export const useIPCData = (methodName, ...args) => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const fetchData = useCallback(async () => {
        try {
            setLoading(true);
            const result = await ipcService[methodName](...args);
            if (result.success) {
                setData(result.data);
            } else {
                setError(result.error || 'Failed to fetch data');
            }
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [methodName, args]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    return {data, loading, error, refetch};
};

/**
 * Hook for bot status
 */
export const useBotStatus = () => useIPCData('getBotStatus');

/**
 * Hook for portfolio data
 */
export const usePortfolio = () => useIPCData('getPortfolioSummary');

/**
 * Hook for market data
 */
export const useMarketData = (symbol) => useIPCData('getMarketData', symbol);

/**
 * Hook for trading stats
 */
export const useTradingStats = () => useIPCData('getTradingStats');

/**
 * Hook for whale signals
 */
export const useWhaleSignals = () => useIPCData('getWhaleSignals');

/**
 * Hook for open orders
 */
export const useOpenOrders = () => useIPCData('getOpenOrders');

/**
 * Hook for coins
 */
export const useCoins = () => useIPCData('getCoins');

/**
 * Hook for settings
 */
export const useSettings = () => useIPCData('getSettings');

/**
 * Hook for exchanges
 */
export const useExchanges = () => useIPCData('getExchanges');

/**
 * Hook for arbitrage opportunities
 */
export const useArbitrageOpportunities = () => useIPCData('getArbitrageOpportunities');

/**
 * Hook for meme coin opportunities
 */
export const useMemeCoinOpportunities = () => useIPCData('getMemeCoinOpportunities');

/**
 * Hook for grid positions
 */
export const useGridPositions = () => useIPCData('getGridPositions');

/**
 * Hook for DCA positions
 */
export const useDCAPositions = () => useIPCData('getDCAPositions');

/**
 * Hook for cross-exchange balances
 */
export const useCrossExchangeBalances = () => useIPCData('getCrossExchangeBalances');

/**
 * Hook for performance metrics
 */
export const usePerformanceMetrics = () => useIPCData('getPerformanceMetrics');

/**
 * Hook for risk metrics
 */
export const useRiskMetrics = () => useIPCData('getRiskMetrics');
