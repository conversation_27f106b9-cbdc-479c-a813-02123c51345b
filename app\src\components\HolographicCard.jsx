import React, {useRef, useState} from 'react';
import PropTypes from 'prop-types';
import {Box, Card} from '@mui/material';
import {motion, useMotionValue, useSpring, useTransform} from 'framer-motion';

/**
 * Advanced Holographic Card Component
 * Features: 3D perspective, holographic effects, magnetic hover, glass morphism
 */
export default function HolographicCard({
                                            children,
                                            variant = 'default',
                                            elevation = 'medium',
                                            interactive = true,
                                            glowColor = '#00eaff',
                                            sx = {},
                                            ...props
                                        }) {
    const [isHovered, setIsHovered] = useState(false);
    const cardRef = useRef(null);

    // Motion values for 3D effect
    const x = useMotionValue(0);
    const y = useMotionValue(0);

    // Spring animations for smooth movement
    const rotateX = useSpring(useTransform(y, [-0.5, 0.5], [2, -2]));
    const rotateY = useSpring(useTransform(x, [-0.5, 0.5], [-2, 2]));

    // Handle mouse movement for 3D effect
    const handleMouseMove = (event) => {
        if (!cardRef.current || !interactive) return;

        const rect = cardRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const rotateXValue = (event.clientY - centerY) / (rect.height / 2);
        const rotateYValue = (event.clientX - centerX) / (rect.width / 2);

        x.set(rotateXValue);
        y.set(rotateYValue);
    };

    /**
     * Handle mouse leaving card area.
     * Resets the 3D rotation effect and hover state.
     */

    /**
     * Handle mouse leaving card area.
     * Resets the 3D rotation effect and hover state.
     */
    const handleMouseLeave = () => {
        setIsHovered(false);
        x.set(0);
        y.set(0);
    };

    /**
     * Handle mouse entering card area.
     * Sets the hover state to true.
     */
    const handleMouseEnter = () => {
        setIsHovered(true);
    };

    // Variant configurations
    const getVariantStyles = () => {
        const variants = {
            default: {
                background: 'rgba(24, 26, 32, 0.85)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                glowColor: '#00eaff'
            },
            premium: {
                background: 'rgba(162, 89, 255, 0.1)',
                border: '1px solid rgba(162, 89, 255, 0.3)',
                glowColor: '#a259ff'
            },
            success: {
                background: 'rgba(76, 175, 80, 0.1)',
                border: '1px solid rgba(76, 175, 80, 0.3)',
                glowColor: '#4caf50'
            },
            warning: {
                background: 'rgba(255, 193, 7, 0.1)',
                border: '1px solid rgba(255, 193, 7, 0.3)',
                glowColor: '#ffc107'
            },
            error: {
                background: 'rgba(244, 67, 54, 0.1)',
                border: '1px solid rgba(244, 67, 54, 0.3)',
                glowColor: '#f44336'
            },
            quantum: {
                background: 'rgba(0, 234, 255, 0.05)',
                border: '1px solid rgba(0, 234, 255, 0.2)',
                glowColor: '#00eaff'
            },
            glass: {
                background: 'rgba(255, 255, 255, 0.05)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                glowColor: '#ffffff'
            }
        };

        return variants[variant] || variants.default;
    };

    // Elevation configurations
    const getElevationStyles = () => {
        const elevations = {
            low: {
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                hoverShadow: '0 8px 25px rgba(0, 0, 0, 0.25)'
            },
            medium: {
                boxShadow: '0 8px 25px rgba(0, 0, 0, 0.25)',
                hoverShadow: '0 15px 40px rgba(0, 0, 0, 0.35)'
            },
            high: {
                boxShadow: '0 15px 40px rgba(0, 0, 0, 0.35)',
                hoverShadow: '0 25px 60px rgba(0, 0, 0, 0.45)'
            }
        };

        return elevations[elevation] || elevations.medium;
    };

    const variantStyles = getVariantStyles();
    const elevationStyles = getElevationStyles();
    const cardGlowColor = glowColor || variantStyles.glowColor;

    return (
        <motion.div
            ref={cardRef}
            style={{
                perspective: 1000,
                transformStyle: 'preserve-3d'
            }}
            onMouseMove={handleMouseMove}
            onMouseLeave={handleMouseLeave}
            onMouseEnter={handleMouseEnter}
            whileHover={{scale: interactive ? 1.005 : 1}}
            transition={{type: 'spring', stiffness: 300, damping: 30}}
        >
            <motion.div
                style={{
                    rotateX: interactive ? rotateX : 0,
                    rotateY: interactive ? rotateY : 0,
                    transformStyle: 'preserve-3d'
                }}
            >
                <Card
                    {...props}
                    sx={{
                        position: 'relative',
                        overflow: 'hidden',
                        borderRadius: '20px',
                        backdropFilter: 'blur(20px) saturate(180%)',
                        WebkitBackdropFilter: 'blur(20px) saturate(180%)',
                        border: variantStyles.border,
                        background: variantStyles.background,
                        boxShadow: elevationStyles.boxShadow,
                        transition: 'all 0.4s cubic-bezier(0.23, 1, 0.32, 1)',

                        // Hover effects
                        ...(isHovered && interactive && {
                            boxShadow: `
                                ${elevationStyles.hoverShadow},
                                0 0 30px ${cardGlowColor}40,
                                inset 0 1px 0 rgba(255, 255, 255, 0.2)
                            `,
                            border: `1px solid ${cardGlowColor}60`
                        }),

                        // Holographic shine effect
                        '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: '-100%',
                            width: '100%',
                            height: '100%',
                            background: `linear-gradient(90deg,
                                transparent,
                                rgba(255, 255, 255, 0.1),
                                transparent
                            )`,
                            transition: 'left 0.6s ease',
                            zIndex: 1,
                            pointerEvents: 'none'
                        },

                        ...(isHovered && interactive && {
                            '&::before': {
                                left: '100%'
                            }
                        }),

                        // Quantum field effect
                        '&::after': {
                            content: '""',
                            position: 'absolute',
                            top: '-2px',
                            left: '-2px',
                            right: '-2px',
                            bottom: '-2px',
                            background: `conic-gradient(from 0deg,
                                ${cardGlowColor}00,
                                ${cardGlowColor}40,
                                ${cardGlowColor}00,
                                ${cardGlowColor}40,
                                ${cardGlowColor}00
                            )`,
                            borderRadius: '22px',
                            opacity: isHovered && interactive ? 0.6 : 0,
                            animation: isHovered && interactive ? 'quantumRotation 4s linear infinite' : 'none',
                            zIndex: -1,
                            filter: 'blur(6px)',
                            transition: 'opacity 0.3s ease'
                        },

                        ...sx
                    }}
                >
                    {/* Content wrapper */}
                    <Box sx={{position: 'relative', zIndex: 2}}>
                        {children}
                    </Box>

                    {/* Inner glow effect */}
                    {variant === 'quantum' && (
                        <Box
                            sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                background: `radial-gradient(circle at center,
                                    ${cardGlowColor}10 0%,
                                    transparent 50%
                                )`,
                                opacity: isHovered ? 0.8 : 0.3,
                                transition: 'opacity 0.3s ease',
                                pointerEvents: 'none',
                                zIndex: 0
                            }}
                        />
                    )}

                    {/* Neural network pattern overlay for premium variant */}
                    {variant === 'premium' && (
                        <Box
                            sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                backgroundImage: `
                                    radial-gradient(circle at 25% 25%, ${cardGlowColor}20 2px, transparent 2px),
                                    radial-gradient(circle at 75% 75%, ${cardGlowColor}15 1px, transparent 1px),
                                    radial-gradient(circle at 50% 50%, ${cardGlowColor}10 1px, transparent 1px)
                                `,
                                backgroundSize: '60px 60px, 40px 40px, 80px 80px',
                                opacity: isHovered ? 0.6 : 0.2,
                                transition: 'opacity 0.3s ease',
                                pointerEvents: 'none',
                                zIndex: 0
                            }}
                        />
                    )}

                    {/* Crystalline structure for high elevation */}
                    {elevation === 'high' && (
                        <Box
                            sx={{
                                position: 'absolute',
                                top: '10px',
                                right: '10px',
                                width: '20px',
                                height: '20px',
                                background: `linear-gradient(45deg,
                                    ${cardGlowColor}40,
                                    transparent,
                                    ${cardGlowColor}20
                                )`,
                                clipPath: 'polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%)',
                                opacity: isHovered ? 1 : 0.5,
                                transition: 'opacity 0.3s ease',
                                animation: 'crystalGlow 3s ease-in-out infinite',
                                pointerEvents: 'none',
                                zIndex: 1
                            }}
                        />
                    )}
                </Card>
            </motion.div>
        </motion.div>
    );
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes quantumRotation {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes crystalGlow {
        0%, 100% {
            filter: brightness(1) contrast(1);
            transform: scale(1);
        }
        50% {
            filter: brightness(1.1) contrast(1.1);
            transform: scale(1.02);
        }
    }
`;
document.head.appendChild(style);

HolographicCard.propTypes = {
    children: PropTypes.node.isRequired,
    variant: PropTypes.oneOf(['default', 'premium', 'success', 'warning', 'error', 'quantum', 'glass']),
    elevation: PropTypes.oneOf(['low', 'medium', 'high']),
    interactive: PropTypes.bool,
    glowColor: PropTypes.string,
    sx: PropTypes.object
};
