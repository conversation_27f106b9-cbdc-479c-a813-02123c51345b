// Import logger for consistent logging
import logger from '../utils/logger';

import React, {useCallback, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Chip,
  CircularProgress,
  Grid2 as Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography
} from '@mui/material';
import {Assessment, Timeline, TrendingDown} from '@mui/icons-material';
import HolographicCard from './HolographicCard';

const customWindow = window;
const electronAPI = customWindow.electronAPI || {
    getDrawdownAnalysis: () => Promise.resolve({success: true, data: {}}),
    getPerformanceHistory: (_timeRange) => Promise.resolve({success: true, data: []})
};

const DrawdownAnalyzer = () => {
    const [analysis, setAnalysis] = useState({
        maxDrawdown: 0,
        currentDrawdown: 0,
        recoveryTime: 'N/A',
        valueAtRisk: 0,
        sharpeRatio: 'N/A',
        sortinoRatio: 'N/A',
        beta: 'N/A',
        alpha: 'N/A'
    });
    const [history, setHistory] = useState([]);
    const [loading, setLoading] = useState(true);

    const fetchData = useCallback(async () => {
        try {
            const [analysisData, performanceHistory] = await Promise.all([
                electronAPI.getDrawdownAnalysis(),
                electronAPI.getPerformanceHistory('30d')]);

            if (analysisData?.success) {
                setAnalysis(prev => ({...prev, ...analysisData.data}));
            }
            if (performanceHistory?.success) {
                setHistory(performanceHistory.data || []);
            }
        } catch (error) {
            logger.error('Failed to fetch drawdown analysis:', error);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
        return () => clearInterval(interval);
    }, [fetchData]);

    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(value || 0);
    };

    if (loading) {
        return (
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh'}}>
                <CircularProgress/>
            </Box>
        );
    }

    return (
        <Box sx={{p: 3}}>
            <Typography variant="h4" sx={{color: '#f44336', fontWeight: 800, mb: 3}}>
                <TrendingDown sx={{mr: 2}}/>
                Drawdown Analyzer
            </Typography>

            <Grid container spacing={3}>
                {/* Drawdown Summary */}
                <Grid size={{xs: 12, md: 6}}>
                    <HolographicCard variant="error" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#f44336', mb: 2}}>
                            <TrendingDown sx={{mr: 1}}/>
                            Drawdown Summary
                        </Typography>
                        <Grid container spacing={2}>
                            <Grid size={{xs: 12, md: 6}}>
                                <Typography variant="h3" sx={{color: '#f44336', fontWeight: 800}}>
                                    {analysis?.maxDrawdown || 0}%
                                </Typography>
                                <Typography variant="body2" sx={{color: '#888'}}>
                                    Maximum Drawdown
                                </Typography>
                            </Grid>
                            <Grid size={{xs: 12, md: 6}}>
                                <Typography variant="h3" sx={{color: '#ff9800', fontWeight: 800}}>
                                    {analysis?.currentDrawdown || 0}%
                                </Typography>
                                <Typography variant="body2" sx={{color: '#888'}}>
                                    Current Drawdown
                                </Typography>
                            </Grid>
                            <Grid size={{xs: 12, md: 6}}>
                                <Typography variant="h3" sx={{color: '#4caf50', fontWeight: 800}}>
                                    {analysis?.recoveryTime || 'N/A'}
                                </Typography>
                                <Typography variant="body2" sx={{color: '#888'}}>
                                    Recovery Time
                                </Typography>
                            </Grid>
                            <Grid size={{xs: 12, md: 6}}>
                                <Typography variant="h3" sx={{color: '#00eaff', fontWeight: 800}}>
                                    {formatCurrency(analysis?.valueAtRisk || 0)}
                                </Typography>
                                <Typography variant="body2" sx={{color: '#888'}}>
                                    Value at Risk (VaR)
                                </Typography>
                            </Grid>
                        </Grid>
                    </HolographicCard>
                </Grid>

                {/* Drawdown History */}
                <Grid size={{xs: 12, md: 6}}>
                    <HolographicCard variant="secondary" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#a259ff', mb: 2}}>
                            <Timeline sx={{mr: 1}}/>
                            Drawdown History
                        </Typography>
                        <TableContainer>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell sx={{color: '#a259ff'}}>Date</TableCell>
                                        <TableCell sx={{color: '#a259ff'}}>Drawdown</TableCell>
                                        <TableCell sx={{color: '#a259ff'}}>Peak</TableCell>
                                        <TableCell sx={{color: '#a259ff'}}>Trough</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {history.map((item, index) => (
                                        <TableRow key={index}>
                                            <TableCell sx={{color: '#fff'}}>
                                                {new Date(item?.date || Date.now()).toLocaleDateString()}
                                            </TableCell>
                                            <TableCell>
                                                <Chip
                                                    label={`${item?.drawdown || 0}%`}
                                                    size="small"
                                                    sx={{
                                                        backgroundColor: (item?.drawdown || 0) > 10 ? '#f4433620' : '#ff980020',
                                                        color: (item?.drawdown || 0) > 10 ? '#f44336' : '#ff9800'
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell sx={{color: '#fff'}}>
                                                {formatCurrency(item?.peak || 0)}
                                            </TableCell>
                                            <TableCell sx={{color: '#fff'}}>
                                                {formatCurrency(item?.trough || 0)}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </HolographicCard>
                </Grid>

                {/* Risk Metrics */}
                <Grid size={{xs: 12}}>
                    <HolographicCard variant="gold" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#ffc107', mb: 2}}>
                            <Assessment sx={{mr: 1}}/>
                            Detailed Risk Metrics
                        </Typography>
                        <Grid container spacing={3}>
                            <Grid size={{xs: 12, md: 3}}>
                                <Typography variant="h5" sx={{color: '#ffc107', fontWeight: 800}}>
                                    Sharpe Ratio
                                </Typography>
                                <Typography variant="h4" sx={{color: '#fff'}}>
                                    {analysis?.sharpeRatio || 'N/A'}
                                </Typography>
                            </Grid>
                            <Grid size={{xs: 12, md: 3}}>
                                <Typography variant="h5" sx={{color: '#ffc107', fontWeight: 800}}>
                                    Sortino Ratio
                                </Typography>
                                <Typography variant="h4" sx={{color: '#fff'}}>
                                    {analysis?.sortinoRatio || 'N/A'}
                                </Typography>
                            </Grid>
                            <Grid size={{xs: 12, md: 3}}>
                                <Typography variant="h5" sx={{color: '#ffc107', fontWeight: 800}}>
                                    Beta
                                </Typography>
                                <Typography variant="h4" sx={{color: '#fff'}}>
                                    {analysis?.beta || 'N/A'}
                                </Typography>
                            </Grid>
                            <Grid size={{xs: 12, md: 3}}>
                                <Typography variant="h5" sx={{color: '#ffc107', fontWeight: 800}}>
                                    Alpha
                                </Typography>
                                <Typography variant="h4" sx={{color: '#fff'}}>
                                    {analysis?.alpha || 'N/A'}
                                </Typography>
                            </Grid>
                        </Grid>
                    </HolographicCard>
                </Grid>
            </Grid>
        </Box>
    );
};

DrawdownAnalyzer.propTypes = {
    analysis: PropTypes.object,
    history: PropTypes.array
};

export default DrawdownAnalyzer;
