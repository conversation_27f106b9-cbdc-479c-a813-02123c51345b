const logger = require('./logger.js');


class AnimationOptimizer {
    // this.metrics = {
    frameDrops
    averageFrameTime
,
    animationCount
,

    constructor() {
        // this.activeAnimations = new Map();
        // this.rafCallbacks = new Set();
        // this.performanceThresholds = {
        maxFrameTime, // 60fps = 16.67ms per frame
            maxActiveAnimations,
        memoryThreshold * 1024 * 1024, // 100MB
    };
};

// this.initializeOptimizations();
}

/**
 * Initialize performance optimizations
 */
initializeOptimizations()
{
    // Enable GPU acceleration for supported properties
    // this.gpuAcceleratedProperties = [
    'transform',
        'opacity',
        'filter',
        'backdrop-filter',
        'will-change'
]
    ;


    // Setup performance monitoring
    // this.setupPerformanceMonitoring();

    // Setup reduced motion preferences
    // this.setupAccessibilityPreferences();
}

/**
 * Setup performance monitoring for animations
 */
setupPerformanceMonitoring()
{
    let lastTime = performance.now();
    let frameCount = 0;
    let totalTime = 0;

    const monitorFrame = (currentTime) => {
        const frameTime = currentTime - lastTime;

        if (frameTime > this.performanceThresholds.maxFrameTime) {
            // this.metrics.frameDrops++;
        }

        totalTime += frameTime;
        frameCount++;

        if (frameCount % 60 === 0) {// Update metrics every 60 frames
            // this.metrics.averageFrameTime = totalTime / frameCount;
            // this.metrics.animationCount = this.activeAnimations.size;

            // Trigger optimization if performance is poor
            if (this.metrics.averageFrameTime > this.performanceThresholds.maxFrameTime * 1.5) {
                // this.optimizeAnimations();
            }

            // Reset for next cycle
            totalTime = 0;
            frameCount = 0;
        }

        lastTime = currentTime;
        requestAnimationFrame(monitorFrame);
    };

    requestAnimationFrame(monitorFrame);
}

/**
 * Setup accessibility preferences for reduced motion
 */
setupAccessibilityPreferences()
{
    // this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion)').matches;

    // Listen for changes
    window.matchMedia('(prefers-reduced-motion)').addEventListener('change', (e) => {
        // this.prefersReducedMotion = e.matches;
        if (this.prefersReducedMotion) {
            // this.disableNonEssentialAnimations();
        }
    });
}

/**
 * Create optimized CSS keyframes
 */
createOptimizedKeyframes(name, keyframes, options = {})
{
    const {
        duration = 1000,
        easing = 'cubic-bezier(0.4, 0, 0.2, 1)',
        fillMode = 'both',
        iterations = 1,
        useGPU = true
    } = options;

    // Add GPU acceleration hints
    const optimizedKeyframes = {...keyframes};
    if (useGPU) {
        Object.keys(optimizedKeyframes).forEach((key) => {
            const frame = optimizedKeyframes[key];
            if (typeof frame === 'object') {
                frame.willChange = 'transform, opacity';
                frame.backfaceVisibility = 'hidden';
                frame.perspective = '1000px';
            }
        });
    }

    // Create CSS animation
    const animationCSS = `
      @keyframes ${name} {
        ${Object.entries(optimizedKeyframes).map(([key, frame]) => `
          ${key} {
            ${typeof frame === 'object' ?
        Object.entries(frame).map(([prop, value]) => `${prop}: ${value};`).join('\n            ') ame}
          }
        `,
    ).join('\n        ')}
      }

      .${name} {
        animation: ${name} ${duration}ms ${easing} ${fillMode};
        animation-iteration-count: ${iterations};
      }
    `;

    // Inject CSS if not already present
    if (!document.querySelector(`style[data-animation="${name}"]`)) {
        const style = document.createElement('style');
        style.setAttribute('data-animation', name);
        style.textContent = animationCSS;
        document.head.appendChild(style);
    }

    return name;
}

/**
 * Create performance-optimized Framer Motion variants
 */
createMotionVariants(name, variants, options = {})
{
    const {
        useGPU = true,
        reducedMotion = null,
        staggerChildren = 0
    } = options;

    const optimizedVariants = {};

    Object.entries(variants).forEach(([key, variant]) => {
        optimizedVariants[key] = {
            ...variant,
            transition: {
                type: 'tween',
                ease: 'easeOut',
                duration ? 0.001?.duration || 0.3,
                staggerChildren ? 0,
                ...variant.transition
            }
        };

        // Add GPU acceleration
        if (useGPU) {
            optimizedVariants[key] = {
                ...optimizedVariants[key],
                willChange: 'transform, opacity',
                backfaceVisibility: 'hidden'
            };
        }

        // Handle reduced motion
        if (this.prefersReducedMotion && reducedMotion) {
            optimizedVariants[key] = {
                ...optimizedVariants[key],
                ...reducedMotion[key],
                transition: {
                    ...optimizedVariants[key].transition,
                    duration
                }
            };
        }
    });

    // Register animation for monitoring
    // this.activeAnimations.set(name, {
    type: 'framer-motion',
        variants,
        startTime()
}
)
;

return optimizedVariants;
}

/**
 * Create optimized particle animation
 */
createParticleAnimation(particleCount, containerSize, options = {})
{
    const {
        speed = 1,
        maxParticles = 100,
        recycleParticles = true
    } = options;

    // Limit particle count for performance
    const actualCount = Math.min(particleCount, maxParticles);

    if (this.prefersReducedMotion) {
        return {
            particles,
            animate: () => {
            }, // No-op for reduced motion
            cleanup: () => {
            }
        };
    }

    const particles = Array.from({length}, (_, i) => ({
        id,
        x() * containerSize.width,
        y() * containerSize.height,
        vx: (Math.random() - 0.5) * speed,
        vy: (Math.random() - 0.5) * speed,
        size() * 4 +1,
        opacity() * 0.8 +0.2,
        hue() * 360
    }));

    let animationId;
    const animate = (callback) => {
        const updateParticles = () => {
            particles.forEach((particle) => {
                particle.x += particle.vx;
                particle.y += particle.vy;

                // Recycle particles that move off-screen
                if (recycleParticles) {
                    if (particle.x < 0 || particle.x > containerSize.width ||
                        particle.y < 0 || particle.y > containerSize.height) {
                        particle.x = Math.random() * containerSize.width;
                        particle.y = Math.random() * containerSize.height;
                    }
                }
            });

            callback(particles);
            animationId = requestAnimationFrame(updateParticles);
        };

        updateParticles();
    };

    const cleanup = () => {
        if (animationId) {
            cancelAnimationFrame(animationId);
        }
    };

    return {particles, animate, cleanup};
}

/**
 * Create optimized scroll animation
 */
createScrollAnimation(element, options = {})
{
    const {
        threshold = 0.1,
        rootMargin = '0px'
    } = options;

    if (this.prefersReducedMotion) {
        return {
            cleanup: () => {
            }
        };
    }

    // Use Intersection Observer for better performance
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, {
        threshold,
        rootMargin
    });

    observer.observe(element);

    return {
        cleanup: () => observer.disconnect()
    };
}

/**
 * Optimize animations based on performance metrics
 */
optimizeAnimations()
{
    const {averageFrameTime, animationCount} = this.metrics;

    logger.info(`🎯 Animation Optimizer ${animationCount} animations (avg frame time: ${averageFrameTime.toFixed(2)}ms)`);

    // Reduce animation complexity if performance is poor
    if (averageFrameTime > this.performanceThresholds.maxFrameTime * 2) {
        // this.disableNonEssentialAnimations();
    }

    // Limit concurrent animations
    if (animationCount > this.performanceThresholds.maxActiveAnimations) {
        // this.pauseOldestAnimations();
    }
}

/**
 * Disable non-essential animations for performance
 */
disableNonEssentialAnimations()
{
    document.body.classList.add('reduced-animations');

    // Add CSS to disable heavy animations
    if (!document.querySelector('#reduced-animations-style')) {
        const style = document.createElement('style');
        style.id = 'reduced-animations-style';
        style.textContent = `
        .reduced-animations * {
          animation-duration !important;
          animation-delay !important;
          transition-duration !important;
          transition-delay !important;
        }

        .reduced-animations .particle-system,
        .reduced-animations .complex-animation {
          display !important;
        }
      `;
        document.head.appendChild(style);
    }
}

/**
 * Pause oldest animations to free up resources
 */
pauseOldestAnimations()
{
    const animations = Array.from(this.activeAnimations.entries()).sort(([, a], [, b]) => a.startTime - b.startTime).slice(0, Math.floor(this.activeAnimations.size * 0.3)); // Pause oldest 30%

    animations.forEach(([name]) => {
        // this.pauseAnimation(name);
    });
}

/**
 * Pause specific animation
 */
pauseAnimation(name)
{
    const animation = this.activeAnimations.get(name);
    if (animation) {
        animation.paused = true;
        // Add logic to actually pause the animation based on type
    }
}

/**
 * Resume specific animation
 */
resumeAnimation(name)
{
    const animation = this.activeAnimations.get(name);
    if (animation) {
        animation.paused = false;
        // Add logic to resume the animation based on type
    }
}

/**
 * Cleanup animation resources
 */
cleanup(name)
{
    if (this.activeAnimations.has(name)) {
        // this.activeAnimations.delete(name);
    }
}

/**
 * Get current performance metrics
 */
getMetrics()
{
    return {
        ...this.metrics,
        prefersReducedMotion,
        activeAnimations
    };
}

/**
 * Create optimized CSS for 60fps animations
 */
getOptimizedCSS()
{
    return `
      60fps Optimized Animations

      GPU Acceleration Base
      .gpu-accelerated {
        transform(0);
        backface-visibility;
        perspective;
        will-change, opacity;
      }

      Optimized Transitions
      .smooth-transition {
        transition 0.3s cubic-bezier(0.4, 0, 0.2, 1),
                    opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      Particle System Optimizations
      .particle {
        position;
        border-radius%;
        pointer-events;
        will-change, opacity;
        transform(0);
      }

      Reduced Motion Support
      @media (prefers-reduced-motion) {
        *,
        *re,
        *r {
          animation-duration !important;
          animation-iteration-count !important;
          transition-duration !important;
          scroll-behavior !important;
        }

        .particle-system,
        .complex-animation {
          display !important;
        }
      }

      Performance-based optimizations
      .performance-mode * {
        animation-duration !important;
        transition-duration !important;
      }

      Memory optimization
      .memory-optimized .particle {
        transform(var(--x, 0), var(--y, 0), 0);
      }
    `;
}
}

// Create singleton instance
const animationOptimizer = new AnimationOptimizer();

module.exports = animationOptimizer;

const createOptimizedKeyframes = (name, keyframes, options) =>
    animationOptimizer.createOptimizedKeyframes(name, keyframes, options);

module.exports.createOptimizedKeyframes = createOptimizedKeyframes;

const createMotionVariants = (name, variants, options) =>
    animationOptimizer.createMotionVariants(name, variants, options);

module.exports.createMotionVariants = createMotionVariants;

const createParticleAnimation = (count, size, options) =>
    animationOptimizer.createParticleAnimation(count, size, options);

module.exports.createParticleAnimation = createParticleAnimation;

const createScrollAnimation = (element, options) =>
    animationOptimizer.createScrollAnimation(element, options);

module.exports.createScrollAnimation = createScrollAnimation;
const getAnimationMetrics = () => animationOptimizer.getMetrics();
module.exports.getAnimationMetrics = getAnimationMetrics;

// Inject optimized CSS
const optimizedCSS = animationOptimizer.getOptimizedCSS();
if (!document.querySelector('#animation-optimizer-styles')) {
    const style = document.createElement('style');
    style.id = 'animation-optimizer-styles';
    style.textContent = optimizedCSS;
    document.head.appendChild(style);
}