/**
 * Test file for AutonomousDashboard IPC integration
 * Tests the Start button IPC communication functionality
 */

import React from 'react';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import AutonomousDashboard from '../AutonomousDashboard';

// Mock the IPC service
jest.mock('../../services/ipcService', () => {
    return jest.fn().mockImplementation(() => ({
        getTradingStats: jest.fn().mockResolvedValue({success: true, data: {}}),
        getWhaleSignals: jest.fn().mockResolvedValue({success: true, data: []}),
        getMemeCoinOpportunities: jest.fn().mockResolvedValue({success: true, data: []}),
        getPerformanceMetrics: jest.fn().mockResolvedValue({success: true, data: {}}),
        getLogs: jest.fn().mockResolvedValue({success: true, data: []}),
        quickIPCCall: jest.fn().mockResolvedValue({success: true, data: {isRunning: false}}),
        criticalIPCCall: jest.fn().mockResolvedValue({success: true, data: {message: 'Success'}}),
        tradingIPCCall: jest.fn().mockResolvedValue({success: true, data: {message: 'Success'}})
    }));
});

// Mock window.electronAPI
const mockElectronAPI = {
    getRealTimeStatus: jest.fn().mockResolvedValue({success: true, data: {isRunning: false}}),
    getActiveBots: jest.fn().mockResolvedValue({success: true, data: []}),
    initializeTrading: jest.fn().mockResolvedValue({success: true, data: {message: 'Initialized'}}),
    startBot: jest.fn().mockResolvedValue({success: true, data: {message: 'Started'}}),
    stopBot: jest.fn().mockResolvedValue({success: true, data: {message: 'Stopped'}}),
    stopGrid: jest.fn().mockResolvedValue({success: true, data: {message: 'Grid stopped'}}),
    healthCheck: jest.fn().mockResolvedValue({success: true, data: {status: 'healthy'}})
};

Object.defineProperty(window, 'electronAPI', {
    value: mockElectronAPI,
    writable: true
});

describe('AutonomousDashboard IPC Integration', () => {
    const mockShowNotification = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('renders Start button with proper IPC connection', async () => {
        render(<AutonomousDashboard showNotification={mockShowNotification}/>);

        await waitFor(() => {
            expect(screen.getByText(/START SYSTEM/i)).toBeInTheDocument();
        });
    });

    test('Start button calls proper IPC channels when clicked', async () => {
        render(<AutonomousDashboard showNotification={mockShowNotification}/>);

        const startButton = await waitFor(() =>
            screen.getByText(/START SYSTEM/i),
        );

        fireEvent.click(startButton);

        await waitFor(() => {
            expect(mockElectronAPI.initializeTrading).toHaveBeenCalled();
            expect(mockElectronAPI.startBot).toHaveBeenCalled();
        });
    });

    test('displays loading state when starting system', async () => {
        // Mock a delayed response
        mockElectronAPI.startBot.mockImplementation(() =>
            new Promise(resolve => setTimeout(() => resolve({success: true, data: {}}), 100)),
        );

        render(<AutonomousDashboard showNotification={mockShowNotification}/>);

        const startButton = await waitFor(() =>
            screen.getByText(/START SYSTEM/i),
        );

        fireEvent.click(startButton);

        await waitFor(() => {
            expect(screen.getByText(/STARTING.../i)).toBeInTheDocument();
        });
    });

    test('shows error state when IPC call fails', async () => {
        mockElectronAPI.startBot.mockResolvedValue({
            success: false,
            error: 'Failed to start trading system'
        });

        render(<AutonomousDashboard showNotification={mockShowNotification}/>);

        const startButton = await waitFor(() =>
            screen.getByText(/START SYSTEM/i),
        );

        fireEvent.click(startButton);

        await waitFor(() => {
            expect(mockShowNotification).toHaveBeenCalledWith(
                expect.stringContaining('Failed to start'),
                'error',
            );
        });
    });

    test('displays connection status indicator', async () => {
        render(<AutonomousDashboard showNotification={mockShowNotification}/>);

        await waitFor(() => {
            expect(screen.getByText(/Connection:/i)).toBeInTheDocument();
        });
    });

    test('retry button clears error and retries connection', async () => {
        // First make health check fail
        mockElectronAPI.healthCheck.mockResolvedValueOnce({
            success: false,
            error: 'Connection failed'
        });

        render(<AutonomousDashboard showNotification={mockShowNotification}/>);

        // Wait for error to appear
        await waitFor(() => {
            expect(screen.getByText(/System Error:/i)).toBeInTheDocument();
        });

        // Click retry button
        const retryButton = screen.getByText(/Retry/i);
        fireEvent.click(retryButton);

        await waitFor(() => {
            expect(mockElectronAPI.healthCheck).toHaveBeenCalledTimes(2);
        });
    });

    test('manual refresh button triggers data refresh', async () => {
        render(<AutonomousDashboard showNotification={mockShowNotification}/>);

        const refreshButton = await waitFor(() =>
            screen.getByText(/Refresh/i),
        );

        fireEvent.click(refreshButton);

        await waitFor(() => {
            expect(mockElectronAPI.healthCheck).toHaveBeenCalled();
            expect(mockElectronAPI.getRealTimeStatus).toHaveBeenCalled();
        });
    });
});
