// Import logger for consistent logging
import logger from '../utils/logger';

/// <reference path="../types/electron.d.ts" />
import React, {useCallback, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Chip,
  CircularProgress,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography
} from '@mui/material';
import {ShowChart, Storage} from '@mui/icons-material';
import HolographicCard from './HolographicCard';

const customWindow = window;
const electronAPI = {
    getMarketData: (_symbol = 'BTC') => Promise.resolve({success: true, data: []}),
    fetchPriceHistory: (_symbol = 'BTC', _timeframe = '1h') => Promise.resolve({success: true, data: []}),
    ...customWindow.electronAPI
};

const DataCollector = () => {
    const [marketData, setMarketData] = useState([]);
    const [priceHistory, setPriceHistory] = useState([]);
    const [loading, setLoading] = useState(true);

    const fetchData = useCallback(async () => {
        try {
            const [market, history] = await Promise.all([
                electronAPI.getMarketData('BTC'),
                electronAPI.fetchPriceHistory('BTC', '1h')]);

            if (market?.success) setMarketData(market.data || []);
            if (history?.success) setPriceHistory(history.data || []);
        } catch (error) {
            logger.error('Failed to fetch data:', error);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 60000); // Refresh every minute
        return () => clearInterval(interval);
    }, [fetchData]);

    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(value || 0);
    };

    if (loading) {
        return (
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh'}}>
                <CircularProgress/>
            </Box>
        );
    }

    return (
        <Box sx={{p: 3}}>
            <Typography variant="h4" sx={{color: '#00eaff', fontWeight: 800, mb: 3}}>
                <Storage sx={{mr: 2}}/>
                Data Collector
            </Typography>

            <Grid container spacing={3}>
                {/* Market Data */}
                <Grid item xs={12} md={8}>
                    <HolographicCard variant="premium" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#00eaff', mb: 2}}>
                            Live Market Data ({marketData.length})
                        </Typography>
                        <TableContainer>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell sx={{color: '#00eaff'}}>Symbol</TableCell>
                                        <TableCell sx={{color: '#00eaff'}}>Price</TableCell>
                                        <TableCell sx={{color: '#00eaff'}}>Change</TableCell>
                                        <TableCell sx={{color: '#00eaff'}}>Volume</TableCell>
                                        <TableCell sx={{color: '#00eaff'}}>Market Cap</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {marketData.map((coin) => (
                                        <TableRow key={coin.symbol || `coin-${Math.random()}`}>
                                            <TableCell sx={{color: '#fff', fontWeight: 600}}>
                                                {coin.symbol || 'Unknown'}
                                            </TableCell>
                                            <TableCell sx={{color: '#fff'}}>
                                                {formatCurrency(coin.price)}
                                            </TableCell>
                                            <TableCell>
                                                <Chip
                                                    label={`${coin.change >= 0 ? '+' : ''}${coin.change?.toFixed(2) || 0}%`}
                                                    size="small"
                                                    sx={{
                                                        backgroundColor: coin.change >= 0 ? '#4caf5020' : '#f4433620',
                                                        color: coin.change >= 0 ? '#4caf50' : '#f44336'
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell sx={{color: '#fff'}}>
                                                {formatCurrency(coin.volume)}
                                            </TableCell>
                                            <TableCell sx={{color: '#fff'}}>
                                                {formatCurrency(coin.marketCap)}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </HolographicCard>
                </Grid>

                {/* Price History */}
                <Grid item xs={12} md={4}>
                    <HolographicCard variant="secondary" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#a259ff', mb: 2}}>
                            <ShowChart sx={{mr: 1}}/>
                            Price History ({priceHistory.length})
                        </Typography>
                        <TableContainer>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell sx={{color: '#a259ff'}}>Time</TableCell>
                                        <TableCell sx={{color: '#a259ff'}}>Price</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {priceHistory.map((item, index) => (
                                        <TableRow key={index}>
                                            <TableCell sx={{color: '#fff'}}>
                                                {new Date(item.timestamp).toLocaleTimeString()}
                                            </TableCell>
                                            <TableCell sx={{color: '#fff'}}>
                                                {formatCurrency(item.price)}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </HolographicCard>
                </Grid>
            </Grid>
        </Box>
    );
};

DataCollector.propTypes = {
    marketData: PropTypes.array,
    priceHistory: PropTypes.array
};

export default DataCollector;