import React from 'react';
import PropTypes from 'prop-types';
import {
    Box,
    Divider,
    Drawer,
    List,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    Tooltip,
    Typography
} from '@mui/material';
import {BugReport, Dashboard, MonitorHeart, Settings, Visibility} from '@mui/icons-material';
import GridOnIcon from '@mui/icons-material/GridOn';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';


export default function Sidebar({selected, onSelect, onSettingsClick}) {
    const navItems = [
        {text: 'Ultimate Dashboard', icon: <Dashboard/>, color: '#00eaff'},
        {text: 'Grid Trading', icon: <GridOnIcon/>, color: '#4caf50'},
        {text: 'Arbitrage', icon: <CompareArrowsIcon/>, color: '#ff9800'},
        {text: 'Portfolio', icon: <AccountBalanceWalletIcon/>, color: '#9c27b0'},
        {text: 'Whale Tracker', icon: <Visibility/>, color: '#845ef7'},
        {text: 'System Diagnostics', icon: <BugReport/>, color: '#ff5722'},
        {text: 'Bot Monitoring', icon: <MonitorHeart/>, color: '#00eaff'}];

    return (
        <Drawer
            variant="permanent"
            sx={{
                width: 280,
                flexShrink: 0,
                '& .MuiDrawer-paper': {
                    width: 280,
                    boxSizing: 'border-box',
                    background: 'linear-gradient(180deg, rgba(24,26,32,0.95) 0%, rgba(35,39,47,0.95) 100%)',
                    borderRight: '1px solid rgba(0,234,255,0.2)',
                    backdropFilter: 'blur(10px)'
                }
            }}
        >
            {/* Header */}
            <Box sx={{p: 3, borderBottom: '1px solid rgba(255,255,255,0.1)'}}>
                <Box sx={{display: 'flex', alignItems: 'center', mb: 1}}>
                    <TrendingUpIcon sx={{
                        color: '#00eaff',
                        fontSize: '2rem',
                        mr: 1
                    }}/>
                    <Typography
                        variant="h6"
                        sx={{
                            background: 'linear-gradient(45deg, #00eaff 30%, #a259ff 90%)',
                            backgroundClip: 'text',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            fontWeight: 800
                        }}
                    >
                        Electron Trader
                    </Typography>
                </Box>
                <Typography variant="body2" sx={{color: '#888', fontSize: '0.8rem'}}>
                    Professional Trading Suite
                </Typography>
            </Box>

            {/* Navigation */}
            <List sx={{px: 2, py: 2}}>
                {navItems.map((item, idx) => (
                    <Tooltip key={item.text} title={item.text} placement="right">
                        <ListItemButton
                            selected={selected === idx}
                            onClick={() => onSelect(idx)}
                            sx={{
                                borderRadius: 2,
                                mb: 1,
                                transition: 'all 0.3s ease',
                                '&.Mui-selected': {
                                    background: `linear-gradient(90deg, ${item.color}20 0%, ${item.color}10 100%)`,
                                    border: `1px solid ${item.color}40`,
                                    '&:hover': {
                                        background: `linear-gradient(90deg, ${item.color}30 0%, ${item.color}15 100%)`
                                    }
                                },
                                '&:hover': {
                                    background: 'rgba(255,255,255,0.05)',
                                    transform: 'translateX(4px)'
                                }
                            }}
                        >
                            <ListItemIcon sx={{
                                color: selected === idx ? item.color : '#888',
                                minWidth: '40px',
                                transition: 'color 0.3s ease'
                            }}>
                                {item.icon}
                            </ListItemIcon>
                            <ListItemText
                                primary={item.text}
                                sx={{
                                    '& .MuiListItemText-primary': {
                                        color: selected === idx ? '#fff' : '#888',
                                        fontWeight: selected === idx ? 600 : 400,
                                        transition: 'color 0.3s ease'
                                    }
                                }}
                            />
                        </ListItemButton>
                    </Tooltip>
                ))}
            </List>

            <Box sx={{flexGrow: 1}}/>

            <Divider sx={{mx: 2, borderColor: 'rgba(255,255,255,0.1)'}}/>

            <List sx={{px: 2, py: 2}}>
                <Tooltip title="Settings" placement="right">
                    <ListItemButton
                        onClick={onSettingsClick}
                        sx={{
                            borderRadius: 2,
                            mb: 1,
                            transition: 'all 0.3s ease',
                            '&:hover': {
                                background: 'rgba(255,255,255,0.05)',
                                transform: 'translateX(4px)'
                            }
                        }}
                    >
                        <ListItemIcon sx={{color: '#888', minWidth: '40px'}}>
                            <Settings/>
                        </ListItemIcon>
                        <ListItemText
                            primary="Settings"
                            sx={{
                                '& .MuiListItemText-primary': {
                                    color: '#888',
                                    fontWeight: 400
                                }
                            }}
                        />
                    </ListItemButton>
                </Tooltip>
            </List>
            {/* Footer */}
            <Box sx={{
                p: 2,
                borderTop: '1px solid rgba(255,255,255,0.1)',
                textAlign: 'center'
            }}>
                <Typography variant="body2" sx={{color: '#666', fontSize: '0.7rem'}}>
                    v1.0.0 • AI-Powered
                </Typography>
            </Box>
        </Drawer>
    );
}

Sidebar.propTypes = {
    selected: PropTypes.number.isRequired,
    onSelect: PropTypes.func.isRequired,
    onSettingsClick: PropTypes.func.isRequired
};
