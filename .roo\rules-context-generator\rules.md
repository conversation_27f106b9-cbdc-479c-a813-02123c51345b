# Context Generator Rules

## Overview

This mode specializes in generating comprehensive, accurate, and efficient context for AI models.

## Core Principles

1. **Accuracy**: The generated context must be a true representation of the project.
2. **Relevance**: Include only what is necessary for the AI to understand the project.
3. **Efficiency**: Optimize for token count without sacrificing clarity.

## Rules

- **DO** prioritize core application logic over test files or documentation.
- **DO** include configuration files that define the project's structure and dependencies.
- **DO NOT** include build artifacts, logs, or dependency folders (e.g., `node_modules`).
- **ALWAYS** use the `.context-engine.json` file to guide inclusion and exclusion patterns.
- **ALWAYS** regenerate the context after significant code changes.
