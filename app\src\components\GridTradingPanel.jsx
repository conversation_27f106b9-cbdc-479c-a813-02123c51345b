'use strict';

const React = require('react');
const {useState, useEffect, useCallback} = React;
const PropTypes = require('prop-types');
const logger = require('../utils/logger');
const {
    Box,
    CircularProgress,
    Typography,
    Grid,
    TableContainer,
    Table,
    TableHead,
    TableRow,
    TableCell,
    TableBody,
    Chip,
    Alert
} = require('@mui/material');
const {GridOn, MonetizationOn, AutoGraph, Timeline} = require('@mui/icons-material');
const AccountBalance = require('@mui/icons-material/AccountBalance');
const HolographicCard = require('./HolographicCard');

/**
 * @typedef {Object} ElectronAPI
 * @property {Function} [getGridPositions]
 * @property {Function} [getGridHistory]
 */

/**
 * @type {Window & { electronAPI?: ElectronAPI }}
 */
const customWindow = window;
const api = customWindow.electronAPI || {
    getGridPositions: () => Promise.resolve({
        success: true,
        data: []
    }),
    getGridHistory: () => Promise.resolve({
        success: true,
        data: {
            totalPl: 0,
            activeGrids: 0,
            totalInvestment: 0,
            history: []
        }
    })
};
const GridTradingPanel = function GridTradingPanel({
                                                       showNotification
                                                   }) {
    const [gridPositions, setGridPositions] = useState([]);
    const [gridStats, setGridStats] = useState({
        totalPl: 0,
        activeGrids: 0,
        totalInvestment: 0,
        recentActivity: []
    });
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    const fetchData = useCallback(async () => {
        try {
            setError(null);
            const [positionsResult, statsResult] = await Promise.all([api.getGridPositions(), api.getGridHistory(), // Assuming this provides stats and history
            ]);
            if (positionsResult.success) {
                setGridPositions(positionsResult.data);
            } else {
                throw new Error(positionsResult.error || 'Failed to fetch grid positions.');
            }
            if (statsResult.success) {
                // Assuming statsResult.data has the structure { totalPl, activeGrids, totalInvestment, history }
                setGridStats({
                    totalPl: statsResult.data.totalPl || 0,
                    activeGrids: positionsResult.data.length,
                    totalInvestment: statsResult.data.totalInvestment || 0,
                    recentActivity: statsResult.data.history || []
                });
            } else {
                throw new Error(statsResult.error || 'Failed to fetch grid history.');
            }
        } catch (error) {
            logger.error('Failed to fetch grid data:', error);
            setError(error.message);
            showNotification(error.message, 'error');
        } finally {
            setIsLoading(false);
        }
    }, [showNotification]);
    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
        return () => clearInterval(interval);
    }, [fetchData]);
    const formatCurrency = value => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(value || 0);
    };
    const formatPercentage = value => {
        const color = value >= 0 ? '#4caf50' : '#f44336';
        const sign = value >= 0 ? '+' : '';
        return /*#__PURE__*/React.createElement('span', {
            style: {
                color,
                fontWeight: 600
            }
        }, sign, (value || 0).toFixed(2), '%');
    };
    if (isLoading) {
        return /*#__PURE__*/React.createElement(Box, {
            sx: {
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100vh'
            }
        }, /*#__PURE__*/React.createElement(CircularProgress, null));
    }
    return /*#__PURE__*/React.createElement(Box, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/React.createElement(Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            mb: 3
        }
    }, /*#__PURE__*/React.createElement(GridOn, {
        sx: {
            color: '#00eaff',
            mr: 2,
            fontSize: 32
        }
    }), /*#__PURE__*/React.createElement(Box, null, /*#__PURE__*/React.createElement(Typography, {
        variant: 'h4',
        sx: {
            color: '#00eaff',
            fontWeight: 800,
            background: 'linear-gradient(45deg, #00eaff 30%, #a259ff 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
        }
    }, 'Grid Trading Monitoring'), /*#__PURE__*/React.createElement(Typography, {
        variant: 'subtitle1',
        sx: {
            color: '#888'
        }
    }, 'Live monitoring of autonomous grid trading bots'))), error && /*#__PURE__*/React.createElement(Alert, {
        severity: 'error',
        sx: {
            mb: 2
        }
    }, error), /*#__PURE__*/React.createElement(Grid, {
        container: true,
        spacing: 3,
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/React.createElement(Grid, {
        item: true,
        xs: 12,
        md: 4
    }, /*#__PURE__*/React.createElement(HolographicCard, {
        variant: 'quantum',
        elevation: 'medium',
        sx: {
            textAlign: 'center'
        }
    }, /*#__PURE__*/React.createElement(MonetizationOn, {
        sx: {
            fontSize: 40,
            color: 'primary.main'
        }
    }), /*#__PURE__*/React.createElement(Typography, {
        variant: 'h6'
    }, 'Total P/L'), /*#__PURE__*/React.createElement(Typography, {
        variant: 'h5',
        sx: {
            color: gridStats.totalPl >= 0 ? 'success.main' : 'error.main'
        }
    }, formatCurrency(gridStats.totalPl)))), /*#__PURE__*/React.createElement(Grid, {
        item: true,
        xs: 12,
        md: 4
    }, /*#__PURE__*/React.createElement(HolographicCard, {
        variant: 'premium',
        elevation: 'medium',
        sx: {
            textAlign: 'center'
        }
    }, /*#__PURE__*/React.createElement(AutoGraph, {
        sx: {
            fontSize: 40,
            color: 'secondary.main'
        }
    }), /*#__PURE__*/React.createElement(Typography, {
        variant: 'h6'
    }, 'Active Grids'), /*#__PURE__*/React.createElement(Typography, {
        variant: 'h5'
    }, gridStats.activeGrids))), /*#__PURE__*/React.createElement(Grid, {
        item: true,
        xs: 12,
        md: 4
    }, /*#__PURE__*/React.createElement(HolographicCard, {
        variant: 'gold',
        elevation: 'medium',
        sx: {
            textAlign: 'center'
        }
    }, /*#__PURE__*/React.createElement(AccountBalance, {
        sx: {
            fontSize: 40,
            color: 'warning.main'
        }
    }), /*#__PURE__*/React.createElement(Typography, {
        variant: 'h6'
    }, 'Total Investment'), /*#__PURE__*/React.createElement(Typography, {
        variant: 'h5'
    }, formatCurrency(gridStats.totalInvestment))))), /*#__PURE__*/React.createElement(Grid, {
        container: true,
        spacing: 3
    }, /*#__PURE__*/React.createElement(Grid, {
        item: true,
        xs: 12,
        lg: 8
    }, /*#__PURE__*/React.createElement(HolographicCard, {
        variant: 'default',
        elevation: 'medium',
        sx: {}
    }, /*#__PURE__*/React.createElement(Typography, {
        variant: 'h6',
        sx: {
            color: '#a259ff',
            mb: 2,
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/React.createElement(AutoGraph, {
        sx: {
            mr: 1
        }
    }), 'Active Grid Positions (', gridPositions.length, ')'), gridPositions.length === 0 ? /*#__PURE__*/React.createElement(Typography, {
        sx: {
            textAlign: 'center',
            color: '#888',
            mt: 4
        }
    }, 'No active grid positions.') : /*#__PURE__*/React.createElement(TableContainer, null, /*#__PURE__*/React.createElement(Table, null, /*#__PURE__*/React.createElement(TableHead, null, /*#__PURE__*/React.createElement(TableRow, null, /*#__PURE__*/React.createElement(TableCell, null, 'Pair'), /*#__PURE__*/React.createElement(TableCell, null, 'Status'), /*#__PURE__*/React.createElement(TableCell, null, 'P/L'), /*#__PURE__*/React.createElement(TableCell, null, '24h P/L'), /*#__PURE__*/React.createElement(TableCell, null, 'Trades'), /*#__PURE__*/React.createElement(TableCell, null, 'Uptime'))), /*#__PURE__*/React.createElement(TableBody, null, gridPositions.map(position => /*#__PURE__*/React.createElement(TableRow, {
        key: position.id
    }, /*#__PURE__*/React.createElement(TableCell, null, position.symbol), /*#__PURE__*/React.createElement(TableCell, null, /*#__PURE__*/React.createElement(Chip, {
        label: position.status,
        color: position.status === 'active' ? 'success' : 'default',
        size: 'small'
    })), /*#__PURE__*/React.createElement(TableCell, null, formatCurrency(position.totalPl)), /*#__PURE__*/React.createElement(TableCell, null, formatPercentage(position.dailyPl)), /*#__PURE__*/React.createElement(TableCell, null, position.totalTrades), /*#__PURE__*/React.createElement(TableCell, null, position.uptime)))))))), /*#__PURE__*/React.createElement(Grid, {
        item: true,
        xs: 12,
        lg: 4
    }, /*#__PURE__*/React.createElement(HolographicCard, {
        variant: 'default',
        elevation: 'medium',
        sx: {}
    }, /*#__PURE__*/React.createElement(Typography, {
        variant: 'h6',
        sx: {
            color: '#00eaff',
            mb: 2,
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/React.createElement(Timeline, {
        sx: {
            mr: 1
        }
    }), 'Grid Activity Log'), /*#__PURE__*/React.createElement(Box, {
        sx: {
            height: 400,
            overflowY: 'auto'
        }
    }, gridStats.recentActivity.length === 0 ? /*#__PURE__*/React.createElement(Typography, {
        sx: {
            textAlign: 'center',
            color: '#888',
            mt: 4
        }
    }, 'No recent activity.') : gridStats.recentActivity.map(activity => /*#__PURE__*/React.createElement(Box, {
        key: activity.id || activity.timestamp,
        sx: {
            mb: 1,
            p: 1,
            borderRadius: 1,
            backgroundColor: 'rgba(255,255,255,0.02)'
        }
    }, /*#__PURE__*/React.createElement(Typography, {
        variant: 'body2',
        sx: {
            color: 'primary.main'
        }
    }, new Date(activity.timestamp).toLocaleString()), /*#__PURE__*/React.createElement(Typography, {
        variant: 'body2'
    }, activity.message))))))));
};
GridTradingPanel.propTypes = {
    showNotification: PropTypes.func.isRequired
};

module.exports = GridTradingPanel;