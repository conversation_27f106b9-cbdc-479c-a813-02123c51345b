'use strict';

const ErrorReporter = require('../services/ErrorReporter');
const enhancedErrorLogger = require('./GlobalErrorHandler');
const logger = require('./logger');

/**
 * @fileoverview System-Wide Error Handler
 * @description Centralized error handling for the entire application, including
 * frontend, backend, and component-level errors. It coordinates error reporting,
 * recovery strategies, and system health monitoring.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-22
 */
class SystemWideErrorHandler {
    constructor() {
        // this.initialized = false;
        // this.errorReporter = new ErrorReporter();
        // this.errorBoundaries = new Map();
        // this.criticalWorkflows = new Set(['trading', 'portfolio', 'authentication']);
        // this.systemHealth = 'healthy'; // 'healthy', 'degraded', 'critical'
        // this.errorStats = {
            totalErrors: 0,
            criticalErrors: 0,
            recoveredErrors: 0,
            lastError: null,
        };

        // this.circuitBreaker = {
            isOpen: false,
            failureCount: 0,
            threshold: 5, // 5 critical failures to open
            resetTimeout: 300000, // 5 minutes
            lastFailure: null,
        };

        // this.recoveryStrategies = new Map([
            ['network', this.handleNetworkError.bind(this)],
            ['authentication', this.handleAuthError.bind(this)],
            ['trading', this.handleTradingError.bind(this)],
            ['database', this.handleDatabaseError.bind(this)],
            ['component', this.handleComponentError.bind(this)],
            ['critical', this.handleCriticalError.bind(this)],
        ]);

        // this.handleGlobalError = this.handleGlobalError.bind(this);
        // this.handleUnhandledRejection = this.handleUnhandledRejection.bind(this);
        // this.handleResourceError = this.handleResourceError.bind(this);
    }

    /**
     * Initializes the system-wide error handler.
     */
    async initialize() {
        if (this.initialized) return;
        try {
            logger.info('🛡️ Initializing System-Wide Error Handler...');
            await enhancedErrorLogger.initialize();
            // this.setupGlobalErrorHandlers();
            // this.setupErrorBoundaryCoordination();
            await this.setupBackendErrorIntegration();
            // this.setupHealthMonitoring();
            // this.setupAutomaticRecovery();
            // this.initialized = true;
            logger.info('✅ System-Wide Error Handler initialized');
            // this.reportSystemEvent('system-error-handler-initialized', {
                timestamp: new Date().toISOString(),
                status: 'success',
            });
        } catch (error) {
            logger.error('❌ Failed to initialize System-Wide Error Handler:', error);
            throw error;
        }
    }

    /**
     * Sets up global event listeners for uncaught errors.
     */
    setupGlobalErrorHandlers() {
        window.addEventListener('error', this.handleGlobalError);
        window.addEventListener('unhandledrejection', this.handleUnhandledRejection);
        window.addEventListener('error', this.handleResourceError, true);
        logger.info('✅ Global error handlers set up');
    }

    /**
     * Sets up coordination with React error boundaries.
     */
    setupErrorBoundaryCoordination() {
        window.addEventListener('componentError', (event) => {
            // this.handleComponentError(event.detail);
        });

        window.registerErrorBoundary = (name, boundary) => {
            // this.errorBoundaries.set(name, boundary);
            logger.info(`📝 Registered error boundary: ${name}`);
        };

        window.unregisterErrorBoundary = (name) => {
            // this.errorBoundaries.delete(name);
            logger.info(`📝 Unregistered error boundary: ${name}`);
        };
    }

    /**
     * Sets up IPC listeners for backend errors and health updates.
     */
    async setupBackendErrorIntegration() {
        try {
            if (window.electronAPI && window.electronAPI.ipcRenderer) {
                window.electronAPI.ipcRenderer.on('backend-error', (event, errorData) => {
                    // this.handleBackendError(errorData);
                });
                window.electronAPI.ipcRenderer.on('system-health-update', (event, healthData) => {
                    // this.handleSystemHealthUpdate(healthData);
                });
                await window.electronAPI.ipcRenderer.invoke('register-error-listener');
            }
            logger.info('✅ Backend error integration set up');
        } catch (error) {
            logger.warn('⚠️ Backend error integration failed:', error);
        }
    }

    /**
     * Sets up periodic health checks and error rate monitoring.
     */
    setupHealthMonitoring() {
        setInterval(() => this.performHealthCheck(), 30000);
        setInterval(() => this.checkErrorRates(), 60000);
        logger.info('✅ Health monitoring set up');
    }

    /**
     * Sets up periodic attempts at automatic recovery.
     */
    setupAutomaticRecovery() {
        setInterval(() => this.attemptAutomaticRecovery(), 120000);
        logger.info('✅ Automatic recovery set up');
    }

    /**
     * Handles global JavaScript errors.
     * @param {ErrorEvent} event - The error event.
     */
    handleGlobalError(event) {
        const {message, filename, lineno, colno, error} = event;
        const errorData = {
            type: 'javascript-error',
            message,
            filename,
            lineno,
            colno,
            stack: error ? error.stack : null,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
        };
        enhancedErrorLogger.logError(error || new Error(message), {filename, lineno, colno});
        // this.processError(errorData);
    }

    /**
     * Handles unhandled promise rejections.
     * @param {PromiseRejectionEvent} event - The rejection event.
     */
    handleUnhandledRejection(event) {
        const {reason} = event;
        const errorData = {
            type: 'unhandled-promise-rejection',
            message: reason.message || String(reason),
            stack: reason.stack,
            timestamp: new Date().toISOString(),
            url: window.location.href,
        };
        enhancedErrorLogger.error('Unhandled Promise Rejection', {
            reason: reason.message || String(reason),
            stack: reason.stack,
        }, 'unhandled-promise-rejection');
        // this.processError(errorData);
        event.preventDefault();
    }

    /**
     * Handles resource loading errors.
     * @param {Event} event - The error event.
     */
    handleResourceError(event) {
        const target = event.target;
        if (target && (target.src || target.href)) {
            const errorData = {
                type: 'resource-error',
                message: `Failed to load resource: ${target.src || target.href}`,
                resource: target.tagName,
                source: target.src || target.href,
                timestamp: new Date().toISOString(),
                url: window.location.href,
            };
            // this.processError(errorData);
        }
    }

    /**
     * Handles errors from React components.
     * @param {Object} errorDetail - The error details from the component.
     */
    handleComponentError(errorDetail) {
        const {componentName, workflow, errorData: componentErrorData} = errorDetail;
        const errorData = {
            type: 'component-error',
            component: componentName,
            workflow,
            message: componentErrorData.message,
            stack: componentErrorData.stack,
            timestamp: new Date().toISOString(),
            critical: this.criticalWorkflows.has(workflow),
        };
        enhancedErrorLogger.logComponentError(componentName, new Error(componentErrorData.message || 'Component error'), {workflow});
        // this.processError(errorData);
    }

    /**
     * Handles errors originating from the backend.
     * @param {Object} errorData - The error data from the backend.
     */
    handleBackendError(errorData) {
        const processedError = {
            type: 'backend-error',
            ...errorData,
            timestamp: errorData.timestamp || new Date().toISOString(),
            critical: errorData.severity === 'critical',
        };
        // this.processError(processedError);
    }

    /**
     * Processes a system health update from the backend.
     * @param {Object} healthData - The health data from the backend.
     */
    handleSystemHealthUpdate(healthData) {
        const previousHealth = this.systemHealth;
        // this.systemHealth = healthData.status;
        if (previousHealth !== this.systemHealth) {
            logger.info(`🏥 System health changed: ${previousHealth} → ${this.systemHealth}`);
            // this.reportSystemEvent('system-health-changed', {
                previousHealth,
                currentHealth: this.systemHealth,
                timestamp: new Date().toISOString(),
                details: healthData.details,
            });
            if (this.systemHealth === 'degraded' || this.systemHealth === 'critical') {
                // this.triggerSystemRecovery(healthData);
            }
        }
    }

    /**
     * Central processing pipeline for all errors.
     * @param {Object} errorData - The error data to process.
     */
    async processError(errorData) {
        try {
            // this.updateErrorStats(errorData);

            if (this.shouldTriggerCircuitBreaker(errorData)) {
                // this.triggerCircuitBreaker(errorData);
                return;
            }

            const severity = this.classifyErrorSeverity(errorData);
            errorData.severity = severity;

            enhancedErrorLogger.log(severity === 'critical' ? 'critical' : 'error', errorData.message, errorData, errorData.type);
            logger.error(`🚨 System Error [${severity}]:`, errorData);

            await this.errorReporter.report(errorData);

            const strategy = this.determineRecoveryStrategy(errorData);
            if (strategy && this.recoveryStrategies.has(strategy)) {
                await this.executeRecoveryStrategy(strategy, errorData);
            }

            // this.notifyErrorBoundaries(errorData);
            // this.updateSystemHealth(errorData);
        } catch (processingError) {
            logger.error('❌ Error processing system error:', processingError);
            logger.error('Original error:', errorData);
        }
    }

    /**
     * Updates internal error statistics.
     * @param {Object} errorData - The error data.
     */
    updateErrorStats(errorData) {
        // this.errorStats.totalErrors++;
        // this.errorStats.lastError = errorData;
        if (errorData.critical || errorData.severity === 'critical') {
            // this.errorStats.criticalErrors++;
        }
    }

    /**
     * Determines if the circuit breaker should be triggered.
     * @param {Object} errorData - The error data.
     * @returns {boolean}
     */
    shouldTriggerCircuitBreaker(errorData) {
        if (errorData.critical || errorData.severity === 'critical') {
            // this.circuitBreaker.failureCount++;
            // this.circuitBreaker.lastFailure = Date.now();
            return this.circuitBreaker.failureCount >= this.circuitBreaker.threshold;
        }
        return false;
    }

    /**
     * Activates the circuit breaker, entering safe mode.
     * @param {Object} errorData - The error that triggered the breaker.
     */
    triggerCircuitBreaker(errorData) {
        // this.circuitBreaker.isOpen = true;
        logger.error('🔴 CIRCUIT BREAKER ACTIVATED - System entering safe mode');
        // this.reportSystemEvent('circuit-breaker-activated', {
            reason: errorData,
            timestamp: new Date().toISOString(),
            failureCount: this.circuitBreaker.failureCount,
        });
        // this.enterSafeMode();
        setTimeout(() => this.resetCircuitBreaker(), this.circuitBreaker.resetTimeout);
    }

    /**
     * Resets the circuit breaker, exiting safe mode.
     */
    resetCircuitBreaker() {
        // this.circuitBreaker.isOpen = false;
        // this.circuitBreaker.failureCount = 0;
        logger.info('🟢 Circuit breaker reset - System exiting safe mode');
        // this.reportSystemEvent('circuit-breaker-reset', {
            timestamp: new Date().toISOString(),
        });
        // this.exitSafeMode();
    }

    /**
     * Classifies the severity of an error.
     * @param {Object} errorData - The error data.
     * @returns {string} The severity level ('critical', 'high', 'medium', 'low').
     */
    classifyErrorSeverity(errorData) {
        const message = errorData.message || '';
        if (errorData.critical || (errorData.type === 'backend-error' && errorData.component === 'trading') || message.includes('trading') || message.includes('position') || message.includes('order')) {
            return 'critical';
        }
        if (errorData.type === 'unhandled-promise-rejection' || message.includes('network') || message.includes('database') || message.includes('authentication')) {
            return 'high';
        }
        if (errorData.type === 'component-error' || errorData.type === 'resource-error') {
            return 'medium';
        }
        return 'low';
    }

    /**
     * Determines the appropriate recovery strategy for an error.
     * @param {Object} errorData - The error data.
     * @returns {string | null} The name of the recovery strategy.
     */
    determineRecoveryStrategy(errorData) {
        const message = errorData.message || '';
        if (message.includes('network') || message.includes('fetch')) return 'network';
        if (message.includes('auth') || message.includes('login')) return 'authentication';
        if (message.includes('trading') || errorData.component === 'trading') return 'trading';
        if (message.includes('database') || message.includes('storage')) return 'database';
        if (errorData.type === 'component-error') return 'component';
        if (errorData.severity === 'critical') return 'critical';
        return null;
    }

    /**
     * Executes a named recovery strategy.
     * @param {string} strategy - The name of the strategy.
     * @param {Object} errorData - The error data.
     * @returns {Promise<boolean>} Whether recovery was successful.
     */
    async executeRecoveryStrategy(strategy, errorData) {
        try {
            logger.info(`🔄 Executing recovery strategy: ${strategy}`);
            const recoveryHandler = this.recoveryStrategies.get(strategy);
            const success = await recoveryHandler(errorData);
            if (success) {
                // this.errorStats.recoveredErrors++;
                logger.info(`✅ Recovery successful for strategy: ${strategy}`);
            } else {
                logger.warn(`⚠️ Recovery failed for strategy: ${strategy}`);
            }
            return success;
        } catch (error) {
            logger.error('❌ Recovery strategy execution failed:', error);
            return false;
        }
    }

    // --- Recovery Strategy Implementations ---

    async handleNetworkError() {
        logger.info('🌐 Handling network error...');
        if (!navigator.onLine) {
            logger.info('📡 Network offline - waiting for reconnection');
            return new Promise(resolve => {
                const handleOnline = () => {
                    window.removeEventListener('online', handleOnline);
                    resolve(true);
                };
                window.addEventListener('online', handleOnline);
            });
        }
        return true;
    }

    async handleAuthError() {
        logger.info('🔐 Handling authentication error...');
        localStorage.removeItem('auth');
        sessionStorage.clear();
        if (!window.location.pathname.includes('/login')) {
            window.location.href = '/login';
        }
        return true;
    }

    async handleTradingError(errorData) {
        logger.info('💰 Handling trading error...');
        if (errorData.severity === 'critical' && window.electronAPI) {
            try {
                await window.electronAPI.ipcRenderer.invoke('emergency-stop-trading');
            } catch (error) {
                logger.error('Failed to stop trading:', error);
            }
        }
        return true;
    }

    async handleDatabaseError() {
        logger.info('🗄️ Handling database error...');
        try {
            localStorage.removeItem('corrupted_data');
            window.dispatchEvent(new CustomEvent('refresh-data'));
        } catch (error) {
            logger.error('Database recovery failed:', error);
        }
        return true;
    }

    async handleCriticalError(errorData) {
        logger.info('🚨 Handling critical error...');
        // this.enterSafeMode();
        // this.showCriticalErrorNotification(errorData);
        return true;
    }

    /**
     * Notifies all registered error boundaries of a system error.
     * @param {Object} errorData - The error data.
     */
    notifyErrorBoundaries(errorData) {
        for (const [name, boundary] of this.errorBoundaries) {
            if (typeof boundary.onSystemError === 'function') {
                try {
                    boundary.onSystemError(errorData);
                } catch (error) {
                    logger.error(`Error notifying boundary ${name}:`, error);
                }
            }
        }
    }

    /**
     * Updates the system health status based on an error.
     * @param {Object} errorData - The error data.
     */
    updateSystemHealth(errorData) {
        const previousHealth = this.systemHealth;
        if (errorData.severity === 'critical') {
            // this.systemHealth = 'critical';
        } else if (errorData.severity === 'high' && this.systemHealth === 'healthy') {
            // this.systemHealth = 'degraded';
        }
        if (previousHealth !== this.systemHealth) {
            // this.reportSystemEvent('system-health-degraded', {
                previousHealth,
                currentHealth: this.systemHealth,
                cause: errorData,
                timestamp: new Date().toISOString(),
            });
        }
    }

    /**
     * Performs a periodic health check and reports it.
     */
    async performHealthCheck() {
        try {
            const healthData = this.getSystemHealth();
            if (window.electronAPI) {
                await window.electronAPI.ipcRenderer.invoke('report-frontend-health', healthData);
            }
        } catch (error) {
            logger.error('Health check failed:', error);
        }
    }

    /**
     * Checks if the error rate is within acceptable limits.
     */
    checkErrorRates() {
        // Simplified: In a real app, track errors over time.
        const errorRate = this.errorStats.totalErrors / 5; // errors per 5 min
        if (errorRate > 10) {
            logger.warn(`⚠️ High error rate detected: ${errorRate.toFixed(2)} errors/min`);
            // this.reportSystemEvent('high-error-rate', {
                errorRate,
                timestamp: new Date().toISOString(),
            });
        }
    }

    /**
     * Attempts to automatically recover the system from a bad state.
     */
    async attemptAutomaticRecovery() {
        if (this.systemHealth !== 'healthy') {
            logger.info('🔄 Attempting automatic system recovery...');
            if (this.systemHealth === 'degraded') {
                await this.recoverFromDegradedState();
            } else if (this.systemHealth === 'critical') {
                await this.recoverFromCriticalState();
            }
        }
    }

    /**
     * Attempts to recover from a 'degraded' state.
     */
    async recoverFromDegradedState() {
        logger.info('🔄 Recovering from degraded state...');
        const now = Date.now();
        if (this.circuitBreaker.lastFailure && now - this.circuitBreaker.lastFailure > 300000) {
            // this.circuitBreaker.failureCount = 0;
        }
        // this.systemHealth = 'healthy';
        // this.reportSystemEvent('automatic-recovery-attempted', {
            fromState: 'degraded',
            timestamp: new Date().toISOString(),
        });
    }

    /**
     * Attempts to recover from a 'critical' state.
     */
    async recoverFromCriticalState() {
        logger.info('🚨 Attempting recovery from critical state...');
        try {
            localStorage.clear();
            sessionStorage.clear();
            setTimeout(() => window.location.reload(), 5000);
        } catch (error) {
            logger.error('Critical state recovery failed:', error);
        }
    }

    /**
     * Puts the application into a safe, limited-functionality mode.
     */
    enterSafeMode() {
        logger.info('🛡️ Entering safe mode...');
        document.body.classList.add('safe-mode');
        window.dispatchEvent(new CustomEvent('enter-safe-mode'));
        // this.showSafeModeNotification();
    }

    /**
     * Exits safe mode and restores full functionality.
     */
    exitSafeMode() {
        logger.info('🟢 Exiting safe mode...');
        document.body.classList.remove('safe-mode');
        window.dispatchEvent(new CustomEvent('exit-safe-mode'));
    }

    /**
     * Displays a notification for critical errors.
     * @param {Object} errorData - The error data.
     */
    showCriticalErrorNotification(errorData) {
        const notification = document.createElement('div');
        notification.className = 'critical-error-notification';
        notification.innerHTML = `
            <div class="error-icon">🚨</div>
            <div class="error-content">
                <h3>Critical System Error</h3>
                <p>A critical error has occurred. The system is entering safe mode.</p>
                <button onclick="window.location.reload()">Reload Application</button>
            </div>
        `;
        document.body.appendChild(notification);
        setTimeout(() => notification.remove(), 30000);
    }

    /**
     * Displays a notification that safe mode is active.
     */
    showSafeModeNotification() {
        const notification = document.createElement('div');
        notification.className = 'safe-mode-notification';
        notification.innerHTML = `
            <div class="safe-mode-icon">🛡️</div>
            <div class="safe-mode-content">
                <h3>Safe Mode Active</h3>
                <p>The system is running in safe mode due to multiple errors.</p>
            </div>
        `;
        document.body.appendChild(notification);
    }

    /**
     * Reports a system event to the backend and other components.
     * @param {string} eventType - The type of event.
     * @param {Object} data - The event data.
     */
    reportSystemEvent(eventType, data) {
        // this.errorReporter.report({
            type: 'system-event',
            eventType,
            data,
            timestamp: new Date().toISOString(),
        });
        window.dispatchEvent(new CustomEvent('system-event', {
            detail: {eventType, data},
        }));
    }

    /**
     * Triggers a system recovery based on health data.
     * @param {Object} healthData - The health data.
     */
    triggerSystemRecovery(healthData) {
        logger.info('🔄 Triggering system recovery:', healthData);
        // this.attemptAutomaticRecovery();
    }

    // --- Public API ---

    /**
     * Gets the current system health status.
     * @returns {Object}
     */
    getSystemHealth() {
        return {
            health: this.systemHealth,
            errorStats: {...this.errorStats},
            circuitBreaker: {
                isOpen: this.circuitBreaker.isOpen,
                failureCount: this.circuitBreaker.failureCount,
            },
            errorBoundaries: Array.from(this.errorBoundaries.keys()),
            timestamp: new Date().toISOString(),
        };
    }

    /**
     * Shuts down the error handler and cleans up resources.
     */
    async shutdown() {
        logger.info('🛑 Shutting down System-Wide Error Handler...');
        window.removeEventListener('error', this.handleGlobalError);
        window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
        window.removeEventListener('error', this.handleResourceError, true);
        await enhancedErrorLogger.shutdown();
        // In a real app, clear intervals by their IDs
        // this.initialized = false;
        logger.info('✅ System-Wide Error Handler shut down');
    }
}

const systemWideErrorHandler = new SystemWideErrorHandler();
module.exports = systemWideErrorHandler;