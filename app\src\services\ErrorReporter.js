/**
 * Simple mock ErrorReporter for build compatibility
 */
class ErrorReporter {
  constructor() {
    // this.console = console;
  }

  async report(errorData) {
    // this.console.error('[ErrorReporter]', errorData);
    return { id: Date.now(), reported: true };
  }

  async reportNetworkError(error, url) {
    return this.report({
      type: 'network_error',
      message: error.message,
      stack: error.stack,
      url,
      timestamp: new Date().toISOString(),
    });
  }

  async reportReactError(error, _errorInfo) {
    return this.report({
      type: 'react_error',
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });
  }

  async retryFailedReports() {
    // Mock implementation
    return Promise.resolve();
  }

  getErrorStats() {
    return {
      total: 0,
      byType: {},
      bySeverity: {},
      recent: 0,
    };
  }

  clearStoredErrors() {
    // Mock implementation
  }
}

module.exports = { ErrorReporter };