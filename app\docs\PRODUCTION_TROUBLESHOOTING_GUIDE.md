# Production Troubleshooting Guide

## Overview

This guide provides comprehensive troubleshooting procedures for common issues encountered when deploying and running the Meme Coin Trader application in production environments.

## Table of Contents

1. [General Troubleshooting Approach](#general-troubleshooting-approach)
2. [Application Startup Issues](#application-startup-issues)
3. [Database Connection Problems](#database-connection-problems)
4. [Trading System Issues](#trading-system-issues)
5. [Exchange API Problems](#exchange-api-problems)
6. [Performance Issues](#performance-issues)
7. [Memory and Resource Problems](#memory-and-resource-problems)
8. [Network and Connectivity Issues](#network-and-connectivity-issues)
9. [Security and Authentication Issues](#security-and-authentication-issues)
10. [Platform-Specific Issues](#platform-specific-issues)
11. [Monitoring and Logging Issues](#monitoring-and-logging-issues)
12. [Recovery Procedures](#recovery-procedures)

## General Troubleshooting Approach

### 1. Initial Diagnosis Steps

```bash
# Check application status
ps aux | grep -i memetrader

# Check system resources
top -p $(pgrep -f memetrader)

# Check disk space
df -h

# Check memory usage
free -h

# Check network connectivity
netstat -tulpn | grep :3001
```

### 2. Log Analysis

```bash
# Check application logs
tail -f /var/log/memetrader/app.log

# Check error logs
tail -f /var/log/memetrader/error.log

# Check system logs
journalctl -u memetrader -f

# Search for specific errors
grep -i "error\|exception\|failed" /var/log/memetrader/*.log
```

### 3. Health Check Verification

```bash
# Basic health check
curl -f http://localhost:3001/health

# Detailed health check
curl -s http://localhost:3001/health/detailed | jq .

# Check specific components
curl -s http://localhost:3001/health/trading | jq .
curl -s http://localhost:3001/health/database | jq .
```

## Application Startup Issues

### Issue: Application Won't Start

**Symptoms:**
- Process doesn't start
- Immediate exit after startup
- No response on health check endpoints

**Diagnostic Steps:**

```bash
# Check if port is already in use
lsof -i :3001

# Check environment variables
env | grep -E "(NODE_ENV|DATABASE|TRADING)"

# Try starting in debug mode
DEBUG=* NODE_ENV=production npm start

# Check for missing dependencies
npm ls --depth=0
```

**Common Causes and Solutions:**

1. **Port Already in Use**
   ```bash
   # Kill process using the port
   sudo kill -9 $(lsof -t -i:3001)
   
   # Or change the port
   export PORT=3002
   ```

2. **Missing Environment Variables**
   ```bash
   # Validate environment configuration
   node scripts/validate-env.js
   
   # Load environment file
   source .env.production
   ```

3. **Database Connection Issues**
   ```bash
   # Test database connection
   node -e "require('./app/trading/data/DatabaseManager.js').testConnection()"
   ```

4. **Permission Issues**
   ```bash
   # Check file permissions
   ls -la /var/log/memetrader/
   
   # Fix permissions
   sudo chown -R memetrader:memetrader /var/log/memetrader/
   sudo chmod 755 /var/log/memetrader/
   ```

### Issue: Application Starts But Crashes Immediately

**Diagnostic Steps:**

```bash
# Check crash logs
journalctl -u memetrader --since "5 minutes ago"

# Check for core dumps
ls -la /var/crash/

# Run with increased verbosity
NODE_ENV=production LOG_LEVEL=debug npm start
```

**Common Solutions:**

1. **Memory Issues**
   ```bash
   # Increase Node.js heap size
   export NODE_OPTIONS="--max-old-space-size=4096"
   ```

2. **Native Module Issues**
   ```bash
   # Rebuild native modules
   npm rebuild
   
   # Specifically rebuild SQLite
   npm rebuild better-sqlite3
   ```

3. **Configuration Errors**
   ```bash
   # Validate configuration files
   node -e "console.log(JSON.parse(require('fs').readFileSync('./config/production.json')))"
   ```

## Database Connection Problems

### Issue: Cannot Connect to Database

**Symptoms:**
- Database connection timeout errors
- "ECONNREFUSED" errors
- Trading system fails to initialize

**Diagnostic Steps:**

```bash
# Test database connectivity
ping database-host

# Check database service status
systemctl status mysql  # For MySQL
systemctl status postgresql  # For PostgreSQL

# Test database connection manually
mysql -h localhost -u username -p database_name  # MySQL
psql -h localhost -U username -d database_name   # PostgreSQL

# Check SQLite file permissions
ls -la ./databases/trading_system.db
```

**Solutions:**

1. **MySQL Connection Issues**
   ```bash
   # Check MySQL service
   sudo systemctl start mysql
   sudo systemctl enable mysql
   
   # Check MySQL configuration
   sudo mysql -e "SHOW VARIABLES LIKE 'bind_address';"
   
   # Grant permissions
   mysql -u root -p -e "GRANT ALL PRIVILEGES ON memetrader.* TO 'memetrader_user'@'%';"
   ```

2. **SQLite Permission Issues**
   ```bash
   # Fix SQLite file permissions
   sudo chown memetrader:memetrader ./databases/trading_system.db
   sudo chmod 664 ./databases/trading_system.db
   
   # Ensure directory is writable
   sudo chmod 755 ./databases/
   ```

3. **Connection Pool Issues**
   ```bash
   # Reduce connection pool size
   export DATABASE_POOL_SIZE=5
   
   # Increase connection timeout
   export DATABASE_TIMEOUT=60000
   ```

### Issue: Database Performance Problems

**Symptoms:**
- Slow query execution
- Database timeouts
- High CPU usage from database

**Diagnostic Steps:**

```bash
# Check database performance
mysql -e "SHOW PROCESSLIST;" | grep memetrader

# Check slow query log
tail -f /var/log/mysql/slow.log

# Analyze SQLite performance
sqlite3 trading_system.db ".timer on" ".explain query plan SELECT * FROM trades;"
```

**Solutions:**

1. **Add Database Indexes**
   ```sql
   -- Add indexes for common queries
   CREATE INDEX idx_trades_timestamp ON trades(timestamp);
   CREATE INDEX idx_trades_symbol ON trades(symbol);
   CREATE INDEX idx_trades_status ON trades(status);
   ```

2. **Optimize Database Configuration**
   ```bash
   # MySQL optimization
   echo "innodb_buffer_pool_size = 1G" >> /etc/mysql/mysql.conf.d/mysqld.cnf
   echo "query_cache_size = 256M" >> /etc/mysql/mysql.conf.d/mysqld.cnf
   
   # SQLite optimization
   export SQLITE_CACHE_SIZE=10000
   export SQLITE_JOURNAL_MODE=WAL
   ```

## Trading System Issues

### Issue: Trading System Not Executing Trades

**Symptoms:**
- No trades being executed
- Trading status shows "inactive"
- Opportunities detected but not acted upon

**Diagnostic Steps:**

```bash
# Check trading system status
curl -s http://localhost:3001/trading/status | jq .

# Check active strategies
curl -s http://localhost:3001/trading/strategies | jq .

# Check recent trading activity
curl -s http://localhost:3001/trading/history?limit=10 | jq .

# Check risk management status
curl -s http://localhost:3001/trading/risk-status | jq .
```

**Common Causes and Solutions:**

1. **Paper Trading Mode Enabled**
   ```bash
   # Check trading mode
   echo $TRADING_MODE
   
   # Switch to live trading
   export TRADING_MODE=live
   export PAPER_TRADING=false
   ```

2. **Risk Management Blocking Trades**
   ```bash
   # Check risk limits
   curl -s http://localhost:3001/trading/risk-limits | jq .
   
   # Temporarily adjust limits
   export MAX_POSITION_SIZE=0.2
   export DAILY_LOSS_LIMIT=1000
   ```

3. **Insufficient Balance**
   ```bash
   # Check account balances
   curl -s http://localhost:3001/trading/balances | jq .
   
   # Check minimum trade amounts
   echo $MIN_TRADE_AMOUNT
   ```

### Issue: Trading System Making Bad Decisions

**Symptoms:**
- Consistent losses
- Poor entry/exit timing
- Ignoring stop losses

**Diagnostic Steps:**

```bash
# Check strategy performance
curl -s http://localhost:3001/trading/performance | jq .

# Check recent trades
curl -s http://localhost:3001/trading/trades?status=completed | jq .

# Check strategy parameters
curl -s http://localhost:3001/trading/strategy-config | jq .
```

**Solutions:**

1. **Adjust Strategy Parameters**
   ```bash
   # Increase stop loss percentage
   export STOP_LOSS_PERCENTAGE=0.08
   
   # Reduce position sizes
   export MAX_POSITION_SIZE=0.05
   
   # Enable more conservative risk management
   export RISK_MANAGEMENT_ENABLED=true
   ```

2. **Switch to Paper Trading for Testing**
   ```bash
   export TRADING_MODE=paper
   export PAPER_TRADING=true
   ```

## Exchange API Problems

### Issue: Exchange API Connection Failures

**Symptoms:**
- "API key invalid" errors
- Connection timeout errors
- Rate limit exceeded errors

**Diagnostic Steps:**

```bash
# Test API connectivity
curl -s "https://api.binance.com/api/v3/ping"

# Check API key configuration
echo $BINANCE_API_KEY | head -c 10

# Test API authentication
node -e "
const ccxt = require('ccxt');
const exchange = new ccxt.binance({
  apiKey: process.env.BINANCE_API_KEY,
  secret: process.env.BINANCE_API_SECRET,
  sandbox: false
});
exchange.fetchBalance().then(console.log).catch(console.error);
"
```

**Solutions:**

1. **API Key Issues**
   ```bash
   # Verify API key format
   if [[ ${#BINANCE_API_KEY} -ne 64 ]]; then
     echo "Invalid API key length"
   fi
   
   # Check API key permissions
   # Log into exchange and verify permissions include:
   # - Read account information
   # - Place orders
   # - Access trading history
   ```

2. **Rate Limiting**
   ```bash
   # Reduce API call frequency
   export EXCHANGE_RATE_LIMIT=50
   export EXCHANGE_RETRY_DELAY=2000
   
   # Enable request queuing
   export EXCHANGE_ENABLE_RATE_LIMIT=true
   ```

3. **Network Issues**
   ```bash
   # Test network connectivity
   ping api.binance.com
   
   # Check firewall rules
   sudo iptables -L | grep -i binance
   
   # Use proxy if needed
   export PROXY_ENABLED=true
   export PROXY_HOST=proxy.example.com
   export PROXY_PORT=8080
   ```

### Issue: Exchange Data Feed Problems

**Symptoms:**
- Stale price data
- Missing market data
- WebSocket connection failures

**Diagnostic Steps:**

```bash
# Check WebSocket connections
netstat -an | grep ESTABLISHED | grep 443

# Test market data endpoints
curl -s "https://api.binance.com/api/v3/ticker/24hr?symbol=BTCUSDT"

# Check data freshness
curl -s http://localhost:3001/market-data/BTCUSDT | jq '.timestamp'
```

**Solutions:**

1. **WebSocket Reconnection**
   ```bash
   # Enable automatic reconnection
   export WEBSOCKET_AUTO_RECONNECT=true
   export WEBSOCKET_RECONNECT_DELAY=5000
   ```

2. **Fallback to REST API**
   ```bash
   # Disable WebSocket, use REST polling
   export USE_WEBSOCKET=false
   export MARKET_DATA_INTERVAL=1000
   ```

## Performance Issues

### Issue: High CPU Usage

**Symptoms:**
- CPU usage consistently above 80%
- Slow response times
- System becomes unresponsive

**Diagnostic Steps:**

```bash
# Monitor CPU usage
top -p $(pgrep -f memetrader)

# Check CPU usage by thread
ps -eLf | grep memetrader

# Profile application
node --prof app.js
node --prof-process isolate-*.log > profile.txt
```

**Solutions:**

1. **Optimize Event Loop**
   ```bash
   # Increase UV thread pool size
   export UV_THREADPOOL_SIZE=16
   
   # Reduce polling frequency
   export MARKET_DATA_INTERVAL=5000
   export HEALTH_CHECK_INTERVAL=60000
   ```

2. **Enable Clustering**
   ```javascript
   // Use cluster module for CPU-intensive tasks
   const cluster = require('cluster');
   const numCPUs = require('os').cpus().length;
   
   if (cluster.isMaster) {
     for (let i = 0; i < numCPUs; i++) {
       cluster.fork();
     }
   } else {
     // Worker process
     require('./app.js');
   }
   ```

### Issue: High Memory Usage

**Symptoms:**
- Memory usage continuously increasing
- Out of memory errors
- Application crashes with heap errors

**Diagnostic Steps:**

```bash
# Monitor memory usage
ps -o pid,ppid,pmem,rss,vsz,comm -p $(pgrep -f memetrader)

# Generate heap dump
kill -USR2 $(pgrep -f memetrader)

# Analyze heap dump
node --inspect-brk app.js
# Then use Chrome DevTools
```

**Solutions:**

1. **Increase Heap Size**
   ```bash
   export NODE_OPTIONS="--max-old-space-size=8192"
   ```

2. **Fix Memory Leaks**
   ```javascript
   // Clear intervals and timeouts
   process.on('SIGTERM', () => {
     clearInterval(marketDataInterval);
     clearTimeout(healthCheckTimeout);
   });
   
   // Remove event listeners
   process.removeAllListeners('uncaughtException');
   ```

3. **Implement Memory Monitoring**
   ```javascript
   setInterval(() => {
     const usage = process.memoryUsage();
     if (usage.heapUsed > 1024 * 1024 * 1024) { // 1GB
       console.warn('High memory usage detected:', usage);
       if (global.gc) global.gc();
     }
   }, 60000);
   ```

## Memory and Resource Problems

### Issue: File Descriptor Limits

**Symptoms:**
- "EMFILE: too many open files" errors
- Cannot create new connections
- Database connection failures

**Diagnostic Steps:**

```bash
# Check current file descriptor usage
lsof -p $(pgrep -f memetrader) | wc -l

# Check system limits
ulimit -n

# Check process limits
cat /proc/$(pgrep -f memetrader)/limits
```

**Solutions:**

```bash
# Increase file descriptor limits
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# For systemd services
echo "LimitNOFILE=65536" >> /etc/systemd/system/memetrader.service

# Temporary increase
ulimit -n 65536
```

### Issue: Disk Space Problems

**Symptoms:**
- "No space left on device" errors
- Log files not being written
- Database write failures

**Diagnostic Steps:**

```bash
# Check disk usage
df -h

# Check large files
du -sh /var/log/memetrader/*
du -sh ./databases/*

# Check inode usage
df -i
```

**Solutions:**

```bash
# Clean up log files
find /var/log/memetrader -name "*.log" -mtime +7 -delete

# Rotate logs
logrotate -f /etc/logrotate.d/memetrader

# Compress old logs
gzip /var/log/memetrader/*.log.1

# Move databases to larger partition
mv ./databases /var/lib/memetrader/
ln -s /var/lib/memetrader/databases ./databases
```

## Network and Connectivity Issues

### Issue: Network Timeouts

**Symptoms:**
- API requests timing out
- WebSocket disconnections
- Health check failures

**Diagnostic Steps:**

```bash
# Test network connectivity
ping -c 4 api.binance.com
traceroute api.binance.com

# Check DNS resolution
nslookup api.binance.com

# Test HTTP connectivity
curl -w "@curl-format.txt" -o /dev/null -s "https://api.binance.com/api/v3/ping"
```

**Solutions:**

1. **Increase Timeouts**
   ```bash
   export EXCHANGE_TIMEOUT=30000
   export API_TIMEOUT=15000
   export NETWORK_TIMEOUT=45000
   ```

2. **Configure Retry Logic**
   ```bash
   export RETRY_ATTEMPTS=5
   export RETRY_DELAY=2000
   export EXPONENTIAL_BACKOFF=true
   ```

3. **Use Connection Pooling**
   ```javascript
   const https = require('https');
   const agent = new https.Agent({
     keepAlive: true,
     maxSockets: 50,
     timeout: 30000
   });
   ```

### Issue: Firewall Blocking Connections

**Symptoms:**
- Connection refused errors
- Specific exchanges not accessible
- Intermittent connectivity issues

**Diagnostic Steps:**

```bash
# Check firewall status
sudo ufw status
sudo iptables -L

# Test specific ports
telnet api.binance.com 443
nc -zv api.binance.com 443

# Check proxy settings
echo $HTTP_PROXY
echo $HTTPS_PROXY
```

**Solutions:**

```bash
# Allow outbound HTTPS traffic
sudo ufw allow out 443

# Allow specific domains
sudo iptables -A OUTPUT -d api.binance.com -j ACCEPT

# Configure proxy
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080
```

## Security and Authentication Issues

### Issue: API Key Authentication Failures

**Symptoms:**
- "Invalid API key" errors
- "Signature verification failed" errors
- Unauthorized access errors

**Diagnostic Steps:**

```bash
# Check API key format
echo $BINANCE_API_KEY | wc -c

# Verify API secret
echo $BINANCE_API_SECRET | wc -c

# Test signature generation
node -e "
const crypto = require('crypto');
const secret = process.env.BINANCE_API_SECRET;
const query = 'timestamp=' + Date.now();
const signature = crypto.createHmac('sha256', secret).update(query).digest('hex');
console.log('Signature:', signature);
"
```

**Solutions:**

1. **Regenerate API Keys**
   - Log into exchange
   - Delete old API keys
   - Create new API keys with proper permissions
   - Update environment variables

2. **Check System Clock**
   ```bash
   # Synchronize system time
   sudo ntpdate -s time.nist.gov
   
   # Enable NTP
   sudo systemctl enable ntp
   sudo systemctl start ntp
   ```

3. **Verify Permissions**
   - Ensure API keys have trading permissions
   - Check IP whitelist settings
   - Verify withdrawal permissions if needed

### Issue: Encryption/Decryption Failures

**Symptoms:**
- Cannot decrypt stored credentials
- Encryption key errors
- Configuration decryption failures

**Diagnostic Steps:**

```bash
# Check master key file
ls -la /etc/memetrader/master.key

# Test encryption/decryption
node -e "
const crypto = require('crypto');
const key = require('fs').readFileSync('/etc/memetrader/master.key');
const cipher = crypto.createCipher('aes-256-gcm', key);
console.log('Encryption test passed');
"
```

**Solutions:**

```bash
# Regenerate master key
openssl rand -hex 32 > /etc/memetrader/master.key
chmod 600 /etc/memetrader/master.key

# Re-encrypt credentials
node scripts/re-encrypt-credentials.js
```

## Platform-Specific Issues

### Windows-Specific Issues

**Issue: Windows Service Won't Start**

```powershell
# Check service status
Get-Service "Meme Coin Trader"

# Check event logs
Get-EventLog -LogName Application -Source "Meme Coin Trader" -Newest 10

# Start service manually
Start-Service "Meme Coin Trader"

# Check service configuration
sc query "Meme Coin Trader"
```

**Issue: Path Length Limitations**

```powershell
# Enable long path support
New-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "LongPathsEnabled" -Value 1 -PropertyType DWORD -Force

# Use shorter paths
$env:TEMP = "C:\Temp"
$env:npm_config_cache = "C:\npm-cache"
```

### macOS-Specific Issues

**Issue: Code Signing Problems**

```bash
# Check code signature
codesign -dv --verbose=4 "/Applications/Meme Coin Trader.app"

# Re-sign application
codesign --force --deep --sign "Developer ID Application: Your Name" "/Applications/Meme Coin Trader.app"

# Check Gatekeeper
spctl --assess --type exec "/Applications/Meme Coin Trader.app"
```

**Issue: Keychain Access Problems**

```bash
# Unlock keychain
security unlock-keychain ~/Library/Keychains/login.keychain

# Add keychain to search list
security list-keychains -s ~/Library/Keychains/login.keychain

# Reset keychain permissions
security set-keychain-settings ~/Library/Keychains/login.keychain
```

### Linux-Specific Issues

**Issue: Systemd Service Problems**

```bash
# Check service status
systemctl status memetrader

# Check service logs
journalctl -u memetrader -f

# Reload service configuration
systemctl daemon-reload
systemctl restart memetrader

# Check service file
cat /etc/systemd/system/memetrader.service
```

**Issue: Permission Problems**

```bash
# Check file ownership
ls -la /var/lib/memetrader/

# Fix permissions
sudo chown -R memetrader:memetrader /var/lib/memetrader/
sudo chmod -R 755 /var/lib/memetrader/

# Check SELinux context
ls -Z /var/lib/memetrader/
```

## Monitoring and Logging Issues

### Issue: Logs Not Being Written

**Symptoms:**
- Empty log files
- Missing log entries
- Log rotation not working

**Diagnostic Steps:**

```bash
# Check log directory permissions
ls -la /var/log/memetrader/

# Check disk space
df -h /var/log/

# Test log writing
echo "Test log entry" >> /var/log/memetrader/app.log

# Check logrotate configuration
cat /etc/logrotate.d/memetrader
```

**Solutions:**

```bash
# Fix log directory permissions
sudo mkdir -p /var/log/memetrader
sudo chown memetrader:memetrader /var/log/memetrader
sudo chmod 755 /var/log/memetrader

# Configure logrotate
sudo tee /etc/logrotate.d/memetrader << EOF
/var/log/memetrader/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 memetrader memetrader
}
EOF
```

### Issue: Health Checks Failing

**Symptoms:**
- Health endpoint returns 500 errors
- Monitoring alerts triggering
- Load balancer removing instance

**Diagnostic Steps:**

```bash
# Test health endpoint
curl -v http://localhost:3001/health

# Check health check logs
grep "health" /var/log/memetrader/app.log

# Test individual components
curl http://localhost:3001/health/database
curl http://localhost:3001/health/trading
```

**Solutions:**

```bash
# Increase health check timeout
export HEALTH_CHECK_TIMEOUT=10000

# Disable problematic health checks temporarily
export HEALTH_CHECK_DATABASE=false

# Restart health monitoring
curl -X POST http://localhost:3001/admin/restart-health-checks
```

## Recovery Procedures

### Emergency Shutdown

```bash
#!/bin/bash
# emergency-shutdown.sh

echo "Initiating emergency shutdown..."

# Stop all trading activities
curl -X POST http://localhost:3001/trading/emergency-stop

# Close all open positions (if safe to do so)
curl -X POST http://localhost:3001/trading/close-all-positions

# Stop the application
pkill -TERM -f memetrader

# Wait for graceful shutdown
sleep 10

# Force kill if still running
pkill -KILL -f memetrader

echo "Emergency shutdown completed"
```

### Data Recovery

```bash
#!/bin/bash
# data-recovery.sh

BACKUP_DIR="/var/backups/memetrader"
RECOVERY_DIR="/var/lib/memetrader/recovery"

echo "Starting data recovery..."

# Create recovery directory
mkdir -p "$RECOVERY_DIR"

# Restore database from backup
if [[ -f "$BACKUP_DIR/trading_system.db.backup" ]]; then
    cp "$BACKUP_DIR/trading_system.db.backup" "$RECOVERY_DIR/trading_system.db"
    echo "Database restored from backup"
fi

# Restore configuration
if [[ -f "$BACKUP_DIR/config.json.backup" ]]; then
    cp "$BACKUP_DIR/config.json.backup" "$RECOVERY_DIR/config.json"
    echo "Configuration restored from backup"
fi

# Verify data integrity
node scripts/verify-data-integrity.js "$RECOVERY_DIR"

echo "Data recovery completed"
```

### System Recovery

```bash
#!/bin/bash
# system-recovery.sh

echo "Starting system recovery..."

# Check system resources
echo "Checking system resources..."
df -h
free -h

# Clean up temporary files
echo "Cleaning temporary files..."
rm -rf /tmp/memetrader-*
rm -rf /var/tmp/memetrader-*

# Restart dependent services
echo "Restarting services..."
systemctl restart mysql
systemctl restart nginx

# Restore from backup if needed
if [[ "$1" == "--restore-backup" ]]; then
    ./data-recovery.sh
fi

# Start application
echo "Starting application..."
systemctl start memetrader

# Verify recovery
sleep 30
curl -f http://localhost:3001/health

echo "System recovery completed"
```

### Rollback Procedure

```bash
#!/bin/bash
# rollback.sh

PREVIOUS_VERSION="$1"

if [[ -z "$PREVIOUS_VERSION" ]]; then
    echo "Usage: $0 <previous_version>"
    exit 1
fi

echo "Rolling back to version $PREVIOUS_VERSION..."

# Stop current application
systemctl stop memetrader

# Backup current version
mv /opt/memetrader /opt/memetrader.rollback

# Restore previous version
if [[ -d "/opt/memetrader.$PREVIOUS_VERSION" ]]; then
    mv "/opt/memetrader.$PREVIOUS_VERSION" /opt/memetrader
else
    echo "Previous version not found!"
    exit 1
fi

# Restore previous configuration
if [[ -f "/etc/memetrader/config.$PREVIOUS_VERSION.json" ]]; then
    cp "/etc/memetrader/config.$PREVIOUS_VERSION.json" /etc/memetrader/config.json
fi

# Start application
systemctl start memetrader

# Verify rollback
sleep 30
if curl -f http://localhost:3001/health; then
    echo "Rollback successful"
else
    echo "Rollback failed, manual intervention required"
    exit 1
fi
```

## Diagnostic Scripts

### Comprehensive System Check

Create `system-check.sh`:

```bash
#!/bin/bash
# system-check.sh

echo "=== Meme Coin Trader System Check ==="
echo "Timestamp: $(date)"
echo

# System Information
echo "=== System Information ==="
echo "OS: $(uname -a)"
echo "Uptime: $(uptime)"
echo "Load: $(cat /proc/loadavg)"
echo

# Resource Usage
echo "=== Resource Usage ==="
echo "Memory:"
free -h
echo
echo "Disk:"
df -h
echo
echo "CPU:"
top -bn1 | grep "Cpu(s)"
echo

# Application Status
echo "=== Application Status ==="
if pgrep -f memetrader > /dev/null; then
    echo "✅ Application is running"
    echo "PID: $(pgrep -f memetrader)"
    echo "Memory usage: $(ps -o pid,ppid,pmem,rss,vsz,comm -p $(pgrep -f memetrader))"
else
    echo "❌ Application is not running"
fi
echo

# Network Connectivity
echo "=== Network Connectivity ==="
if curl -s --max-time 5 https://api.binance.com/api/v3/ping > /dev/null; then
    echo "✅ Binance API accessible"
else
    echo "❌ Binance API not accessible"
fi

if curl -s --max-time 5 http://localhost:3001/health > /dev/null; then
    echo "✅ Health endpoint responding"
else
    echo "❌ Health endpoint not responding"
fi
echo

# Database Status
echo "=== Database Status ==="
if [[ -f "./databases/trading_system.db" ]]; then
    echo "✅ Database file exists"
    echo "Size: $(du -h ./databases/trading_system.db | cut -f1)"
    echo "Last modified: $(stat -c %y ./databases/trading_system.db)"
else
    echo "❌ Database file not found"
fi
echo

# Log Files
echo "=== Log Files ==="
if [[ -d "/var/log/memetrader" ]]; then
    echo "Log files:"
    ls -lah /var/log/memetrader/
    echo
    echo "Recent errors:"
    tail -n 5 /var/log/memetrader/error.log 2>/dev/null || echo "No error log found"
else
    echo "❌ Log directory not found"
fi
echo

# Configuration
echo "=== Configuration ==="
echo "NODE_ENV: ${NODE_ENV:-not set}"
echo "TRADING_MODE: ${TRADING_MODE:-not set}"
echo "DATABASE_TYPE: ${DATABASE_TYPE:-not set}"
echo "LOG_LEVEL: ${LOG_LEVEL:-not set}"
echo

echo "=== System Check Complete ==="
```

### Performance Monitoring Script

Create `performance-monitor.sh`:

```bash
#!/bin/bash
# performance-monitor.sh

INTERVAL=${1:-60}  # Default 60 seconds
DURATION=${2:-3600}  # Default 1 hour

echo "Starting performance monitoring for $DURATION seconds..."

END_TIME=$(($(date +%s) + DURATION))

while [[ $(date +%s) -lt $END_TIME ]]; do
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    PID=$(pgrep -f memetrader)
    
    if [[ -n "$PID" ]]; then
        # Get process stats
        STATS=$(ps -o pid,ppid,pcpu,pmem,rss,vsz -p $PID | tail -1)
        CPU=$(echo $STATS | awk '{print $3}')
        MEM=$(echo $STATS | awk '{print $4}')
        RSS=$(echo $STATS | awk '{print $5}')
        
        # Get system load
        LOAD=$(cat /proc/loadavg | awk '{print $1}')
        
        # Get memory info
        MEM_TOTAL=$(free | grep Mem | awk '{print $2}')
        MEM_USED=$(free | grep Mem | awk '{print $3}')
        MEM_PERCENT=$(echo "scale=2; $MEM_USED * 100 / $MEM_TOTAL" | bc)
        
        # Log performance data
        echo "$TIMESTAMP,CPU:$CPU%,MEM:$MEM%,RSS:${RSS}KB,LOAD:$LOAD,SYS_MEM:$MEM_PERCENT%" >> performance.log
        
        # Check for performance issues
        if (( $(echo "$CPU > 80" | bc -l) )); then
            echo "⚠️  High CPU usage detected: $CPU%"
        fi
        
        if (( $(echo "$MEM > 80" | bc -l) )); then
            echo "⚠️  High memory usage detected: $MEM%"
        fi
        
    else
        echo "$TIMESTAMP,APPLICATION_NOT_RUNNING" >> performance.log
        echo "❌ Application not running"
    fi
    
    sleep $INTERVAL
done

echo "Performance monitoring completed. Data saved to performance.log"
```

## Contact and Support

### Getting Help

1. **Check Documentation**: Review all deployment guides and troubleshooting steps
2. **Search Logs**: Look for specific error messages in application logs
3. **Community Forums**: Post questions with relevant log excerpts
4. **Issue Tracker**: Report bugs with reproduction steps
5. **Professional Support**: Contact support team for critical issues

### Information to Provide When Seeking Help

```bash
# System information
uname -a
node --version
npm --version

# Application version
cat package.json | grep version

# Environment configuration
env | grep -E "(NODE_ENV|DATABASE|TRADING)" | sort

# Recent logs
tail -n 50 /var/log/memetrader/error.log

# System resources
free -h
df -h
ps aux | grep memetrader
```

---

*This troubleshooting guide is part of the comprehensive production deployment documentation. Keep this guide updated as new issues are discovered and resolved.*