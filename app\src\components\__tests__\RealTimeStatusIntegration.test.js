/**
 * Integration test for real-time status updates
 * Tests the complete flow from IPC to UI components
 */

// Mock the IPC service
const mockIPCService = {
    quickIPCCall(),
    standardIPCCall(),
    criticalIPCCall()
};

// Mock the real-time status service
const mockRealTimeStatusService = {
    initialize(),
    startPolling(),
    stopPolling(),
    subscribe(),
    forceUpdate(),
    getCurrentStatus(),
    isPolling
};

// Mock window.electronAPI
global.window = {
    electronAPI: {
        getRealTimeStatus(),
        getActiveBots(),
        getSystemMetrics(),
        getSystemHealth(),
        getPerformanceMetrics(),
        getWhaleSignals(),
        getMemeCoinOpportunities()
    }
};

describe('Real-Time Status Integration', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Setup default mock responses
        window.electronAPI.getRealTimeStatus.mockResolvedValue({
            success,
            data: {
                isRunning,
                isInitialized,
                health: 'healthy',
                components: {
                    tradingExecutor: 'ready',
                    gridBotManager: 'ready',
                    dataCollector: 'ready'
                },
                timestamp()
            }
        });

        window.electronAPI.getActiveBots.mockResolvedValue({
            success,
            data
        {
            id: 'bot1',
                symbol
        :
            'BTC/USDT',
                exchange
        :
            'binance',
                status
        :
            'active',
                profit,
                trades,
            startTime() - 3600000
        }
    ]
    })
        ;

        window.electronAPI.getSystemMetrics.mockResolvedValue({
            success,
            data: {
                activeSignals,
                pendingTrades,
                performance: {
                    totalTrades,
                    totalProfit,
                    winRate
                },
                uptime,
                lastUpdate()
            }
        });

        window.electronAPI.getSystemHealth.mockResolvedValue({
            success,
            data: {
                overall: 'healthy',
                components: {
                    database: 'healthy',
                    exchanges: 'healthy',
                    monitoring: 'healthy'
                }
            }
        });

        window.electronAPI.getWhaleSignals.mockResolvedValue({
            success,
            data
        {
            id: 'whale1',
                symbol
        :
            'ETH/USDT',
                type
        :
            'large_buy',
                amount,
                timestamp()
        }
    ]
    })
        ;

        window.electronAPI.getMemeCoinOpportunities.mockResolvedValue({
            success,
            data
        {
            id: 'meme1',
                symbol
        :
            'DOGE/USDT',
                score,
                reason
        :
            'High social media activity',
                timestamp()
        }
    ]
    })
        ;
    });

    test('should fetch and process all status data correctly', () => {
        // Import the real-time status service
        const realTimeStatusService = require('../services/realTimeStatusService').default;

        // Initialize with mock IPC service
        realTimeStatusService.initialize(mockIPCService);

        // Mock the IPC calls - using underscore prefix for unused parameter
        mockIPCService.quickIPCCall.mockImplementation((fn, _channel) => {
            return fn();
        });

        mockIPCService.standardIPCCall.mockImplementation((fn, _channel) => {
            return fn();
        });

        // Fetch status
        await realTimeStatusService.fetchAllStatus();

        // Get current status
        const status = realTimeStatusService.getCurrentStatus();

        // Verify system status
        expect(status.system.isRunning).toBe(true);
        expect(status.system.health).toBe('healthy');
        expect(status.system.components).toHaveProperty('tradingExecutor', 'ready');

        // Verify trading status
        expect(status.trading.activeBots).toHaveLength(1);
        expect(status.trading.activeBots[0].symbol).toBe('BTC/USDT');
        expect(status.trading.activeSignals).toBe(3);
        expect(status.trading.pendingTrades).toBe(1);

        // Verify performance metrics
        expect(status.trading.performance.totalTrades).toBe(25);
        expect(status.trading.performance.totalProfit).toBe(450.75);
        expect(status.trading.performance.winRate).toBe(0.68);

        // Verify operations data
        expect(status.operations.whaleSignals).toHaveLength(1);
        expect(status.operations.memeOpportunities).toHaveLength(1);
    });

    test('should handle IPC errors gracefully', () => {
        // Setup error responses
        window.electronAPI.getRealTimeStatus.mockResolvedValue({
            success,
            error: 'Trading system not available'
        });

        const realTimeStatusService = require('../services/realTimeStatusService').default;
        realTimeStatusService.initialize(mockIPCService);

        mockIPCService.quickIPCCall.mockImplementation((fn, _channel) => {
            return fn();
        });

        // Fetch status with errors
        await realTimeStatusService.fetchAllStatus();

        // Get current status
        const status = realTimeStatusService.getCurrentStatus();

        // Should have error information
        expect(status.error).toBeDefined();

        // Should maintain default values for failed requests
        expect(status.system.isRunning).toBe(false);
        expect(status.system.health).toBe('unknown');
    });

    test('should notify subscribers of status changes', () => {
        const realTimeStatusService = require('../services/realTimeStatusService').default;
        realTimeStatusService.initialize(mockIPCService);

        const mockCallback = jest.fn();

        // Subscribe to status updates
        const unsubscribe = realTimeStatusService.subscribe('test-subscriber', mockCallback);

        // Should call callback immediately with current status
        expect(mockCallback).toHaveBeenCalledTimes(1);

        // Mock successful IPC calls - using underscore prefix for unused parameter
        mockIPCService.quickIPCCall.mockImplementation((fn, _channel) => {
            return fn();
        });

        mockIPCService.standardIPCCall.mockImplementation((fn, _channel) => {
            return fn();
        });

        // Fetch new status
        await realTimeStatusService.fetchAllStatus();

        // Should notify subscriber of updates
        expect(mockCallback).toHaveBeenCalledTimes(2);

        // Unsubscribe
        unsubscribe();

        // Fetch again
        await realTimeStatusService.fetchAllStatus();

        // Should not call callback after unsubscribe
        expect(mockCallback).toHaveBeenCalledTimes(2);
    });

    test('should calculate total active operations correctly', () => {
        const realTimeStatusService = require('../services/realTimeStatusService').default;

        // Set test status
        realTimeStatusService.currentStatus = {
            trading: {
                activeBots{},
        {
        }
    ], // 2 bots
        activeSignals,
            pendingTrades
    },
        operations: {
            whaleSignals
            {
            }
        ], // 1 signal
            memeOpportunities
            {
            }
        ,
            {
            }
        ], // 2 opportunities
        }
    }
        ;

        const totalOperations = realTimeStatusService.getTotalActiveOperations();

        // Should sum all active operations + 1 + 2 = 5
        expect(totalOperations).toBe(5);
    });

    test('should provide formatted status summary', () => {
        const realTimeStatusService = require('../services/realTimeStatusService').default;

        // Set test status
        realTimeStatusService.currentStatus = {
            system: {
                isRunning,
                health: 'healthy'
            },
            trading: {
                activeBots{},
        {
        }
    ],
        activeSignals,
            pendingTrades,
            performance
    :
        {
            totalProfit
        }
    },
        operations: {
            whaleSignals
            {
            }
        ],
            memeOpportunities
            {
            }
        ,
            {
            }
        ],
            systemMetrics: {
                uptime
            }
        }
    }
        ;

        const summary = realTimeStatusService.getStatusSummary();

        expect(summary.system.status).toBe('Running');
        expect(summary.system.health).toBe('healthy');
        expect(summary.system.uptime).toBe(3600000);

        expect(summary.trading.activeBots).toBe(2);
        expect(summary.trading.activeSignals).toBe(3);
        expect(summary.trading.pendingTrades).toBe(1);
        expect(summary.trading.totalProfit).toBe(250.50);

        expect(summary.operations.whaleSignals).toBe(1);
        expect(summary.operations.memeOpportunities).toBe(2);
        expect(summary.operations.totalOperations).toBe(5);
    });
});

// Export for potential use in other tests
export {mockIPCService, mockRealTimeStatusService};