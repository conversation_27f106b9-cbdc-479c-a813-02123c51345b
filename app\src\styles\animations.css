/* 60fps Optimized Animation System for Trading Interface */

/* Performance Base - GPU Acceleration */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
    will-change: transform, opacity;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .particle-system,
    .complex-animation {
        display: none !important;
    }
}

/* Optimized Particle System Background */
@keyframes floatingParticles {
    0% {
        transform: translate3d(0px, 0px, 0) rotate(0deg);
        opacity: 0;
        will-change: transform, opacity;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translate3d(50px, -100vh, 0) rotate(360deg);
        opacity: 0;
        will-change: auto;
    }
}

@keyframes particleGlow {
    0%, 100% {
        box-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
    }
    50% {
        box-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
    }
}

.particle {
    position: fixed;
    width: 3px;
    height: 3px;
    border-radius: 50%;
    pointer-events: none;
    z-index: 0;
    animation: floatingParticles linear infinite, particleGlow 2s ease-in-out infinite;
}

.particle-cyan {
    color: #00eaff;
}

.particle-purple {
    color: #a259ff;
}

.particle-gold {
    color: #ffc107;
}

.particle-green {
    color: #4caf50;
}

.particle-pink {
    color: #ff6b6b;
}

/* Optimized Liquid Morphing Effects */
@keyframes liquidMorph {
    0%, 100% {
        border-radius: 20px 40px 30px 50px / 25px 30px 40px 20px;
        will-change: border-radius;
    }
    25% {
        border-radius: 50px 20px 40px 30px / 30px 50px 20px 40px;
    }
    50% {
        border-radius: 30px 50px 20px 40px / 40px 20px 50px 30px;
    }
    75% {
        border-radius: 40px 30px 50px 20px / 20px 40px 30px 50px;
    }
}

.liquid-morph {
    animation: liquidMorph 8s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    backface-visibility: hidden;
}

/* Holographic Gradients */
.holographic-gradient {
    background: linear-gradient(
            45deg,
            #ff006e,
            #8338ec,
            #3a86ff,
            #06ffa5,
            #ffbe0b,
            #fb5607,
            #ff006e
    );
    background-size: 400% 400%;
    animation: holographicShift 3s ease infinite;
}

@keyframes holographicShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Chromatic Aberration */
.chromatic-aberration {
    position: relative;
}

.chromatic-aberration::before,
.chromatic-aberration::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.chromatic-aberration::before {
    color: #ff0000;
    transform: translateX(-2px);
    opacity: 0.7;
    animation: glitchRed 2s infinite;
}

.chromatic-aberration::after {
    color: #00ffff;
    transform: translateX(2px);
    opacity: 0.7;
    animation: glitchBlue 2s infinite;
}

@keyframes glitchRed {
    0%, 100% {
        transform: translateX(-2px);
    }
    50% {
        transform: translateX(-4px);
    }
}

@keyframes glitchBlue {
    0%, 100% {
        transform: translateX(2px);
    }
    50% {
        transform: translateX(4px);
    }
}

/* Magnetic Hover Effects */
.magnetic-hover {
    transition: all 0.3s cubic-bezier(0.23, 1, 0.320, 1);
    transform-origin: center;
}

.magnetic-hover:hover {
    transform: scale(1.05) translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 234, 255, 0.3),
    0 0 60px rgba(162, 89, 255, 0.2),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
}

/* 3D Depth Effects */
.depth-3d {
    transform-style: preserve-3d;
    perspective: 1000px;
}

.depth-layer-1 {
    transform: translateZ(10px);
}

.depth-layer-2 {
    transform: translateZ(20px);
}

.depth-layer-3 {
    transform: translateZ(30px);
}

/* Neural Network Pulse */
@keyframes neuralPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

.neural-node {
    animation: neuralPulse 2s ease-in-out infinite;
}

.neural-connection {
    background: linear-gradient(90deg, transparent, #00eaff, transparent);
    background-size: 200% 100%;
    animation: connectionFlow 2s linear infinite;
}

@keyframes connectionFlow {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Quantum State Effects */
.quantum-state {
    position: relative;
    overflow: hidden;
}

.quantum-state::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(0, 234, 255, 0.1) 0%, transparent 70%);
    animation: quantumRotation 10s linear infinite;
}

@keyframes quantumRotation {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Volumetric Lighting */
.volumetric-light {
    position: relative;
    overflow: hidden;
}

.volumetric-light::after {
    content: '';
    position: absolute;
    top: -100%;
    left: -100%;
    width: 300%;
    height: 300%;
    background: conic-gradient(
            from 0deg,
            transparent,
            rgba(255, 255, 255, 0.1),
            transparent,
            rgba(0, 234, 255, 0.1),
            transparent
    );
    animation: lightSweep 8s linear infinite;
    pointer-events: none;
}

@keyframes lightSweep {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Glass Style 2.0 */
.glass-style-advanced {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
}

.glass-style-advanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 25%,
    transparent 75%,
    rgba(255, 255, 255, 0.1) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.glass-style-advanced:hover::before {
    opacity: 1;
}

/* Energy Field Effects */
@keyframes energyField {
    0%, 100% {
        box-shadow: 0 0 20px rgba(0, 234, 255, 0.4),
        0 0 40px rgba(0, 234, 255, 0.2),
        0 0 60px rgba(0, 234, 255, 0.1);
    }
    50% {
        box-shadow: 0 0 30px rgba(162, 89, 255, 0.4),
        0 0 60px rgba(162, 89, 255, 0.2),
        0 0 90px rgba(162, 89, 255, 0.1);
    }
}

.energy-field {
    animation: energyField 3s ease-in-out infinite;
}

/* Crystalline Structures */
.crystal-facet {
    clip-path: polygon(50% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
    background: linear-gradient(135deg,
    rgba(0, 234, 255, 0.2) 0%,
    rgba(162, 89, 255, 0.2) 50%,
    rgba(255, 107, 107, 0.2) 100%
    );
    animation: crystalGlow 4s ease-in-out infinite;
}

@keyframes crystalGlow {
    0%, 100% {
        filter: brightness(1) contrast(1);
        transform: scale(1);
    }
    50% {
        filter: brightness(1.2) contrast(1.1);
        transform: scale(1.02);
    }
}

/* Liquid Metal Effects */
.liquid-metal {
    background: linear-gradient(45deg,
    #434343 0%,
    #000000 25%,
    #434343 50%,
    #000000 75%,
    #434343 100%
    );
    background-size: 400% 400%;
    animation: liquidMetalFlow 6s ease-in-out infinite;
    position: relative;
}

.liquid-metal::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 70%
    );
    background-size: 200% 200%;
    animation: metalSheen 3s linear infinite;
}

@keyframes liquidMetalFlow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes metalSheen {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Gesture Response Animations */
.gesture-responsive {
    transition: all 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.gesture-responsive.pressed {
    transform: scale(0.95) translateY(2px);
    filter: brightness(1.2);
}

.gesture-responsive.released {
    transform: scale(1.05) translateY(-2px);
    filter: brightness(0.9);
}

/* Performance Optimized Animations */
/* GPU acceleration is already defined at the top of the file */

/* Responsive Animation Controls */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* High Performance Mode */
.performance-mode * {
    animation: none !important;
    transition: none !important;
}

.performance-mode .particle {
    display: none;
}