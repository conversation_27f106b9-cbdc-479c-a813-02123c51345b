{"issues": [{"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\webpack.config.production.js", "line": 224, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\webpack.config.performance.js", "line": 166, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js", "message": "File has 696 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\dependencies.js", "message": "File has 534 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js", "message": "File has 821 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\automatic-failure-recovery.js", "message": "File has 717 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js", "line": 184, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js", "line": 185, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\enhanced-error-handling-recovery.test.js", "message": "File has 633 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\comprehensive-error-handling.test.js", "line": 202, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\comprehensive-error-handling.test.js", "line": 278, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\comprehensive-error-handling.test.js", "line": 279, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\unit\\simple-ipc-communication.test.js", "line": 115, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\unit\\simple-ipc-communication.test.js", "line": 135, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\unit\\simple-ipc-communication.test.js", "line": 136, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\unit\\simple-ipc-communication.test.js", "line": 137, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\unit\\simple-ipc-communication.test.js", "line": 138, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\unit\\ipc-communication.test.js", "line": 83, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\e2e\\application-workflow.test.js", "message": "File has 613 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\e2e\\application-workflow.test.js", "line": 140, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\e2e\\application-workflow.test.js", "line": 141, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\e2e\\application-workflow.test.js", "line": 193, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\e2e\\application-workflow.test.js", "line": 194, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\e2e\\application-workflow.test.js", "line": 590, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\e2e\\application-workflow.test.js", "line": 591, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\e2e\\application-workflow.test.js", "line": 603, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\e2e\\application-workflow.test.js", "line": 604, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\tests\\test-mysql-connection.js", "line": 8, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\tests\\test-database-integration.js", "message": "File has 1157 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\tests\\comprehensive-system-validation.test.js", "message": "File has 788 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\tests\\backtesting-system.test.js", "message": "File has 585 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\tests\\integration\\trading-system.test.js", "message": "File has 544 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\tests\\integration\\trading-system.test.js", "line": 31, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\tests\\integration\\trading-system.test.js", "line": 32, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\scripts\\performance-optimizer.js", "message": "File has 639 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\monitoring\\status-reporter.js", "message": "File has 555 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\monitoring\\HealthMonitor.js", "message": "File has 942 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\monitoring\\health-monitoring-system.js", "message": "File has 1363 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\monitoring\\health-monitor.js", "message": "File has 754 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\monitoring\\health-dashboard.js", "message": "File has 579 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\monitoring\\health-cli.js", "message": "File has 549 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\examples\\ccxt-decimal-example.js", "line": 63, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\examples\\ccxt-decimal-example.js", "line": 64, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\validation\\CoinAgeValidator.js", "message": "File has 1156 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\ValidateTradingEngines.js", "line": 43, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\ValidateTradingEngines.js", "line": 44, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\TradingExecutor.js", "message": "File has 883 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\FuturesGridManager.js", "message": "File has 742 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\AutoProfitStopManager.js", "message": "File has 1140 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\whaletrader\\Elite.WhaleTracker.js", "message": "File has 780 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\orchestration\\TradingOrchestrator.js", "message": "File has 2608 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\orchestration\\event-coordinator.js", "message": "File has 681 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\orchestration\\enhanced-component-initializer.js", "message": "File has 781 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\orchestration\\component-initializer.js", "message": "File has 595 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\bots\\UnifiedGridBotEngine.js", "message": "File has 1222 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\trading\\bots\\FuturesGridBot.js", "message": "File has 1017 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\TradingAPIIntegrator.js", "message": "File has 508 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\APIResourcePoolManager.js", "message": "File has 592 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\validation\\ValidationTest.js", "line": 179, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\validation\\ValidationTest.js", "line": 180, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\validation\\ValidationTest.js", "line": 193, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\validation\\ValidationTest.js", "line": 283, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\validation\\InputValidator.js", "message": "File has 631 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\validation\\InputValidator.js", "line": 476, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\validation\\InputValidator.js", "line": 477, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\utils\\ErrorHandlingUtils.js", "line": 197, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\secure-credential-manager.js", "message": "File has 807 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\health-monitor.js", "message": "File has 683 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\CredentialManager.js", "message": "File has 583 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\risk\\PositionSizingManager.js", "message": "File has 747 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\risk\\LiquidationProtector.js", "message": "File has 956 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\risk\\LiquidationProtector.js", "line": 918, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\risk\\LiquidationProtector.js", "line": 919, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\risk\\LiquidationProtector.js", "line": 920, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\risk\\LiquidationProtector.js", "line": 921, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\recovery\\RecoveryManager.js", "message": "File has 578 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\TradingSystemErrorHandler.js", "message": "File has 1042 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\shared\\security\\error-handling\\ErrorHandler.js", "message": "File has 828 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\protection\\ExitLiquidityProtector.js", "message": "File has 746 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\optimization\\performance-monitor.js", "message": "File has 895 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\monitoring\\TradingPerformanceMonitor.js", "message": "File has 626 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\monitoring\\PerformanceMonitor.js", "message": "File has 742 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\monitoring\\performance-monitor.js", "message": "File has 569 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\monitoring\\AnalyticsDashboard.js", "message": "File has 870 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\monitoring\\AnalyticsDashboard.js", "line": 727, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\exchange\\ProductionExchangeConnector.js", "message": "File has 515 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\data-collection\\NewListingDetector.js", "message": "File has 991 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\data-collection\\HistoricalPriceTracker.js", "message": "File has 674 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\data-collection\\backtesting.js", "message": "File has 1240 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\constants\\constants.js", "line": 236, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\constants\\constants.js", "line": 237, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\config\\ConfigurationManager.js", "line": 29, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\config\\ConfigurationManager.js", "line": 30, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\config\\configuration-loader.js", "message": "File has 619 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\ccxt\\engines\\CCXT-Exchange-Manager.js", "message": "File has 517 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\backtesting\\BacktestingIntegrator.js", "message": "File has 527 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\backtesting\\BacktestingEngine.js", "message": "File has 813 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\analysis\\SocialSentimentAnalyzer.js", "message": "File has 1371 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\analysis\\SmartMoneyDetector.js", "message": "File has 676 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\analysis\\PumpDetectionEngine.js", "message": "File has 1192 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\analysis\\NewCoinDecisionEngine.js", "message": "File has 716 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\analysis\\MemeCoinPatternAnalyzer.js", "message": "File has 1485 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\analysis\\HistoricalPriceTracker.js", "message": "File has 856 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\analysis\\ExitLiquidityProtector.js", "message": "File has 802 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\analysis\\EntryTimingEngine.js", "message": "File has 718 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\analysis\\ComprehensiveWalletTracker.js", "message": "File has 817 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\engines\\analysis\\BlockchainTransactionAnalyzer.js", "message": "File has 730 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 128, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 135, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 144, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 151, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 160, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 167, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 176, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 183, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 192, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 199, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 208, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\database\\migrations\\001-enhanced-features.js", "line": 215, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\data\\UnifiedDatabaseInitializer.js", "message": "File has 626 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\data\\transaction-manager.js", "message": "File has 718 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\data\\DatabaseManager.js", "message": "File has 662 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\data\\DatabaseManager.js", "line": 64, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\data\\databases\\unified-database-init.js", "message": "File has 645 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\data\\databases\\granular_sql_debug.js", "message": "File has 532 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-test-utils.js", "message": "File has 562 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-test-utils.js", "line": 92, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-test-utils.js", "line": 93, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-test-utils.js", "line": 98, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-test-utils.js", "line": 99, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-test-utils.js", "line": 153, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-test-utils.js", "line": 154, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-test-utils.js", "line": 247, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-test-utils.js", "line": 251, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-test-utils.js", "line": 252, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-test-utils.js", "line": 357, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-cli.js", "line": 248, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\config-cli.js", "line": 249, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\components\\PortfolioMonitor.js", "message": "File has 842 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\components\\OpportunityScanner.js", "message": "File has 801 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\components\\OpportunityScanner.js", "line": 14, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\components\\ExchangeHealthMonitor.js", "message": "File has 612 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\components\\DrawdownAnalyzer.js", "message": "File has 650 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\analysis\\RuleBasedSentiment.js", "message": "File has 647 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\analysis\\PerformanceTracker.old.js", "message": "File has 671 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\ai\\StrategyOptimizer.js", "message": "File has 1380 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\ai\\CryptoDiscoveryEngine.js", "message": "File has 1129 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\ai\\AutonomousTrader.js", "message": "File has 691 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\ai\\AutonomousTrader.js", "line": 4, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\ipc\\ipc-validation.test.js", "message": "File has 510 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\ipc\\ipc-end-to-end.test.js", "message": "File has 560 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\ipc\\ipc-end-to-end-test.js", "message": "File has 720 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\ipc\\ipc-end-to-end-test.js", "line": 419, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\ipc\\ipc-end-to-end-comprehensive.test.js", "message": "File has 514 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\ipc\\comprehensive-ipc-test.js", "message": "File has 628 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\integration\\start-button-real-time-integration.test.js", "message": "File has 638 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\integration\\specialized-error-boundaries.test.js", "message": "File has 617 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\integration\\error-reporting-backend.test.js", "message": "File has 583 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\integration\\error-boundary-integration.test.js", "message": "File has 614 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\error-handling\\comprehensive-error-handling.test.js", "message": "File has 573 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\e2e\\startup-sequence-validation.test.js", "message": "File has 743 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\e2e\\simple-workflow.test.js", "line": 40, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\e2e\\simple-workflow.test.js", "line": 41, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\e2e\\simple-workflow.test.js", "line": 53, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\e2e\\simple-workflow.test.js", "line": 54, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\e2e\\error-handling-workflow.test.js", "message": "File has 930 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\e2e\\complete-user-workflow.test.js", "message": "File has 820 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\e2e\\complete-end-to-end-validation.test.js", "message": "File has 547 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\utils\\SystemWideErrorHandler.js", "message": "File has 734 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\utils\\ElectronAPITester.js", "message": "File has 858 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\utils\\ElectronAPITester.js", "line": 232, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\utils\\ComponentRecoveryManager.js", "message": "File has 632 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\utils\\AnimationOptimizer.js", "message": "File has 505 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\services\\realTimeStatusService.js", "message": "File has 579 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\services\\ipcService.js", "message": "File has 1459 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\api\\server.js", "line": 265, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 143, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 150, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 159, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 168, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 175, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 179, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 190, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 194, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 202, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 229, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 232, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 254, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 255, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 258, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 259, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 260, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 261, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 281, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 284, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 285, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 286, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 290, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 291, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 293, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 295, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 299, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 300, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 301, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 304, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 305, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 306, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 313, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 314, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 320, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 321, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 324, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 325, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 326, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 327, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 329, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 330, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 333, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 334, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 338, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 339, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\verify-references.js", "line": 346, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "message": "File has 585 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 47, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 48, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 80, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 81, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 83, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 84, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 96, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 166, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 170, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 203, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 212, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 223, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 229, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 240, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 254, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 258, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 279, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 284, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 293, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 297, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 317, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 322, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 331, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 335, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 355, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 361, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 370, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 374, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 394, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 399, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 408, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 412, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 433, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 438, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 447, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 451, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 481, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 488, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 496, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 500, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 501, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 519, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 520, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 521, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 522, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 523, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 524, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 527, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 532, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 537, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 539, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 545, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 547, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\validate-application-integration.js", "line": 570, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\fix-console-statements.js", "line": 12, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\fix-console-statements.js", "line": 14, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\scripts\\fix-console-statements.js", "line": 16, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\public\\electron.js", "line": 31, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\components\\Dashboard.test.jsx", "line": 366, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\components\\ApplicationErrorBoundary.test.jsx", "line": 270, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\components\\ApplicationErrorBoundary.test.jsx", "line": 271, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\components\\ApplicationErrorBoundary.test.jsx", "line": 287, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\__tests__\\components\\ApplicationErrorBoundary.test.jsx", "line": 288, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\WhaleTracker.jsx", "message": "File has 517 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\UltimateDashboard.jsx", "message": "File has 632 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\UltimateDashboard.jsx", "line": 469, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\UltimateDashboard.jsx", "line": 482, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\UltimateDashboard.jsx", "line": 511, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\UltimateDashboard.jsx", "line": 512, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\SystemDiagnostics.jsx", "message": "File has 722 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\SystemDiagnostics.jsx", "line": 604, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\SystemDiagnostics.jsx", "line": 663, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\SystemDiagnostics.jsx", "line": 680, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\SystemDiagnostics.jsx", "line": 697, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\StartupProgressPanel.jsx", "message": "File has 668 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\SettingsModal.jsx", "message": "File has 639 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\RunningOperationsMonitor.jsx", "message": "File has 536 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\RunningOperationsMonitor.jsx", "line": 307, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\RunningOperationsMonitor.jsx", "line": 344, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\PortfolioTracker.jsx", "message": "File has 808 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\PortfolioTracker.jsx", "line": 412, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\PortfolioTracker.jsx", "line": 413, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\PortfolioTracker.jsx", "line": 513, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\PortfolioTracker.jsx", "line": 525, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\PortfolioTracker.jsx", "line": 530, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\MarketAnalysis.jsx", "message": "File has 910 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\MarketAnalysis.jsx", "line": 494, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\MarketAnalysis.jsx", "line": 496, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\MarketAnalysis.jsx", "line": 896, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\MarketAnalysis.jsx", "line": 900, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\LazyComponents.jsx", "message": "File has 514 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\ErrorNotificationSystem.jsx", "message": "File has 525 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\Dashboard.jsx", "message": "File has 926 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\Dashboard.jsx", "line": 646, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\Dashboard.jsx", "line": 660, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\Dashboard.jsx", "line": 832, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\Dashboard.jsx", "line": 846, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\Dashboard.jsx", "line": 881, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\Dashboard.jsx", "line": 882, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\CrossExchangePortfolio.jsx", "message": "File has 1034 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\CriticalWorkflowErrorBoundary.jsx", "message": "File has 772 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\CoinManager.jsx", "message": "File has 577 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\AutonomousDashboard.jsx", "message": "File has 943 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\AutonomousDashboard.jsx", "line": 599, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\AutonomousDashboard.jsx", "line": 613, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\AutonomousDashboard.jsx", "line": 665, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\ArbitrageOpportunityPanel.jsx", "message": "File has 1213 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\ApplicationErrorBoundary.jsx", "message": "File has 841 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\AdvancedChart.jsx", "line": 237, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\AdvancedChart.jsx", "line": 250, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\AdvancedChart.jsx", "line": 278, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\AdvancedChart.jsx", "line": 297, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\AdvancedChart.jsx", "line": 310, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\components\\AdvancedChart.jsx", "line": 333, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\codebase.issues.md", "line": 14, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\codebase.issues.md", "line": 16, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\codebase.issues.md", "line": 73, "message": "TODO/FIXME comment found", "severity": "low", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\codebase.issues.md", "line": 91, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\codebase.issues.md", "line": 96, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\codebase.issues.md", "line": 101, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\codebase.issues.md", "line": 201, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\codebase.issues.md", "line": 206, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\monitoring\\README.md", "line": 156, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\docs\\DATABASE_SETUP.md", "line": 56, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\docs\\DATABASE_SETUP.md", "line": 146, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\docs\\DATABASE_SETUP.md", "line": 150, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\docs\\DATABASE_SETUP.md", "line": 273, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\docs\\DATABASE_INITIALIZATION.md", "line": 103, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\docs\\DATABASE_INITIALIZATION.md", "line": 104, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\docs\\DATABASE_INITIALIZATION.md", "line": 117, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\docs\\DATABASE_INITIALIZATION.md", "line": 118, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\docs\\DATABASE_INITIALIZATION.md", "line": 148, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\docs\\COMPREHENSIVE_ERROR_HANDLING.md", "line": 312, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\README.md", "line": 319, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\README.md", "line": 323, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\CONFIGURATION_LOADING.md", "line": 45, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\src\\docs\\IPC_USAGE.md", "line": 46, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\TradingExecutor.js.backup.txt", "message": "File has 731 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\TradingExecutor.js.backup.txt", "line": 242, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\TradingExecutor.js.backup.txt", "line": 323, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\TradingExecutor.js.backup.txt", "line": 719, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\ProductionTradingExecutor.js.backup.txt", "message": "File has 885 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "message": "File has 717 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 55, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 70, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 106, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 160, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 173, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 199, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 217, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 243, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 248, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 268, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 271, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 483, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 495, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 552, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 580, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 692, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\backup\\backup.trading\\enhanced-trading-executor.js.backup.txt", "line": 709, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\package-lock.json", "message": "File has 35448 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\package.json", "line": 41, "message": "console.log statement in production code", "severity": "medium", "type": "style", "autoFixable": true}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\ipc-direct-test-results.json", "message": "File has 4334 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\monitoring\\package-lock.json", "message": "File has 4805 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\config\\schemas\\config-schemas.json", "message": "File has 732 lines (>500 limit)", "severity": "medium", "type": "maintainability"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\monitoring\\prometheus.yml", "line": 89, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}, {"file": "C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\monitoring\\prometheus.yml", "line": 96, "message": "Potential hardcoded secret detected", "severity": "critical", "type": "security"}], "quality": {"score": 0, "complexity": 5.197852882703773, "duplication": 2.9025194033560817, "testCoverage": 100, "maintainabilityIndex": 0}, "maintainability": "Critical", "report": "# Project Analysis Report\n\n## Overview\n- **Quality Score**: 0.0/100\n- **Maintainability**: Critical (0)\n- **Test Coverage**: 100.0%\n\n## Issues Summary\n- **Critical**: 113\n- **High**: 0\n- **Medium**: 276\n- **Low**: 1\n\n## Top Issues\n- C:\\Users\\<USER>\\Documents\\electronTrader\\app\\webpack.config.production.js:224 - console.log statement in production code (medium)\n- C:\\Users\\<USER>\\Documents\\electronTrader\\app\\webpack.config.performance.js:166 - console.log statement in production code (medium)\n- C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\TradingOrchestrator.js:? - File has 696 lines (>500 limit) (medium)\n- C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\dependencies.js:? - File has 534 lines (>500 limit) (medium)\n- C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\autonomous-startup.js:? - File has 821 lines (>500 limit) (medium)\n- C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\automatic-failure-recovery.js:? - File has 717 lines (>500 limit) (medium)\n- C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:184 - Potential hardcoded secret detected (critical)\n- C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\error-handling.test.js:185 - Potential hardcoded secret detected (critical)\n- C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\enhanced-error-handling-recovery.test.js:? - File has 633 lines (>500 limit) (medium)\n- C:\\Users\\<USER>\\Documents\\electronTrader\\app\\trading\\__tests__\\comprehensive-error-handling.test.js:202 - Potential hardcoded secret detected (critical)\n\n## Recommendations\n- Address critical and high-severity issues\n- Review security issues immediately\n\n---\n*Generated by Context Engine*\n"}