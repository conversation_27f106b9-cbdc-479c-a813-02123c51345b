/**
 * Exchange color constants for consistent styling across the application
 * @type {Record<string, string>}
 */
export const EXCHANGE_COLORS = {
    binance: '#f3ba2f',
    bybit: '#f7931a',
    okx: '#1890ff',
    kucoin: '#24d369',
    huobi: '#2cadf3',
    kraken: '#5741d9',
    coinbase: '#0052ff',
    gateio: '#23af91',
    bitfinex: '#87d068',
    bitmex: '#ee3b3b',
    default: '#666666'
};

/**
 * Get color for a specific exchange
 * @param {string} exchange - Exchange name
 * @returns {string} Hex color code
 */
export const getExchangeColor = (exchange) => {
    return EXCHANGE_COLORS[exchange?.toLowerCase()] || EXCHANGE_COLORS.default;
};
