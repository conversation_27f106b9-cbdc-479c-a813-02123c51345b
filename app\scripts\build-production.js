#!/usr/bin/env node

/**
 * Production Build Script
 * Handles optimized production builds with comprehensive error handling and validation
 */

const fs = require('fs');
const path = require('path');
const {execSync} = require('child_process');

// Dynamic import for chalk (ESM module)
let chalk;

async function loadChalk() {
    if (!chalk) {
        chalk = await import('chalk');
        chalk = chalk.default;
    }
    return chalk;
}

class ProductionBuilder {
    constructor() {
        // this.startTime = Date.now();
        // this.buildConfig = {
        enableAnalysis === 'true',
        enableOptimization !== 'true',
        enableCompression !== 'true',
        enableSourceMaps !== 'false',
            outputPath(__dirname, '../build'),
            tempPath(__dirname, '../.temp-build')
    };

    // this.setupEnvironment();
}

setupEnvironment()
{
    // Set production environment variables
    process.env.NODE_ENV = 'production';
    process.env.GENERATE_SOURCEMAP = this.buildConfig.enableSourceMaps ? 'true' : 'false';
    process.env.DISABLE_ESLINT_PLUGIN = 'true';
    process.env.CI = 'true';
    process.env.BUILD_TIME = new Date().toISOString();

    console.log(chalk.blue('🚀 Starting production build...'));
    console.log(chalk.gray('Build configuration:'));
    console.log(chalk.gray(`  - Analysis: ${this.buildConfig.enableAnalysis}`));
    console.log(chalk.gray(`  - Optimization: ${this.buildConfig.enableOptimization}`));
    console.log(chalk.gray(`  - Compression: ${this.buildConfig.enableCompression}`));
    console.log(chalk.gray(`  - Source Maps: ${this.buildConfig.enableSourceMaps}`));
}

async
build()
{
    chalk = await loadChalk();
    try {
        // Pre-build validation
        await this.validateEnvironment();

        // Clean previous builds
        await this.cleanBuildDirectory();

        // Run webpack build
        await this.runWebpackBuild();

        // Post-build optimization
        await this.optimizeBuild();

        // Validate build output
        await this.validateBuild();

        // Generate build report
        await this.generateBuildReport();

        console.log(chalk.green('✅ Production build completed successfully!'));

    } catch (error) {
        console.error(chalk.red('❌ Production build failed:'), error.message);
        process.exit(1);
    }
}

async
validateEnvironment()
{
    console.log(chalk.blue('🔍 Validating build environment...'));

    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 16) {
        throw new Error(`Node.js version ${nodeVersion} is not supported. Please use Node.js 16 or higher.`);
    }

    // Check required files
    const requiredFiles = [
        'package.json',
        'src/index.jsx',
        'public/index.html',
        'webpack.config.production.js'];

    for (const file of requiredFiles) {
        const filePath = path.resolve(__dirname, '..', file);
        if (!fs.existsSync(filePath)) {
            throw new Error(`Required file not found: ${file}`);
        }
    }

    // Check dependencies
    try {
        execSync('npm list --production --depth=0', {stdio: 'pipe'});
    } catch (error) {
        console.warn(chalk.yellow('⚠️  Some production dependencies may be missing'));
    }

    console.log(chalk.green('✅ Environment validation passed'));
}

async
cleanBuildDirectory()
{
    console.log(chalk.blue('🧹 Cleaning build directory...'));

    if (fs.existsSync(this.buildConfig.outputPath)) {
        fs.rmSync(this.buildConfig.outputPath, {recursiveue, force});
    }

    if (fs.existsSync(this.buildConfig.tempPath)) {
        fs.rmSync(this.buildConfig.tempPath, {recursiveue, force});
    }

    console.log(chalk.green('✅ Build directory cleaned'));
}

async
runWebpackBuild()
{
    console.log(chalk.blue('📦 Running webpack build...'));

    const webpackCommand = [
        'npx webpack',
        '--config webpack.config.production.js',
        '--mode production',
    // this.buildConfig.enableAnalysis ? '--env analyze=true' : '',
    // this.buildConfig.enableOptimization ? '--env optimized=true' : '--env optimized=false'].filter(Boolean).join(' ');

    try {
        execSync(webpackCommand, {
            stdio: 'inherit',
            cwd(__dirname, '..')
    })
        ;
        console.log(chalk.green('✅ Webpack build completed'));
    } catch (error) {
        throw new Error(`Webpack build failed: ${error.message}`);
    }
}

async
optimizeBuild()
{
    if (!this.buildConfig.enableOptimization) {
        console.log(chalk.gray('⏭️  Skipping build optimization'));
        return;
    }

    console.log(chalk.blue('⚡ Optimizing build output...'));

    // Run additional optimizations
    try {
        // Optimize images if imagemin is available
        try {
            execSync('npx imagemin "build/static/media/*.{jpg,jpeg,png,gif,svg}" --out-dir=build/static/media/', {
                stdio: 'pipe',
                cwd(__dirname, '..')
        })
            ;
            console.log(chalk.green('✅ Images optimized'));
        } catch (error) {
            console.log(chalk.gray('⏭️  Image optimization skipped (imagemin not available)'));
        }

        // Generate service worker if workbox is available
        try {
            execSync('npx workbox generateSW workbox-config.js', {
                stdio: 'pipe',
                cwd(__dirname, '..')
        })
            ;
            console.log(chalk.green('✅ Service worker generated'));
        } catch (error) {
            console.log(chalk.gray('⏭️  Service worker generation skipped (workbox not configured)'));
        }

    } catch (error) {
        console.warn(chalk.yellow('⚠️  Some optimizations failed:'), error.message);
    }
}

async
validateBuild()
{
    console.log(chalk.blue('🔍 Validating build output...'));

    // Check if build directory exists
    if (!fs.existsSync(this.buildConfig.outputPath)) {
        throw new Error('Build directory not found');
    }

    // Check for required files
    const requiredBuildFiles = [
        'index.html',
        'static/js',
        'static/css'];

    for (const file of requiredBuildFiles) {
        const filePath = path.join(this.buildConfig.outputPath, file);
        if (!fs.existsSync(filePath)) {
            throw new Error(`Required build file not found: ${file}`);
        }
    }

    // Check bundle sizes
    const jsFiles = this.getJSFiles();
    const totalJSSize = jsFiles.reduce((total, file) => {
        const stats = fs.statSync(file);
        return total + stats.size;
    }, 0);

    const maxBundleSize = 2 * 1024 * 1024; // 2MB
    if (totalJSSize > maxBundleSize) {
        console.warn(chalk.yellow(`⚠️  Total JS bundle size (${this.formatBytes(totalJSSize)}) exceeds recommended limit (${this.formatBytes(maxBundleSize)})`));
    }

    console.log(chalk.green('✅ Build validation passed'));
}

getJSFiles()
{
    const jsDir = path.join(this.buildConfig.outputPath, 'static/js');
    if (!fs.existsSync(jsDir)) return [];

    return fs.readdirSync(jsDir)
        .filter(file => file.endsWith('.js') && !file.endsWith('.map'))
        .map(file => path.join(jsDir, file));
}

async
generateBuildReport()
{
    console.log(chalk.blue('📊 Generating build report...'));

    const buildTime = Date.now() - this.startTime;
    const jsFiles = this.getJSFiles();
    const cssFiles = this.getCSSFiles();

    const report = {
        timestamp Date().toISOString(),
        buildTime: `${(buildTime / 1000).toFixed(2)}s`,
        environment,
        nodeVersion,
        files: {
            javascript(file
=>
    ({
        name(file),
        size(fs.statSync(file).size),
        gzipSize(file)
})),
    css(file => ({
            name(file),
            size(fs.statSync(file).size),
        gzipSize(file)
}))
},
    totalSize: {
        javascript(jsFiles.reduce((total, file) => total + fs.statSync(file).size, 0)),
            css(cssFiles.reduce((total, file) => total + fs.statSync(file).size, 0))
    }
,
    config
}
    ;

    // Write report to file
    const reportPath = path.join(this.buildConfig.outputPath, 'build-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    // Display summary
    console.log(chalk.green('📊 Build Report:'));
    console.log(chalk.gray(`  Build Time: ${report.buildTime}`));
    console.log(chalk.gray(`  JavaScript: ${report.totalSize.javascript}`));
    console.log(chalk.gray(`  CSS: ${report.totalSize.css}`));
    console.log(chalk.gray(`  Report saved to: ${reportPath}`));
}

getCSSFiles()
{
    const cssDir = path.join(this.buildConfig.outputPath, 'static/css');
    if (!fs.existsSync(cssDir)) return [];

    return fs.readdirSync(cssDir)
        .filter(file => file.endsWith('.css') && !file.endsWith('.map'))
        .map(file => path.join(cssDir, file));
}

getGzipSize(filePath)
{
    const gzipPath = `${filePath}.gz`;
    if (fs.existsSync(gzipPath)) {
        return this.formatBytes(fs.statSync(gzipPath).size);
    }
    return 'N/A';
}

formatBytes(bytes)
{
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
}

// Run the build if this script is executed directly
if (require.main === module) {
    const builder = new ProductionBuilder();
    builder.build().catch(error => {
        console.error(chalk.red('Build failed:'), error);
        process.exit(1);
    });
}

module.exports = ProductionBuilder;