import React, {lazy, Suspense} from 'react';
import LoadingFallback from './LoadingFallback';

/**
 * Lazy-loaded components with preloading capabilities
 * Optimized for performance and reduced initial bundle size
 */

// Lazy load all major components
export const Dashboard = lazy(() => import('./Dashboard'));
export const AutonomousDashboard = lazy(() => import('./AutonomousDashboard'));
export const TradingDashboard = lazy(() => import('./TradingDashboard'));
export const BotDashboard = lazy(() => import('./BotDashboard'));
export const PortfolioSummary = lazy(() => import('./PortfolioSummary'));
export const PositionManager = lazy(() => import('./PositionManager'));
export const TradeHistory = lazy(() => import('./TradeHistory'));
export const Login = lazy(() => import('./Login'));
export const ErrorBoundaryTest = lazy(() => import('./ErrorBoundaryTest'));

// Utility function to wrap components with Suspense
export const withSuspense = (Component, fallback) => {
  return function SuspendedComponent(props) {
    return (
      <Suspense fallback={fallback || <LoadingFallback/>}>
        <Component {...props} />
      </Suspense>
    );
  };
};

// Preloading functions for improved performance
export const preloadAllComponents = () => {
  // Preload all components in parallel
  Promise.all([
    import('./Dashboard'),
    import('./AutonomousDashboard'),
    import('./TradingDashboard'),
    import('./BotDashboard'),
    import('./PortfolioSummary'),
    import('./PositionManager'),
    import('./TradeHistory'),
    import('./Login'),
    import('./ErrorBoundaryTest')]).catch(() => {
  });
};

// Preload based on route
export const preloadBasedOnRoute = (route) => {
  switch (route) {
  case '/dashboard':
    import('./Dashboard');
    break;
  case '/autonomous-dashboard':
    import('./AutonomousDashboard');
    break;
  case '/trading-dashboard':
    import('./TradingDashboard');
    break;
  case '/bot-dashboard':
    import('./BotDashboard');
    break;
  case '/portfolio-summary':
    import('./PortfolioSummary');
    break;
  case '/position-manager':
    import('./PositionManager');
    break;
  case '/trade-history':
    import('./TradeHistory');
    break;
  case '/login':
    import('./Login');
    break;
  default:
    // Preload common components
    import('./Dashboard');
    import('./AutonomousDashboard');
  }
};

// Preload based on usage patterns
export const preloadBasedOnUsage = () => {
  // Preload frequently used components
  Promise.all([import('./Dashboard'), import('./AutonomousDashboard'), import('./TradingDashboard')]);
};

// Preload on user interaction
export const preloadOnInteraction = (componentName) => {
  const componentMap = {
    dashboard: () => import('./Dashboard'),
    autonomousdashboard: () => import('./AutonomousDashboard'),
    tradingdashboard: () => import('./TradingDashboard'),
    botdashboard: () => import('./BotDashboard'),
    portfoliosummary: () => import('./PortfolioSummary'),
    positionmanager: () => import('./PositionManager'),
    tradehistory: () => import('./TradeHistory'),
    login: () => import('./Login'),
    errortest: () => import('./ErrorBoundaryTest'),
  };
  const loader = componentMap[componentName?.toLowerCase()];
  if (loader) {
    loader().catch(() => {
      // Preload failed, error ignored to avoid console statement
    });
  }
};

// Export all components for direct access
export default {
  Dashboard,
  AutonomousDashboard,
  TradingDashboard,
  BotDashboard,
  PortfolioSummary,
  PositionManager,
  TradeHistory,
  Login,
  ErrorBoundaryTest,
  LoadingFallback,
  withSuspense,
  preloadAllComponents,
  preloadBasedOnRoute,
  preloadBasedOnUsage,
  preloadOnInteraction,
};