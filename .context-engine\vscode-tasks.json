{"version": "2.0.0", "tasks": [{"label": "npm: start", "type": "npm", "command": "npm", "args": ["run", "start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: dev", "type": "npm", "command": "npm", "args": ["run", "dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: build", "type": "npm", "command": "npm", "args": ["run", "build"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$tsc"}, {"label": "npm: test", "type": "npm", "command": "npm", "args": ["run", "test"], "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: lint", "type": "npm", "command": "npm", "args": ["run", "lint"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: postinstall", "type": "npm", "command": "npm", "args": ["run", "postinstall"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: rebuild", "type": "npm", "command": "npm", "args": ["run", "rebuild"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "ESLint", "type": "shell", "command": "npx", "args": ["eslint", ".", "--ext", ".js,.jsx,.ts,.tsx"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$eslint-stylish"}]}