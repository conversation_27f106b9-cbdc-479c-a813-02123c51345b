'use strict';

/**
 * @fileoverview Standardized IPC Handler
 * @description Provides a simplified and consistent wrapper for creating Electron IPC handlers.
 * It standardizes response and error formats and integrates with the central logger.
 * This refactored version removes complex, stateful features like automatic retries and
 * circuit breakers, promoting a simpler, more predictable, and stateless architecture.
 * Such concerns are better managed by dedicated modules or at the application's orchestration layer.
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-07-25
 */

const logger = require('./logger');
/**
 * @typedef {Object} IPCResponse
 * @property {boolean} success - Indicates if the operation was successful.
 * @property {string} channel - The IPC channel on which the operation was handled.
 * @property {number} timestamp - The server timestamp of the response.
 * @property {*} [data] - The payload if the operation was successful.
 * @property {IPCError} [error] - The error details if the operation failed.
 */

/**
 * @typedef {Object} IPCError
 * @property {string} code - A standardized error code (e.g., 'VALIDATION_ERROR').
 * @property {string} message - A human-readable error message.
 * @property {string} [stack] - The error stack trace (in development environments only).
 * @property {Object} [context] - Additional contextual data about the error.
 */

class StandardizedIPCHandler {
  /**
   * Creates a standardized IPC handler with consistent error handling and response formatting.
   *
   * @param {string} channel - The name of the IPC channel this handler is for.
   * @param {Function} handler - The async function that performs the core logic for the channel.
   * @returns {Function} An async function compatible with `ipcMain.handle`.
   */
  createHandler(channel, handler) {
    return async (event, ...args) => {
      const startTime = Date.now();
      logger.info(`[IPC][${channel}] Received request.`);

      try {
        // Execute the core business logic for the handler
        const result = await handler(...args);
        const duration = Date.now() - startTime;

        logger.info(`[IPC][${channel}] Operation successful. Duration: ${duration}ms.`);

        // Return a standardized success response
        return this.createSuccessResponse(result, channel);
      } catch (error) {
        const duration = Date.now() - startTime;
        const errorCode = error.code || 'UNHANDLED_EXCEPTION';

        // Log the error with detailed context for easier debugging
        logger.error(`[IPC][${channel}] Operation failed. Duration: ${duration}ms.`, {
          errorMessage: error.message,
          errorCode,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
          context: error.context,
          args,
        });

        // Return a standardized error response
        return this.createErrorResponse(error, channel);
      }
    };
  }

  /**
   * Constructs a standardized success response object.
   *
   * @param {*} data - The payload to be sent back to the renderer process.
   * @param {string} channel - The IPC channel name.
   * @returns {IPCResponse} The standardized success response.
   */
  createSuccessResponse(data, channel) {
    return {
      success: true,
      channel,
      timestamp: Date.now(),
      data,
    };
  }

  /**
   * Constructs a standardized error response object.
   *
   * @param {Error} error - The error object caught during execution.
   * @param {string} channel - The IPC channel name.
   * @returns {IPCResponse} The standardized error response.
   */
  createErrorResponse(error, channel) {
    const isDev = process.env.NODE_ENV === 'development';

    return {
      success: false,
      channel,
      timestamp: Date.now(),
      error: {
        code: error.code || 'UNKNOWN_ERROR',
        message: error.message || 'An unexpected error occurred.',
        stack: isDev ? error.stack : undefined,
        context: error.context,
      },
    };
  }
}

// Export a singleton instance to ensure consistency across the application.
const standardizedIPCHandler = new StandardizedIPCHandler();
module.exports = standardizedIPCHandler;