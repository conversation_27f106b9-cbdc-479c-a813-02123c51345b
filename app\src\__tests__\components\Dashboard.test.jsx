/**
 * Dashboard Component Tests
 * Comprehensive test suite for the main trading dashboard
 */

/// <reference path="../jest.d.ts" />

import React from 'react';
import PropTypes from 'prop-types';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import {createTheme, ThemeProvider} from '@mui/material/styles';
import Dashboard from '../../components/Dashboard';
import UltimateDashboard from '@/components/UltimateDashboard';

// Create theme for tests
const theme = createTheme({
    palette: {
        mode: 'dark',
        primary: {main: '#00eaff'},
        secondary: {main: '#a259ff'}
    }
});
// Wrapper component for testing
const TestWrapper = (props) => (
    <ThemeProvider theme={theme}>
        {props.children}
    </ThemeProvider>
);

TestWrapper.propTypes = {
    children: PropTypes.node.isRequired
};

// Mock props
const defaultProps = {
    balance: 15420.83,
    isTrading: false,
    isLoading: false,
    tradingStats: {
        totalPnL: 847.65,
        successRate: 82.4,
        totalTrades: 156,
        winningTrades: 128,
        openPositions: 7
    },
    whaleSignals: [
        {
            symbol: 'DOGE',
            type: 'Large Buy',
            amount: '$2.4M',
            timestamp: new Date().toISOString(),
            confidence: 0.92
        }],
    memeCoinOpportunities: [
        {
            symbol: 'PEPE',
            score: 8.5,
            reason: 'High social volume spike detected'
        }],
    gridPositions: [
        {
            id: '1',
            symbol: 'DOGE/USDT',
            exchange: 'Binance',
            gridQuantity: 15,
            totalInvestment: 5000,
            totalProfit: 234.56,
            status: 'active',
            range: '$0.08 - $0.12'
        }],
    performanceMetrics: {
        pnlHistory: Array.from({length: 24}, (_, i) => ({
            time: `${i}:00`,
            pnl: Math.random() * 200 - 50
        }))
    },
    onStart: jest.fn(),
    onStop: jest.fn(),
    onRefresh: jest.fn()
};

describe('Dashboard', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('Rendering', () => {
        test('renders without crashing', () => {
            render(
                <TestWrapper>
                    <Dashboard {...defaultProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/Trading Dashboard/i)).toBeInTheDocument();
        });

        test('displays correct balance', () => {
            render(
                <TestWrapper>
                    <Dashboard {...defaultProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/\$15,420\.83/)).toBeInTheDocument();
        });

        test('shows trading status correctly', () => {
            render(
                <TestWrapper>
                    <Dashboard {...defaultProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/STANDBY/i)).toBeInTheDocument();
        });

        test('displays trading stats', () => {
            render(
                <TestWrapper>
                    <Dashboard {...defaultProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/847\.65/)).toBeInTheDocument(); // Total P&L
            expect(screen.getByText(/82\.4%/)).toBeInTheDocument(); // Success rate
        });
    });

    describe('Trading Controls', () => {
        test('calls onStart when Start Trading is clicked', () => {
            render(
                <TestWrapper>
                    <Dashboard {...defaultProps} />
                </TestWrapper>,
            );

            const startButton = screen.getByText(/Start Trading/i);
            fireEvent.click(startButton);

            expect(defaultProps.onStart).toHaveBeenCalledTimes(1);
        });

        test('calls onStop when Stop Trading is clicked', () => {
            const tradingProps = {...defaultProps, isTrading: true};

            render(
                <TestWrapper>
                    <Dashboard {...tradingProps} />
                </TestWrapper>,
            );

            const stopButton = screen.getByText(/Stop Trading/i);
            fireEvent.click(stopButton);

            expect(defaultProps.onStop).toHaveBeenCalledTimes(1);
        });

        test('calls onRefresh when refresh button is clicked', () => {
            render(
                <TestWrapper>
                    <Dashboard {...defaultProps} />
                </TestWrapper>,
            );

            const refreshButton = screen.getByLabelText(/refresh/i) || screen.getByText(/sync/i);
            if (refreshButton) {
                fireEvent.click(refreshButton);
                expect(defaultProps.onRefresh).toHaveBeenCalledTimes(1);
            }
        });
    });

    describe('Data Display', () => {
        test('displays whale signals when available', () => {
            render(
                <TestWrapper>
                    <Dashboard {...defaultProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/DOGE/)).toBeInTheDocument();
            expect(screen.getByText(/Large Buy/)).toBeInTheDocument();
            expect(screen.getByText(/\$2\.4M/)).toBeInTheDocument();
        });

        test('displays meme coin opportunities', () => {
            render(
                <TestWrapper>
                    <Dashboard {...defaultProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/PEPE/)).toBeInTheDocument();
            expect(screen.getByText(/8\.5/)).toBeInTheDocument();
        });

        test('displays grid positions', () => {
            render(
                <TestWrapper>
                    <Dashboard {...defaultProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/DOGE\/USDT/)).toBeInTheDocument();
            expect(screen.getByText(/Binance/)).toBeInTheDocument();
            expect(screen.getByText(/\$234\.56/)).toBeInTheDocument();
        });
    });

    describe('Loading States', () => {
        test('shows loading indicators when isLoading is true', () => {
            const loadingProps = {...defaultProps, isLoading: true};

            render(
                <TestWrapper>
                    <Dashboard {...loadingProps} />
                </TestWrapper>,
            );

            const loadingElements = screen.queryAllByRole('progressbar');
            expect(loadingElements.length).toBeGreaterThan(0);
        });

        test('disables buttons when loading', () => {
            const loadingProps = {...defaultProps, isLoading: true};

            render(
                <TestWrapper>
                    <Dashboard {...loadingProps} />
                </TestWrapper>,
            );

            const buttons = screen.getAllByRole('button');
            const disabledButtons = buttons.filter(button => 'disabled' in button && button.disabled);
            expect(disabledButtons.length).toBeGreaterThan(0);
        });
    });

    describe('Trading Status Changes', () => {
        test('updates button text when trading is active', () => {
            const tradingProps = {...defaultProps, isTrading: true};

            render(
                <TestWrapper>
                    <UltimateDashboard {...tradingProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/ACTIVE/i)).toBeInTheDocument();
            expect(screen.getByText(/Quantum Active/i)).toBeInTheDocument();
        });

        test('changes styling based on trading status', () => {
            const tradingProps = {...defaultProps, isTrading: true};

            render(
                <TestWrapper>
                    <UltimateDashboard {...tradingProps} />
                </TestWrapper>,
            );

            // Check for trading active indicators
            const activeElements = screen.queryAllByText(/ACTIVE/i);
            expect(activeElements.length).toBeGreaterThan(0);
        });
    });

    describe('Real-time Updates', () => {
        test('updates data with real-time effects', async () => {
            jest.useFakeTimers();

            render(
                <TestWrapper>
                    <UltimateDashboard {...defaultProps} />
                </TestWrapper>,
            );

            // Fast-forward time to trigger updates
            jest.advanceTimersByTime(2000);

            await waitFor(() => {
                // Real-time data should be updating
                expect(screen.getByText(/Quantum Elite Trading Matrix/i)).toBeInTheDocument();
            });

            jest.useRealTimers();
        });
    });

    describe('AI Insights', () => {
        test('displays AI insights section', () => {
            render(
                <TestWrapper>
                    <UltimateDashboard {...defaultProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/Neural Network Analysis/i)).toBeInTheDocument();
        });

        test('shows AI predictions with confidence levels', async () => {
            render(
                <TestWrapper>
                    <UltimateDashboard {...defaultProps} />
                </TestWrapper>,
            );

            await waitFor(() => {
                // Look for confidence percentages or AI-related content
                const confidenceElements = screen.queryAllByText(/%/);
                expect(confidenceElements.length).toBeGreaterThan(0);
            });
        });
    });

    describe('Performance Metrics', () => {
        test('displays performance charts', () => {
            render(
                <TestWrapper>
                    <UltimateDashboard {...defaultProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/Quantum Performance Analytics/i)).toBeInTheDocument();
        });

        test('shows portfolio distribution', () => {
            render(
                <TestWrapper>
                    <UltimateDashboard {...defaultProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/Portfolio Quantum Matrix/i)).toBeInTheDocument();
        });
    });

    describe('Accessibility', () => {
        test('has proper ARIA labels for interactive elements', () => {
            render(
                <TestWrapper>
                    <UltimateDashboard {...defaultProps} />
                </TestWrapper>,
            );

            const buttons = screen.getAllByRole('button');
            buttons.forEach(button => {
                expect(button).toHaveAttribute('aria-label');
            });
        });

        test('supports keyboard navigation', () => {
            render(
                <TestWrapper>
                    <UltimateDashboard {...defaultProps} />
                </TestWrapper>,
            );

            const startButton = screen.getByText(/Initiate Quantum Trading/i);

            // Simulate keyboard interaction
            fireEvent.keyDown(startButton, {key: 'Enter', code: 'Enter'});
            expect(defaultProps.onStart).toHaveBeenCalled();
        });
    });

    describe('Error Handling', () => {
        test('handles missing data gracefully', () => {
            const emptyProps = {
                ...defaultProps,
                whaleSignals: null,
                memeCoinOpportunities: null,
                gridPositions: null,
                performanceMetrics: null
            };

            expect(() => {
                render(
                    <TestWrapper>
                        <UltimateDashboard {...emptyProps} />
                    </TestWrapper>,
                );
            }).not.toThrow();
        });

        test('displays fallback content for empty arrays', () => {
            const emptyProps = {
                ...defaultProps,
                whaleSignals: [],
                memeCoinOpportunities: [],
                gridPositions: []
            };

            render(
                <TestWrapper>
                    <UltimateDashboard {...emptyProps} />
                </TestWrapper>,
            );

            expect(screen.getByText(/Monitoring quantum whale activity/i)).toBeInTheDocument();
        });
    });

    describe('Animation Performance', () => {
        test('applies GPU acceleration classes', () => {
            const {container} = render(
                <TestWrapper>
                    <UltimateDashboard {...defaultProps} />
                </TestWrapper>,
            );

            const animatedElements = container.querySelectorAll('[class*="gpu-accelerated"]');
            expect(animatedElements.length).toBeGreaterThan(0);
        });

        test('respects reduced motion preferences', () => {
            // Mock reduced motion preference
            Object.defineProperty(window, 'matchMedia', {
                writable: true,
                value: jest.fn().mockImplementation(query => ({
                    matches: query === '(prefers-reduced-motion: reduce)',
                    media: query,
                    onchange: null,
                    addListener: jest.fn(),
                    removeListener: jest.fn(),
                    addEventListener: jest.fn(),
                    removeEventListener: jest.fn(),
                    dispatchEvent: jest.fn()
                }))
            });

            render(
                <TestWrapper>
                    <UltimateDashboard {...defaultProps} />
                </TestWrapper>,
            );

            // Component should render without motion-heavy animations
            expect(screen.getByText(/Quantum Elite Trading Matrix/i)).toBeInTheDocument();
        });
    });
});
