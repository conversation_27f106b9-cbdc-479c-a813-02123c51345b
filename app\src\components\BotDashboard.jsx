// @ts-nocheck
import React, {useCallback, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {Box, CircularProgress, Grid, Typography} from '@mui/material';
import BotCard from './BotCard';
import HolographicCard from './HolographicCard';

const logger = {
    info: (...args) => logger.info('[INFO]', ...args),
    error: (...args) => logger.error('[ERROR]', ...args),
    warn: (...args) => logger.warn('[WARN]', ...args)
};

// Extend the Window interface to include electronAPI
/**
 * @typedef {Object} ElectronAPI
 * @property {Function} getBotStatus - Function to get bot status
 * @property {Function} getCoins - Function to get coins
 */
// Electron API exposed on window
const api = window.electronAPI || {
    getBotStatus: () => Promise.resolve({
        success: true,
        data: {status: 'stopped', profit: 0, loss: 0, tradesCount: 0, runtime: '0h 0m 0s'}
    }),
    getCoins: () => Promise.resolve({success: true, data: []})
};

/**
 * @typedef {Object} BotStatus
 * @property {string} status - The current status of the bot (e.g., 'active', 'stopped', 'error').
 * @property {number} profit - The total profit of the bot in the base currency.
 * @property {number} loss - The total loss of the bot in the base currency.
 * @property {number} tradesCount - The total number of trades executed by the bot.
 * @property {string} runtime - The runtime of the bot in hours, minutes, and seconds.
 */

/**
 * @typedef {Object} Coin
 * @property {number} id - Unique ID of the coin.
 * @property {string} symbol - Trading symbol of the coin (e.g., 'BTC/USDT').
 * @property {string} status - Status of the coin (e.g., 'active', 'inactive').
 * // Add other relevant coin properties as needed
 */

/**
 * @param {object} props
 * @param {function} props.showNotification - Function to display notifications.
 * @returns {JSX.Element} The BotDashboard component.
 */
function BotDashboard({showNotification}) {
    /** @type {[BotStatus|null, Function]} */
    const [botStatus, setBotStatus] = useState(/** @type {BotStatus|null} */(null));
    /** @type {[Coin[], Function]} */
    const [activeBots, setActiveBots] = useState(/** @type {Coin[]} */([]));
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const fetchData = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            logger.info('Fetching bot dashboard data...');
            const [statusResult, coinsResult] = await Promise.all([
                api.getBotStatus(),
                api.getCoins(), // Fetch all coins to show status correctly
            ]);

            logger.info('Bot dashboard data fetched successfully:', {statusResult, coinsResult});

            if (statusResult.success) {
                setBotStatus(statusResult.data);
            } else {
                throw new Error(statusResult.error || 'Failed to fetch bot status.');
            }

            if (coinsResult.success) {
                setActiveBots(coinsResult.data);
            } else {
                throw new Error(coinsResult.error || 'Failed to fetch active coins.');
            }
        } catch (err) {
            logger.error('Error fetching bot dashboard data:', err);
            const message = err instanceof Error ? err.message : String(err);
            setError(message);
            showNotification(`Error: ${message}`, 'error');
        } finally {
            setLoading(false);
        }
    }, [showNotification]);

    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 30000);
        return () => clearInterval(interval);
    }, [fetchData]);

    if (loading) {
        return (
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%'}}>
                <CircularProgress/>
                <Typography variant="h6" sx={{ml: 2}}>Loading Bot Data...</Typography>
            </Box>
        );
    }

    if (error) {
        return (
            <Box sx={{p: 3}}>
                <Typography color="error">Error: {error}</Typography>
            </Box>
        );
    }

    const activeBotsCount = activeBots.filter((b) => b && b.status === 'active').length;

    return (
        <Box sx={{p: 3}}>
            <Grid container spacing={3} sx={{mb: 3}}>
                <Grid item xs={12} md={6} lg={4}>
                    <HolographicCard variant="quantum" elevation="medium" sx={{height: '100%'}}>
                        <Typography variant="h6" color="primary" gutterBottom>Overall Bot Status</Typography>
                        <Typography variant="h5">Status: <span
                            style={{color: botStatus && botStatus.status === 'active' ? 'lightgreen' : 'red'}}>{botStatus ? botStatus.status : 'N/A'}</span></Typography>
                        <Typography variant="body1">Runtime: {botStatus ? botStatus.runtime : 'N/A'}</Typography>
                        <Typography variant="body1">Total Trades: {botStatus ? botStatus.tradesCount : 0}</Typography>
                    </HolographicCard>
                </Grid>
                <Grid item xs={12} md={6} lg={4}>
                    <HolographicCard variant="success" elevation="medium" sx={{height: '100%'}}>
                        <Typography variant="h6" color="primary" gutterBottom>Financial Overview</Typography>
                        <Typography variant="h5" sx={{color: 'lightgreen'}}>Profit:
                            ${botStatus?.profit?.toFixed(2) || '0.00'}</Typography>
                        <Typography variant="h5" sx={{color: 'red'}}>Loss:
                            ${botStatus?.loss?.toFixed(2) || '0.00'}</Typography>
                    </HolographicCard>
                </Grid>
                <Grid item xs={12} md={6} lg={4}>
                    <HolographicCard variant="premium" elevation="medium" sx={{height: '100%'}}>
                        <Typography variant="h6" color="primary" gutterBottom>Active Grid Bots</Typography>
                        <Typography variant="h5">{activeBotsCount}</Typography>
                        <Typography variant="body1">Currently running grid bots.</Typography>
                    </HolographicCard>
                </Grid>
            </Grid>

            <Typography variant="h5" gutterBottom sx={{mt: 4}}>Active Bots</Typography>
            <Grid container spacing={3}>
                {activeBots.map((bot, idx) => (
                    <Grid item xs={12} sm={6} md={4} key={bot.id ?? idx}>
                        <BotCard
                            bot={bot}
                            onStart={() => {
                            }}
                            onStop={() => {
                            }}
                            onConfigure={() => {
                            }}
                        />
                    </Grid>
                ))}
            </Grid>
        </Box>
    );
}

BotDashboard.propTypes = {
    showNotification: PropTypes.func.isRequired
};

export default BotDashboard;
