import React from 'react';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import StartButton from '../StartButton';

// Mock ipcService
jest.mock('../../services/ipcService', () => ({
    getBotStatus: jest.fn(),
    startBot: jest.fn(),
    stopBot: jest.fn(),
    getSystemInfo: jest.fn(),
    on: jest.fn(() => () => {
    })
}));

describe('StartButton Component', () => {
    let mockIpcService;

    beforeEach(() => {
        jest.clearAllMocks();
        mockIpcService = require('../../services/ipcService');
    });

    test('renders start button when system is stopped', async () => {
        mockIpcService.getBotStatus.mockResolvedValue({
            success: true,
            data: {isRunning: false}
        });
        mockIpcService.getSystemInfo.mockResolvedValue({
            success: true,
            data: {version: '1.0.0'}
        });

        render(<StartButton/>);

        await waitFor(() => {
            expect(screen.getByText('Start Trading')).toBeInTheDocument();
        });
    });

    test('renders stop button when system is running', async () => {
        mockIpcService.getBotStatus.mockResolvedValue({
            success: true,
            data: {isRunning: true}
        });
        mockIpcService.getSystemInfo.mockResolvedValue({
            success: true,
            data: {version: '1.0.0'}
        });

        render(<StartButton/>);

        await waitFor(() => {
            expect(screen.getByText('Stop Trading')).toBeInTheDocument();
        });
    });

    test('shows loading state during startup', async () => {
        mockIpcService.getBotStatus.mockResolvedValue({
            success: true,
            data: {isRunning: false}
        });
        mockIpcService.startBot.mockResolvedValue({
            success: true,
            data: {message: 'Started'}
        });

        render(<StartButton/>);

        await waitFor(() => {
            expect(screen.getByText('Start Trading')).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText('Start Trading'));

        await waitFor(() => {
            expect(screen.getByText('Processing...')).toBeInTheDocument();
        });
    });

    test('handles start button click successfully', async () => {
        const mockOnStatusChange = jest.fn();
        const mockShowNotification = jest.fn();

        mockIpcService.getBotStatus.mockResolvedValue({
            success: true,
            data: {isRunning: false}
        });
        mockIpcService.startBot.mockResolvedValue({
            success: true,
            data: {message: 'Started'}
        });

        render(
            <StartButton
                onStatusChange={mockOnStatusChange}
                showNotification={mockShowNotification}
            />,
        );

        await waitFor(() => {
            expect(screen.getByText('Start Trading')).toBeInTheDocument();
        });

        fireEvent.click(screen.getByText('Start Trading'));

        await waitFor(() => {
            expect(mockIpcService.startBot).toHaveBeenCalled();
        });
    });

    test('shows restart button when system is running', async () => {
        mockIpcService.getBotStatus.mockResolvedValue({
            success: true,
            data: {isRunning: true}
        });

        render(<StartButton/>);

        await waitFor(() => {
            expect(screen.getByText('Restart')).toBeInTheDocument();
        });
    });
});
