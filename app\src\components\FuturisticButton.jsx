import React, {useRef, useState} from 'react';
import PropTypes from 'prop-types';
import {Box, Button} from '@mui/material';
import {motion, useMotionValue, useSpring, useTransform} from 'framer-motion';
// Import UI constants
import {BORDER_RADIUS, SPACING, THEME_COLORS, TYPOGRAPHY} from '../constants/ui-constants';

/**
 * Next-Generation Futuristic Button with Advanced Visual Effects
 * Features: Morphing shapes, magnetic hover, 3D depth, holographic effects
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Button content
 * @param {string} [props.variant='primary'] - Button variant
 * @param {string} [props.size='medium'] - Button size
 * @param {boolean} [props.disabled=false] - Whether button is disabled
 * @param {Function} [props.onClick] - Click handler
 * @param {React.ReactNode} [props.startIcon] - Icon at start of button
 * @param {React.ReactNode} [props.endIcon] - Icon at end of button
 * @param {Object} [props.sx] - MUI sx prop for styling
 * @returns {React.ReactElement} The futuristic button component
 */
export default function FuturisticButton({
                                             children,
                                             variant = 'primary',
                                             size = 'medium',
                                             disabled = false,
                                             onClick,
                                             startIcon,
                                             endIcon,
                                             sx,
                                             ...props
                                         }) {
    const [isHovered, setIsHovered] = useState(false);
    const [isPressed, setIsPressed] = useState(false);
    const buttonRef = useRef(/** @type {HTMLDivElement | null} */(null));

    // Motion values for magnetic effect
    const x = useMotionValue(0);
    const y = useMotionValue(0);
    const rotateX = useSpring(useTransform(y, [-100, 100], [30, -30]));
    const rotateY = useSpring(useTransform(x, [-100, 100], [-30, 30]));

    // Handle mouse movement for magnetic effect
    /**
     * @param {React.MouseEvent} event - Mouse event
     */
    const handleMouseMove = (event) => {
        if (!buttonRef.current || disabled) return;

        const rect = buttonRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        x.set((event.clientX - centerX) * 0.1);
        y.set((event.clientY - centerY) * 0.1);
    };

    /**
     * Handle mouse leaving button area.
     * Resets magnetic effect and hover state.
     */
    const handleMouseLeave = () => {
        setIsHovered(false);
        x.set(0);
        y.set(0);
    };

    // Variant styles
    const getVariantStyles = () => {
        const variants = {
            primary: {
                background: `linear-gradient(45deg, ${THEME_COLORS.PRIMARY}, ${THEME_COLORS.SECONDARY}, #ff6b6b)`,
                boxShadow: `0 0 20px ${THEME_COLORS.PRIMARY}66, 0 0 40px ${THEME_COLORS.SECONDARY}33`,
                color: THEME_COLORS.TEXT_PRIMARY
            },
            secondary: {
                background: `linear-gradient(45deg, ${THEME_COLORS.SECONDARY}, #ff6b6b, ${THEME_COLORS.WARNING})`,
                boxShadow: `0 0 20px ${THEME_COLORS.SECONDARY}66, 0 0 40px #ff6b6b33`,
                color: THEME_COLORS.TEXT_PRIMARY
            },
            success: {
                background: `linear-gradient(45deg, ${THEME_COLORS.SUCCESS}, #00ff85, #26a69a)`,
                boxShadow: `0 0 20px ${THEME_COLORS.SUCCESS}66, 0 0 40px #00ff8533`,
                color: THEME_COLORS.TEXT_PRIMARY
            },
            error: {
                background: `linear-gradient(45deg, ${THEME_COLORS.ERROR}, #ff6b6b, #e91e63)`,
                boxShadow: `0 0 20px ${THEME_COLORS.ERROR}66, 0 0 40px #ff6b6b33`,
                color: THEME_COLORS.TEXT_PRIMARY
            },
            warning: {
                background: `linear-gradient(45deg, #ff9800, ${THEME_COLORS.WARNING}, #ffeb3b)`,
                boxShadow: `0 0 20px #ff980066, 0 0 40px ${THEME_COLORS.WARNING}33`,
                color: '#000000'
            },
            ghost: {
                background: 'rgba(255,255,255,0.05)',
                border: `2px solid ${THEME_COLORS.PRIMARY}80`,
                boxShadow: `0 0 20px ${THEME_COLORS.PRIMARY}1A`,
                color: THEME_COLORS.PRIMARY
            }
        };

        // Use switch statement for type-safe lookup
        switch (variant) {
            case 'primary':
                return variants.primary;
            case 'secondary':
                return variants.secondary;
            case 'success':
                return variants.success;
            case 'error':
                return variants.error;
            case 'warning':
                return variants.warning;
            case 'ghost':
                return variants.ghost;
            default:
                return variants.primary;
        }
    };

    // Size configurations
    const getSizeConfig = () => {
        const sizes = {
            small: {padding: `${SPACING.SM} ${SPACING.MD}`, fontSize: TYPOGRAPHY.FONT_SIZE_SMALL, minHeight: '36px'},
            medium: {padding: `${SPACING.MD} ${SPACING.LG}`, fontSize: TYPOGRAPHY.FONT_SIZE_MEDIUM, minHeight: '48px'},
            large: {padding: `${SPACING.MD} ${SPACING.XL}`, fontSize: TYPOGRAPHY.FONT_SIZE_LARGE, minHeight: '56px'}
        };

        // Use switch statement for type-safe lookup
        switch (size) {
            case 'small':
                return sizes.small;
            case 'medium':
                return sizes.medium;
            case 'large':
                return sizes.large;
            default:
                return sizes.medium;
        }
    };

    const variantStyles = getVariantStyles();
    const sizeConfig = getSizeConfig();

    return (
        <motion.div
            ref={buttonRef}
            style={{
                perspective: 1000,
                transformStyle: 'preserve-3d'
            }}
            onMouseMove={handleMouseMove}
            onMouseLeave={handleMouseLeave}
            onMouseEnter={() => setIsHovered(true)}
            onMouseDown={() => setIsPressed(true)}
            onMouseUp={() => setIsPressed(false)}
            whileHover={{scale: 1.05}}
            whileTap={{scale: 0.95}}
            transition={{type: 'spring', stiffness: 300, damping: 20}}
        >
            <motion.div
                style={{
                    rotateX,
                    rotateY,
                    transformStyle: 'preserve-3d'
                }}
            >
                <Button
                    {...props}
                    disabled={disabled}
                    onClick={onClick?.bind(null)}
                    sx={{
                        position: 'relative',
                        overflow: 'hidden',
                        border: 'none',
                        borderRadius: BORDER_RADIUS.CARD,
                        textTransform: 'none',
                        fontWeight: TYPOGRAPHY.FONT_WEIGHT_BOLD,
                        letterSpacing: '0.5px',
                        cursor: disabled ? 'not-allowed' : 'pointer',
                        backgroundSize: '400% 400%',
                        transition: 'all 0.4s cubic-bezier(0.23, 1, 0.32, 1)',
                        transformOrigin: 'center',
                        backdropFilter: 'blur(10px)',

                        // Apply variant and size styles
                        ...variantStyles,
                        ...sizeConfig,

                        // Disabled state
                        ...(disabled && {
                            background: 'rgba(255,255,255,0.1)',
                            color: 'rgba(255,255,255,0.3)',
                            boxShadow: 'none',
                            cursor: 'not-allowed'
                        }),

                        // Hover state
                        ...(!disabled && {
                            '&:hover': {
                                backgroundPosition: '100% 0',
                                transform: 'translateY(-2px) translateZ(10px)',
                                boxShadow: `
                                    ${variantStyles.boxShadow},
                                    0 10px 30px rgba(0,0,0,0.3),
                                    inset 0 1px 0 rgba(255,255,255,0.2)
                                `
                            }
                        }),

                        // Active state
                        ...(!disabled && {
                            '&:active': {
                                transform: 'translateY(0px) translateZ(5px)',
                                boxShadow: `
                                    ${variantStyles.boxShadow},
                                    0 5px 15px rgba(0,0,0,0.2)
                                `
                            }
                        }),

                        // Holographic shimmer effect
                        '&::before': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: '-100%',
                            width: '100%',
                            height: '100%',
                            background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',
                            transition: 'left 0.5s ease',
                            zIndex: 1
                        },

                        '&:hover::before': !disabled ? {
                            left: '100%'
                        } : {},

                        // Neural network pattern overlay
                        '&::after': {
                            content: '""',
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            right: 0,
                            bottom: 0,
                            background: `
                                radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
                                radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 1px, transparent 1px),
                                radial-gradient(circle at 40% 60%, rgba(255,255,255,0.05) 1px, transparent 1px)
                            `,
                            backgroundSize: '30px 30px, 25px 25px, 35px 35px',
                            opacity: isHovered ? 1 : 0,
                            transition: 'opacity 0.3s ease',
                            pointerEvents: 'none',
                            zIndex: 0
                        },

                        ...sx
                    }}
                >
                    {/* Content wrapper with z-index */}
                    <Box
                        sx={{
                            position: 'relative',
                            zIndex: 2,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                        }}
                    >
                        {startIcon && (
                            <motion.div
                                animate={isHovered ? {rotate: 360} : {rotate: 0}}
                                transition={{duration: 0.5}}
                            >
                                {startIcon}
                            </motion.div>
                        )}

                        <motion.span
                            animate={isPressed ? {scale: 0.95} : {scale: 1}}
                            transition={{duration: 0.1}}
                        >
                            {children}
                        </motion.span>

                        {endIcon && (
                            <motion.div
                                animate={isHovered ? {x: 5} : {x: 0}}
                                transition={{duration: 0.3}}
                            >
                                {endIcon}
                            </motion.div>
                        )}
                    </Box>

                    {/* Energy field effect */}
                    {isHovered && !disabled && (
                        <motion.div
                            initial={{scale: 0, opacity: 0}}
                            animate={{scale: 1, opacity: 0.3}}
                            exit={{scale: 0, opacity: 0}}
                            style={{
                                position: 'absolute',
                                top: '-2px',
                                left: '-2px',
                                right: '-2px',
                                bottom: '-2px',
                                background: variantStyles.background,
                                borderRadius: '18px',
                                filter: 'blur(8px)',
                                zIndex: -1
                            }}
                        />
                    )}
                </Button>
            </motion.div>
        </motion.div>
    );
}

FuturisticButton.propTypes = {
    children: PropTypes.node.isRequired,
    variant: PropTypes.oneOf(['primary', 'secondary', 'success', 'error', 'warning', 'ghost']),
    size: PropTypes.oneOf(['small', 'medium', 'large']),
    disabled: PropTypes.bool,
    onClick: PropTypes.func,
    startIcon: PropTypes.node,
    endIcon: PropTypes.node,
    sx: PropTypes.object
};
