{"app": {"name": "Meme Coin Trader", "description": "Professional cryptocurrency trading platform with AI-powered analytics", "author": "ElectronTrader Team", "homepage": "https://electrontrader.com", "repository": "https://github.com/electrontrader/meme-coin-trader"}, "electron": {"isDev": false, "devTools": false, "contextIsolation": true, "nodeIntegration": false, "webSecurity": true, "allowRunningInsecureContent": false, "experimentalFeatures": false}, "logging": {"level": "error", "console": false, "file": true, "maxFiles": 10, "maxSize": "50m", "compress": true, "datePattern": "YYYY-MM-DD", "auditFile": "logs/audit.json", "errorFile": "logs/error.log", "combinedFile": "logs/combined.log"}, "performance": {"enableMetrics": true, "enableProfiling": false, "memoryLimit": "1gb", "cpuThreshold": 85, "gcOptimization": true, "v8Flags": ["--max-old-space-size=1024", "--optimize-for-size"], "monitoring": {"interval": 30000, "alertThresholds": {"memory": 0.8, "cpu": 0.9, "heap": 0.85}}}, "security": {"enableCSP": true, "enableHSTS": true, "enableCORS": false, "allowedOrigins": [], "contentSecurityPolicy": {"defaultSrc": ["'self'"], "scriptSrc": ["'self'", "'unsafe-inline'"], "styleSrc": ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"], "fontSrc": ["'self'", "https://fonts.gstatic.com"], "imgSrc": ["'self'", "data:", "https:"], "connectSrc": ["'self'", "wss:", "https:"]}, "permissions": {"camera": false, "microphone": false, "geolocation": false, "notifications": true, "persistentStorage": true}}, "features": {"hotReload": false, "autoUpdate": true, "errorReporting": true, "analytics": true, "debugMode": false, "crashReporting": true, "telemetry": true, "autoLaunch": false, "minimizeToTray": true, "singleInstance": true}, "build": {"optimization": true, "minification": true, "compression": true, "sourceMap": false, "bundleAnalysis": false, "treeShaking": true, "deadCodeElimination": true, "assetOptimization": true, "imageOptimization": true, "fontOptimization": true}, "trading": {"environment": "production", "sandbox": false, "rateLimit": {"enabled": true, "requests": 1000, "window": 60000}, "timeout": 30000, "retries": 3, "backoff": "exponential", "healthCheck": {"enabled": true, "interval": 60000, "timeout": 10000}}, "database": {"type": "sqlite", "path": "data/production.db", "backup": {"enabled": true, "interval": 3600000, "retention": 30, "compression": true}, "optimization": {"pragma": {"journal_mode": "WAL", "synchronous": "NORMAL", "cache_size": 10000, "temp_store": "MEMORY"}}}, "ui": {"theme": "dark", "animations": true, "notifications": true, "autoRefresh": true, "refreshInterval": 5000, "chartUpdateInterval": 1000, "maxHistoryItems": 1000, "performance": {"virtualScrolling": true, "lazyLoading": true, "memoization": true, "debounceDelay": 300}}, "monitoring": {"healthChecks": true, "performanceMetrics": true, "errorReporting": true, "userAnalytics": false, "crashReporting": true, "endpoints": {"health": "/api/health", "metrics": "/api/metrics", "errors": "/api/errors"}}, "updates": {"enabled": true, "channel": "stable", "checkInterval": 86400000, "autoDownload": true, "autoInstall": false, "allowPrerelease": false, "feedUrl": "https://updates.electrontrader.com"}, "paths": {"userData": "data", "logs": "logs", "cache": "cache", "temp": "temp", "backups": "backups"}}