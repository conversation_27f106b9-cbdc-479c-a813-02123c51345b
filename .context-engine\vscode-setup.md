# VSCode Configuration

This directory contains VSCode-specific configuration for optimal development experience.

## Available Tasks

Use `Ctrl+Shift+P` > `Tasks: Run Task` to execute:

### Build Tasks

- **npm: start**: npm run start
- **npm: dev**: npm run dev
- **npm: build**: npm run build
- **npm: lint**: npm run lint
- **npm: postinstall**: npm run postinstall
- **npm: rebuild**: npm run rebuild
- **ESLint**: npx eslint . --ext .js,.jsx,.ts,.tsx

### Test Tasks

- **npm: test**: npm run test

## Debug Configurations

Use `F5` or `Ctrl+Shift+D` to start debugging:

- **Debug Node.js with Copilot** (node): launch
- **Debug Current File** (node): launch
- **Attach to Process** (node): attach

## Recommended Extensions

VSCode will prompt to install these extensions for the best development experience:

- dbaeumer.vscode-eslint
- ms-vscode.vscode-json
- redhat.vscode-yaml
- ms-vscode-remote.remote-containers
- ms-vscode.vscode-markdown
- christian-kohler.path-intellisense
- formulahendry.auto-rename-tag

## Key Settings

- **editor.codeActionsOnSave**: `{"source.fixAll.eslint":true}`
- **eslint.format.enable**: `true`

## Getting Started

1. Install recommended extensions when prompted
2. Use `Ctrl+Shift+P` to access the command palette
3. Try `Tasks: Run Task` to see available build tasks
4. Use `F5` to start debugging
5. Enable format on save for consistent code formatting

