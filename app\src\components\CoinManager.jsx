 
'use strict';

Object.defineProperty(exports, '__esModule', {
    value: true
});
exports.default = CoinManager;
const _react = _interopRequireWildcard(require('react'));
const _logger = _interopRequireDefault(require('../utils/logger'));
const _material = require('@mui/material');
const _iconsMaterial = require('@mui/icons-material');
const _VibrantButton = _interopRequireDefault(require('./VibrantButton'));
const _HolographicCard = _interopRequireDefault(require('./HolographicCard'));

function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}

function _interopRequireWildcard(e, t) {
    if ('function' == typeof WeakMap) var r = new WeakMap(),
        n = new WeakMap();
    return (_interopRequireWildcard = function (e, t) {
        if (!t && e && e.__esModule) return e;
        let o,
            i,
            f = {
                __proto__: null,
                default: e
            };
        if (null === e || 'object' != typeof e && 'function' != typeof e) return f;
        if (o = t ? n : r) {
            if (o.has(e)) return o.get(e);
            o.set(e, f);
        }
        for (const t in e) 'default' !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]);
        return f;
    })(e, t);
}

function _extends() {
    return _extends = Object.assign ? Object.assign.bind() : function (n) {
        for (let e = 1; e < arguments.length; e++) {
            const t = arguments[e];
            for (const r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
        }
        return n;
    }, _extends.apply(null, arguments);
}

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) : e[r] = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i : i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String : Number)(t);
} // @ts-nocheck
// Import logger for consistent logging
// use Electron API exposed on window
// Electron API (any) for dynamic methods
const api = window.electronAPI || {
    getCoins: () => Promise.resolve({
        success: true,
        data: []
    }),
    getAvailableSymbols: () => Promise.resolve({
        success: true,
        data: []
    }),
    'save-coin': () => Promise.resolve({
        success: true
    }),
    'delete-coin': () => Promise.resolve({
        success: true
    })
};

/**
 * @typedef {object} Coin
 * @property {number} [id]
 * @property {string} symbol
 * @property {string | number} gridSize
 * @property {string | number} investment
 * @property {string} strategy
 */

/**
 * A component for managing a list of trading coins. It provides a table of
 * existing coins, a form to add new coins, and a button to add new coins. When
 * a user clicks on a coin in the table, its data is prefilled in the form and
 * the user can edit the coin's details. After adding or editing a coin, the
 * component calls the `onAdd` or `onEdit` callbacks with the new coin data.
 *
 * @returns {JSX.Element}
 */
function CoinManager() {
    /** @type {[Coin[], React.Dispatch<React.SetStateAction<Coin[]>>]} */
    const [coins, setCoins] = (0, _react.useState)(/** @type {Coin[]} */[]);
    const [open, setOpen] = (0, _react.useState)(false);
    /** @type {[number | null, React.Dispatch<React.SetStateAction<number | null>>]} */
    const [editingCoinId, setEditingCoinId] = (0, _react.useState)(/** @type {number | null} */null);
    const [availableSymbols, setAvailableSymbols] = (0, _react.useState)(/** @type {string[]} */[]);
    const NEW_COIN_INITIAL_STATE = {
        symbol: '',
        gridSize: '',
        investment: '',
        strategy: 'grid'
    };

    /** @type {[Coin, React.Dispatch<React.SetStateAction<Coin>>]} */
    const [newCoin, setNewCoin] = (0, _react.useState)(/** @type {Coin} */NEW_COIN_INITIAL_STATE);
    const fetchCoins = (0, _react.useCallback)(async () => {
        try {
            const result = await api.getCoins();
            if (result !== null && result !== void 0 && result.success) {
                setCoins(result.data);
            }
        } catch (error) {
            _logger.default.error('Failed to fetch managed coins:', error);
        }
    }, []);
    const fetchSymbols = (0, _react.useCallback)(async () => {
        try {
            const result = await api.getAvailableSymbols();
            if (result !== null && result !== void 0 && result.success) {
                setAvailableSymbols(result.data);
            }
        } catch (error) {
            _logger.default.error('Failed to fetch available symbols:', error);
        }
    }, []);
    (0, _react.useEffect)(() => {
        fetchCoins();
        fetchSymbols();
    }, [fetchCoins, fetchSymbols]);

    /**
     * Opens the coin manager dialog to add a new coin. Resets the form with
     * default values and marks the dialog as open.
     */
    const handleAddCoin = () => {
        setEditingCoinId(null);
        setNewCoin(NEW_COIN_INITIAL_STATE);
        setOpen(true);
    };

    /**
     * Opens the coin manager dialog to edit an existing coin. Prefills the form
     * with the coin's current data and marks the dialog as open.
     *
     * @param {Object} coin - The coin object to edit.
     */

    /** @param {Coin} coin */
    const handleEditCoin = coin => {
        setEditingCoinId(coin.id);
        // Prefill newCoin state with existing coin
        setNewCoin(/** @type {Coin} */_objectSpread({}, coin));
        setOpen(true);
    };

    /**
     * Handles saving a new or edited coin. If editing a coin, calls `onEdit`
     * with the index of the coin and the new coin data. If adding a new coin,
     * calls `onAdd` with the new coin data. Closes the dialog and resets the
     * `newCoin` state to blank.
     */
    const handleSave = async () => {
        try {
            if (editingCoinId !== null) {
                await api['save-coin'](_objectSpread(_objectSpread({}, newCoin), {}, {
                    id: editingCoinId
                }));
            } else {
                await api['save-coin'](newCoin);
            }
            fetchCoins(); // Refresh the list
        } catch (error) {
            _logger.default.error('Failed to save coin:', error);
        }
        setOpen(false);
        setNewCoin(NEW_COIN_INITIAL_STATE);
        setEditingCoinId(null);
    };

    /**
     * Deletes a managed coin by its ID. Removes the coin from the database
     * and refreshes the list of coins.
     *
     * @param {number} coinId - The id of the coin to delete.
     */

    const handleDelete = async coinId => {
        try {
            await api['delete-coin'](coinId);
            fetchCoins(); // Refresh the list
        } catch (error) {
            _logger.default.error(`Failed to delete coin ${coinId}:`, error);
        }
    };

    /**
     * Closes the coin management modal, resets the editing state,
     * and clears the new coin input fields.
     */

    const handleClose = () => {
        setOpen(false);
        setEditingCoinId(null);
        setNewCoin(NEW_COIN_INITIAL_STATE);
    };

    /**
     * Returns a comma-separated RGB value for a strategy's shadow color.
     * @param {string} strategy - The strategy to get the shadow color for.
     * @returns {string} A comma-separated RGB value for the strategy's shadow color.
     */
    const getShadowColor = strategy => {
        switch (strategy) {
            case 'grid':
                return '0,234,255';
            case 'dca':
                return '162,89,255';
            default:
                return '255,193,7';
        }
    };

    /**
     * Returns the color associated with a given trading strategy.
     * @param {string} strategy - The trading strategy to get the color for.
     * @returns {string} The hex color code representing the strategy.
     */

    const getStrategyColor = strategy => {
        switch (strategy) {
            case 'grid':
                return '#00eaff';
            case 'dca':
                return '#a259ff';
            case 'whale':
                return '#ffc107';
            default:
                return '#fff';
        }
    };
    return /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 4,
            minHeight: '100vh'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 4,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        sx: {
            background: 'linear-gradient(45deg, #a259ff 30%, #00eaff 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 800,
            mb: 1
        }
    }, 'Coin Management'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#888',
            fontWeight: 300
        }
    }, 'Configure your trading pairs and strategies')), /*#__PURE__*/_react.default.createElement(_VibrantButton.default, {
        onClick: handleAddCoin,
        startIcon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.AddCircle, null),
        sx: {
            minWidth: '150px'
        }
    }, 'Add Coin')), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, coins.length > 0 ? coins.map((coin, idx) => {
        let _coin$strategy;
        return /*#__PURE__*/_react.default.createElement(_material.Grid, {
            item: true,
            xs: 12,
            sm: 6,
            md: 4,
            key: coin.id || `${coin.symbol}-${idx}`
        }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
            variant: 'default',
            elevation: 'medium',
            sx: {
                border: `1px solid ${getStrategyColor(coin.strategy)}`,
                '&:hover': {
                    boxShadow: `0 10px 30px rgba(${getShadowColor(coin.strategy)}, 0.3)`
                }
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 2
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'h6',
            sx: {
                color: getStrategyColor(coin.strategy),
                fontWeight: 800,
                display: 'flex',
                alignItems: 'center'
            }
        }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.ShowChart, {
            sx: {
                mr: 1
            }
        }), coin.symbol), /*#__PURE__*/_react.default.createElement(_material.Box, null, /*#__PURE__*/_react.default.createElement(_material.IconButton, {
            onClick: () => handleEditCoin(coin),
            sx: {
                color: '#00eaff',
                mr: 1
            }
        }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Edit, null)), /*#__PURE__*/_react.default.createElement(_material.IconButton, {
            onClick: () => {
                let _coin$id;
                return handleDelete((_coin$id = coin.id) !== null && _coin$id !== void 0 ? _coin$id : idx);
            },
            sx: {
                color: '#f44336'
            }
        }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Delete, null)))), /*#__PURE__*/_react.default.createElement(_material.Chip, {
            label: ((_coin$strategy = coin.strategy) === null || _coin$strategy === void 0 ? void 0 : _coin$strategy.toUpperCase()) || 'GRID',
            sx: {
                backgroundColor: `${getStrategyColor(coin.strategy)}20`,
                color: getStrategyColor(coin.strategy),
                fontWeight: 600,
                mb: 2
            }
        }), /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                mb: 2
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            sx: {
                color: '#888',
                mb: 0.5
            }
        }, 'Grid Size'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'h6',
            sx: {
                color: '#fff',
                fontWeight: 600
            }
        }, coin.gridSize)), /*#__PURE__*/_react.default.createElement(_material.Box, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            sx: {
                color: '#888',
                mb: 0.5
            }
        }, 'Investment'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'h6',
            sx: {
                color: '#4caf50',
                fontWeight: 600,
                display: 'flex',
                alignItems: 'center'
            }
        }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.TrendingUp, {
            sx: {
                mr: 0.5,
                fontSize: '1.2rem'
            }
        }), `${String(coin.investment).replace(/^\$/, '')}`))));
    }) : /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'low',
        sx: {
            border: '2px dashed rgba(162,89,255,0.3)',
            textAlign: 'center',
            p: 6
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#888',
            mb: 2
        }
    }, 'No coins configured yet'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#666',
            mb: 3
        }
    }, 'Add your first trading pair to get started'), /*#__PURE__*/_react.default.createElement(_VibrantButton.default, {
        onClick: handleAddCoin,
        startIcon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.AddCircle, null)
    }, 'Add Your First Coin')))), /*#__PURE__*/_react.default.createElement(_material.Dialog, {
        open: open,
        onClose: handleClose,
        maxWidth: 'sm',
        fullWidth: true,
        PaperProps: {
            sx: {
                background: 'rgba(24,26,32,0.95)',
                border: '1px solid rgba(162,89,255,0.3)',
                borderRadius: 3,
                backdropFilter: 'blur(10px)'
            }
        }
    }, /*#__PURE__*/_react.default.createElement(_material.DialogTitle, {
        sx: {
            color: '#a259ff',
            fontWeight: 800
        }
    }, editingCoinId !== null ? 'Edit Coin' : 'Add New Coin'), /*#__PURE__*/_react.default.createElement(_material.DialogContent, null, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 2,
        sx: {
            mt: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(_material.Autocomplete, {
        value: newCoin.symbol,
        onChange: (event, newValue) => {
            setNewCoin(_objectSpread(_objectSpread({}, newCoin), {}, {
                symbol: newValue
            }));
        },
        inputValue: newCoin.symbol,
        onInputChange: (event, newInputValue) => {
            setNewCoin(_objectSpread(_objectSpread({}, newCoin), {}, {
                symbol: newInputValue.toUpperCase()
            }));
        },
        options: availableSymbols,
        renderInput: params => /*#__PURE__*/_react.default.createElement(_material.TextField, _extends({}, params, {
            label: 'Symbol',
            placeholder: 'BTC/USDT',
            sx: {
                '& .MuiOutlinedInput-root': {
                    '& fieldset': {
                        borderColor: 'rgba(162,89,255,0.3)'
                    },
                    '&:hover fieldset': {
                        borderColor: '#a259ff'
                    },
                    '&.Mui-focused fieldset': {
                        borderColor: '#a259ff'
                    }
                },
                '& .MuiInputLabel-root': {
                    color: '#a259ff'
                },
                '& .MuiInputBase-input': {
                    color: '#fff'
                }
            }
        }))
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 6
    }, /*#__PURE__*/_react.default.createElement(_material.TextField, {
        label: 'Grid Size',
        value: newCoin.gridSize,
        onChange: e => setNewCoin(_objectSpread(_objectSpread({}, newCoin), {}, {
            gridSize: e.target.value
        })),
        fullWidth: true,
        placeholder: '10',
        sx: {
            '& .MuiOutlinedInput-root': {
                '& fieldset': {
                    borderColor: 'rgba(0,234,255,0.3)'
                },
                '&:hover fieldset': {
                    borderColor: '#00eaff'
                },
                '&.Mui-focused fieldset': {
                    borderColor: '#00eaff'
                }
            },
            '& .MuiInputLabel-root': {
                color: '#00eaff'
            },
            '& .MuiInputBase-input': {
                color: '#fff'
            }
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 6
    }, /*#__PURE__*/_react.default.createElement(_material.TextField, {
        label: 'Investment ($)',
        value: newCoin.investment,
        onChange: e => setNewCoin(_objectSpread(_objectSpread({}, newCoin), {}, {
            investment: e.target.value
        })),
        fullWidth: true,
        placeholder: '1000',
        sx: {
            '& .MuiOutlinedInput-root': {
                '& fieldset': {
                    borderColor: 'rgba(76,175,80,0.3)'
                },
                '&:hover fieldset': {
                    borderColor: '#4caf50'
                },
                '&.Mui-focused fieldset': {
                    borderColor: '#4caf50'
                }
            },
            '& .MuiInputLabel-root': {
                color: '#4caf50'
            },
            '& .MuiInputBase-input': {
                color: '#fff'
            }
        }
    })))), /*#__PURE__*/_react.default.createElement(_material.DialogActions, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Button, {
        onClick: handleClose,
        sx: {
            color: '#888'
        }
    }, 'Cancel'), /*#__PURE__*/_react.default.createElement(_VibrantButton.default, {
        onClick: handleSave,
        disabled: !newCoin.symbol.trim() || !newCoin.gridSize.toString().trim() || !newCoin.investment.toString().trim()
    }, editingCoinId !== null ? 'Update' : 'Add', ' Coin'))));
}

// CoinManager takes no props
CoinManager.propTypes = {};