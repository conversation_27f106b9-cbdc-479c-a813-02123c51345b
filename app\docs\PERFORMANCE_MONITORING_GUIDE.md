# Performance Monitoring Setup Guide

## Overview

This guide provides comprehensive instructions for setting up performance monitoring and error reporting for the Meme Coin Trader application in production environments.

## Table of Contents

1. [Built-in Performance Monitoring](#built-in-performance-monitoring)
2. [System Metrics Collection](#system-metrics-collection)
3. [Application Performance Monitoring](#application-performance-monitoring)
4. [Database Performance Monitoring](#database-performance-monitoring)
5. [Trading Performance Metrics](#trading-performance-metrics)
6. [External Monitoring Integration](#external-monitoring-integration)
7. [Error Reporting Setup](#error-reporting-setup)
8. [Alerting and Notifications](#alerting-and-notifications)
9. [Dashboard Configuration](#dashboard-configuration)
10. [Performance Optimization](#performance-optimization)

## Built-in Performance Monitoring

### 1. Enable Performance Monitoring

Configure performance monitoring in your environment:

```bash
# Environment variables
export PERFORMANCE_MONITORING=true
export METRICS_COLLECTION=true
export METRICS_INTERVAL=30000
export METRICS_RETENTION=86400000
export HEALTH_CHECK_ENABLED=true
export HEALTH_CHECK_INTERVAL=30000
```

### 2. Performance Monitor Configuration

Create `config/performance.json`:

```json
{
  "enabled": true,
  "collection": {
    "interval": 30000,
    "retention": 86400000,
    "aggregation": true,
    "compression": true
  },
  "metrics": {
    "system": {
      "cpu": true,
      "memory": true,
      "disk": true,
      "network": true
    },
    "application": {
      "eventLoop": true,
      "heap": true,
      "handles": true,
      "requests": true
    },
    "trading": {
      "trades": true,
      "orders": true,
      "positions": true,
      "pnl": true
    },
    "database": {
      "queries": true,
      "connections": true,
      "performance": true
    }
  },
  "thresholds": {
    "cpu": 80,
    "memory": 85,
    "disk": 90,
    "responseTime": 5000,
    "errorRate": 0.05
  },
  "alerts": {
    "enabled": true,
    "channels": ["email", "webhook"],
    "cooldown": 300000
  }
}
```

### 3. Performance Monitor Implementation

The application includes a comprehensive performance monitor:

```javascript
// app/src/utils/PerformanceMonitor.js
class PerformanceMonitor {
  constructor(config) {
    this.config = config;
    this.metrics = new Map();
    this.alerts = new Map();
    this.startTime = Date.now();
    
    if (config.enabled) {
      this.startMonitoring();
    }
  }

  startMonitoring() {
    // System metrics collection
    this.systemMetricsInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, this.config.collection.interval);

    // Application metrics collection
    this.appMetricsInterval = setInterval(() => {
      this.collectApplicationMetrics();
    }, this.config.collection.interval);

    // Trading metrics collection
    this.tradingMetricsInterval = setInterval(() => {
      this.collectTradingMetrics();
    }, this.config.collection.interval);
  }

  collectSystemMetrics() {
    const os = require('os');
    const fs = require('fs');

    // CPU metrics
    const cpuUsage = process.cpuUsage();
    const loadAverage = os.loadavg();

    // Memory metrics
    const memoryUsage = process.memoryUsage();
    const systemMemory = {
      total: os.totalmem(),
      free: os.freemem(),
      used: os.totalmem() - os.freemem()
    };

    // Disk metrics
    const diskUsage = this.getDiskUsage();

    // Network metrics
    const networkInterfaces = os.networkInterfaces();

    const systemMetrics = {
      timestamp: Date.now(),
      cpu: {
        usage: cpuUsage,
        loadAverage: loadAverage,
        cores: os.cpus().length
      },
      memory: {
        process: memoryUsage,
        system: systemMemory,
        percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
      },
      disk: diskUsage,
      network: this.getNetworkStats(networkInterfaces)
    };

    this.recordMetric('system', systemMetrics);
    this.checkThresholds('system', systemMetrics);
  }

  collectApplicationMetrics() {
    // Event loop lag
    const eventLoopLag = this.measureEventLoopLag();

    // Heap statistics
    const v8 = require('v8');
    const heapStats = v8.getHeapStatistics();

    // Handle counts
    const handles = process._getActiveHandles().length;
    const requests = process._getActiveRequests().length;

    // HTTP metrics (if applicable)
    const httpMetrics = this.getHttpMetrics();

    const appMetrics = {
      timestamp: Date.now(),
      eventLoop: {
        lag: eventLoopLag,
        utilization: this.getEventLoopUtilization()
      },
      heap: heapStats,
      handles: handles,
      requests: requests,
      http: httpMetrics,
      uptime: process.uptime()
    };

    this.recordMetric('application', appMetrics);
    this.checkThresholds('application', appMetrics);
  }

  collectTradingMetrics() {
    // Trading performance metrics
    const tradingMetrics = {
      timestamp: Date.now(),
      trades: this.getTradingStats(),
      orders: this.getOrderStats(),
      positions: this.getPositionStats(),
      pnl: this.getPnLStats(),
      latency: this.getLatencyStats()
    };

    this.recordMetric('trading', tradingMetrics);
    this.checkThresholds('trading', tradingMetrics);
  }

  recordMetric(category, data) {
    if (!this.metrics.has(category)) {
      this.metrics.set(category, []);
    }

    const categoryMetrics = this.metrics.get(category);
    categoryMetrics.push(data);

    // Implement retention policy
    const retentionTime = Date.now() - this.config.collection.retention;
    const filteredMetrics = categoryMetrics.filter(
      metric => metric.timestamp > retentionTime
    );
    
    this.metrics.set(category, filteredMetrics);
  }

  checkThresholds(category, metrics) {
    const thresholds = this.config.thresholds;

    // CPU threshold check
    if (category === 'system' && metrics.cpu.loadAverage[0] > thresholds.cpu / 100) {
      this.triggerAlert('high_cpu', {
        value: metrics.cpu.loadAverage[0],
        threshold: thresholds.cpu / 100
      });
    }

    // Memory threshold check
    if (category === 'system' && metrics.memory.percentage > thresholds.memory) {
      this.triggerAlert('high_memory', {
        value: metrics.memory.percentage,
        threshold: thresholds.memory
      });
    }

    // Response time threshold check
    if (category === 'application' && metrics.http && 
        metrics.http.averageResponseTime > thresholds.responseTime) {
      this.triggerAlert('slow_response', {
        value: metrics.http.averageResponseTime,
        threshold: thresholds.responseTime
      });
    }
  }

  triggerAlert(type, data) {
    const now = Date.now();
    const lastAlert = this.alerts.get(type);

    // Implement cooldown to prevent spam
    if (lastAlert && (now - lastAlert) < this.config.alerts.cooldown) {
      return;
    }

    this.alerts.set(type, now);

    const alert = {
      type: type,
      timestamp: now,
      data: data,
      severity: this.getAlertSeverity(type, data)
    };

    this.sendAlert(alert);
  }

  getMetrics(category, timeRange) {
    const categoryMetrics = this.metrics.get(category) || [];
    
    if (timeRange) {
      const startTime = Date.now() - timeRange;
      return categoryMetrics.filter(metric => metric.timestamp > startTime);
    }
    
    return categoryMetrics;
  }

  getAggregatedMetrics(category, timeRange, aggregation = 'average') {
    const metrics = this.getMetrics(category, timeRange);
    
    if (metrics.length === 0) return null;

    // Implement aggregation logic
    return this.aggregateMetrics(metrics, aggregation);
  }
}

module.exports = PerformanceMonitor;
```

## System Metrics Collection

### 1. CPU Monitoring

```javascript
// CPU usage monitoring
class CPUMonitor {
  constructor() {
    this.previousCPUUsage = process.cpuUsage();
    this.previousTime = Date.now();
  }

  getCPUUsage() {
    const currentCPUUsage = process.cpuUsage();
    const currentTime = Date.now();
    
    const userCPUTime = currentCPUUsage.user - this.previousCPUUsage.user;
    const systemCPUTime = currentCPUUsage.system - this.previousCPUUsage.system;
    const totalCPUTime = userCPUTime + systemCPUTime;
    
    const elapsedTime = (currentTime - this.previousTime) * 1000; // Convert to microseconds
    const cpuPercent = (totalCPUTime / elapsedTime) * 100;
    
    this.previousCPUUsage = currentCPUUsage;
    this.previousTime = currentTime;
    
    return {
      user: userCPUTime,
      system: systemCPUTime,
      total: totalCPUTime,
      percentage: cpuPercent
    };
  }

  getLoadAverage() {
    const os = require('os');
    const loadAvg = os.loadavg();
    
    return {
      '1min': loadAvg[0],
      '5min': loadAvg[1],
      '15min': loadAvg[2]
    };
  }
}
```

### 2. Memory Monitoring

```javascript
// Memory usage monitoring
class MemoryMonitor {
  getProcessMemory() {
    const memUsage = process.memoryUsage();
    
    return {
      rss: memUsage.rss,
      heapTotal: memUsage.heapTotal,
      heapUsed: memUsage.heapUsed,
      external: memUsage.external,
      arrayBuffers: memUsage.arrayBuffers,
      heapUtilization: (memUsage.heapUsed / memUsage.heapTotal) * 100
    };
  }

  getSystemMemory() {
    const os = require('os');
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const usedMem = totalMem - freeMem;
    
    return {
      total: totalMem,
      free: freeMem,
      used: usedMem,
      utilization: (usedMem / totalMem) * 100
    };
  }

  getHeapStatistics() {
    const v8 = require('v8');
    return v8.getHeapStatistics();
  }

  getHeapSpaceStatistics() {
    const v8 = require('v8');
    return v8.getHeapSpaceStatistics();
  }
}
```

### 3. Disk Monitoring

```javascript
// Disk usage monitoring
class DiskMonitor {
  getDiskUsage(path = './') {
    const fs = require('fs');
    
    try {
      const stats = fs.statSync(path);
      const { spawn } = require('child_process');
      
      return new Promise((resolve, reject) => {
        const df = spawn('df', ['-h', path]);
        let output = '';
        
        df.stdout.on('data', (data) => {
          output += data.toString();
        });
        
        df.on('close', (code) => {
          if (code === 0) {
            const lines = output.trim().split('\n');
            const diskInfo = lines[1].split(/\s+/);
            
            resolve({
              filesystem: diskInfo[0],
              size: diskInfo[1],
              used: diskInfo[2],
              available: diskInfo[3],
              usePercent: diskInfo[4],
              mountPoint: diskInfo[5]
            });
          } else {
            reject(new Error(`df command failed with code ${code}`));
          }
        });
      });
    } catch (error) {
      return Promise.reject(error);
    }
  }

  getIOStats() {
    const fs = require('fs');
    
    try {
      const procStat = fs.readFileSync('/proc/diskstats', 'utf8');
      const lines = procStat.trim().split('\n');
      
      return lines.map(line => {
        const fields = line.trim().split(/\s+/);
        return {
          major: parseInt(fields[0]),
          minor: parseInt(fields[1]),
          device: fields[2],
          reads: parseInt(fields[3]),
          readsMerged: parseInt(fields[4]),
          sectorsRead: parseInt(fields[5]),
          readTime: parseInt(fields[6]),
          writes: parseInt(fields[7]),
          writesMerged: parseInt(fields[8]),
          sectorsWritten: parseInt(fields[9]),
          writeTime: parseInt(fields[10])
        };
      });
    } catch (error) {
      return [];
    }
  }
}
```

## Application Performance Monitoring

### 1. Event Loop Monitoring

```javascript
// Event loop lag monitoring
class EventLoopMonitor {
  constructor() {
    this.samples = [];
    this.maxSamples = 100;
  }

  measureEventLoopLag() {
    return new Promise((resolve) => {
      const start = process.hrtime.bigint();
      
      setImmediate(() => {
        const lag = Number(process.hrtime.bigint() - start) / 1e6; // Convert to milliseconds
        
        this.samples.push(lag);
        if (this.samples.length > this.maxSamples) {
          this.samples.shift();
        }
        
        resolve(lag);
      });
    });
  }

  getEventLoopUtilization() {
    const { performance } = require('perf_hooks');
    return performance.eventLoopUtilization();
  }

  getAverageEventLoopLag() {
    if (this.samples.length === 0) return 0;
    
    const sum = this.samples.reduce((acc, sample) => acc + sample, 0);
    return sum / this.samples.length;
  }

  getEventLoopStats() {
    return {
      currentLag: this.samples[this.samples.length - 1] || 0,
      averageLag: this.getAverageEventLoopLag(),
      maxLag: Math.max(...this.samples),
      minLag: Math.min(...this.samples),
      utilization: this.getEventLoopUtilization()
    };
  }
}
```

### 2. HTTP Request Monitoring

```javascript
// HTTP request performance monitoring
class HTTPMonitor {
  constructor() {
    this.requests = [];
    this.maxRequests = 1000;
  }

  trackRequest(req, res) {
    const startTime = Date.now();
    
    res.on('finish', () => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const requestData = {
        timestamp: startTime,
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        duration: duration,
        userAgent: req.get('User-Agent'),
        ip: req.ip
      };
      
      this.requests.push(requestData);
      
      if (this.requests.length > this.maxRequests) {
        this.requests.shift();
      }
    });
  }

  getRequestStats(timeRange = 300000) { // Default 5 minutes
    const now = Date.now();
    const recentRequests = this.requests.filter(
      req => (now - req.timestamp) <= timeRange
    );
    
    if (recentRequests.length === 0) {
      return {
        count: 0,
        averageResponseTime: 0,
        errorRate: 0,
        requestsPerSecond: 0
      };
    }
    
    const totalDuration = recentRequests.reduce((sum, req) => sum + req.duration, 0);
    const errorCount = recentRequests.filter(req => req.statusCode >= 400).length;
    
    return {
      count: recentRequests.length,
      averageResponseTime: totalDuration / recentRequests.length,
      errorRate: errorCount / recentRequests.length,
      requestsPerSecond: recentRequests.length / (timeRange / 1000),
      statusCodes: this.getStatusCodeDistribution(recentRequests)
    };
  }

  getStatusCodeDistribution(requests) {
    const distribution = {};
    
    requests.forEach(req => {
      const statusCode = req.statusCode;
      distribution[statusCode] = (distribution[statusCode] || 0) + 1;
    });
    
    return distribution;
  }
}
```

## Database Performance Monitoring

### 1. Query Performance Monitoring

```javascript
// Database query performance monitoring
class DatabaseMonitor {
  constructor() {
    this.queries = [];
    this.maxQueries = 1000;
    this.slowQueryThreshold = 1000; // 1 second
  }

  trackQuery(query, params, startTime, endTime, error = null) {
    const duration = endTime - startTime;
    
    const queryData = {
      timestamp: startTime,
      query: query,
      params: params,
      duration: duration,
      error: error,
      slow: duration > this.slowQueryThreshold
    };
    
    this.queries.push(queryData);
    
    if (this.queries.length > this.maxQueries) {
      this.queries.shift();
    }
    
    // Log slow queries
    if (queryData.slow) {
      console.warn(`Slow query detected (${duration}ms):`, query);
    }
  }

  getQueryStats(timeRange = 300000) {
    const now = Date.now();
    const recentQueries = this.queries.filter(
      query => (now - query.timestamp) <= timeRange
    );
    
    if (recentQueries.length === 0) {
      return {
        count: 0,
        averageDuration: 0,
        slowQueries: 0,
        errorRate: 0
      };
    }
    
    const totalDuration = recentQueries.reduce((sum, query) => sum + query.duration, 0);
    const slowQueries = recentQueries.filter(query => query.slow).length;
    const errorQueries = recentQueries.filter(query => query.error).length;
    
    return {
      count: recentQueries.length,
      averageDuration: totalDuration / recentQueries.length,
      slowQueries: slowQueries,
      errorRate: errorQueries / recentQueries.length,
      queriesPerSecond: recentQueries.length / (timeRange / 1000)
    };
  }

  getSlowQueries(limit = 10) {
    return this.queries
      .filter(query => query.slow)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit);
  }
}
```

### 2. Connection Pool Monitoring

```javascript
// Database connection pool monitoring
class ConnectionPoolMonitor {
  constructor(pool) {
    this.pool = pool;
    this.stats = {
      created: 0,
      destroyed: 0,
      acquired: 0,
      released: 0,
      errors: 0
    };
    
    this.setupEventListeners();
  }

  setupEventListeners() {
    this.pool.on('createSuccess', () => {
      this.stats.created++;
    });
    
    this.pool.on('destroySuccess', () => {
      this.stats.destroyed++;
    });
    
    this.pool.on('acquireSuccess', () => {
      this.stats.acquired++;
    });
    
    this.pool.on('release', () => {
      this.stats.released++;
    });
    
    this.pool.on('createFail', () => {
      this.stats.errors++;
    });
  }

  getPoolStats() {
    return {
      size: this.pool.size,
      available: this.pool.available,
      borrowed: this.pool.borrowed,
      pending: this.pool.pending,
      max: this.pool.max,
      min: this.pool.min,
      stats: { ...this.stats }
    };
  }
}
```

## Trading Performance Metrics

### 1. Trading Statistics

```javascript
// Trading performance monitoring
class TradingMonitor {
  constructor() {
    this.trades = [];
    this.orders = [];
    this.positions = [];
    this.maxRecords = 10000;
  }

  trackTrade(trade) {
    this.trades.push({
      ...trade,
      timestamp: Date.now()
    });
    
    if (this.trades.length > this.maxRecords) {
      this.trades.shift();
    }
  }

  trackOrder(order) {
    this.orders.push({
      ...order,
      timestamp: Date.now()
    });
    
    if (this.orders.length > this.maxRecords) {
      this.orders.shift();
    }
  }

  getTradingStats(timeRange = 86400000) { // Default 24 hours
    const now = Date.now();
    const recentTrades = this.trades.filter(
      trade => (now - trade.timestamp) <= timeRange
    );
    
    if (recentTrades.length === 0) {
      return {
        totalTrades: 0,
        profitableTrades: 0,
        totalPnL: 0,
        winRate: 0,
        averageProfit: 0,
        averageLoss: 0
      };
    }
    
    const profitableTrades = recentTrades.filter(trade => trade.pnl > 0);
    const losingTrades = recentTrades.filter(trade => trade.pnl < 0);
    const totalPnL = recentTrades.reduce((sum, trade) => sum + trade.pnl, 0);
    
    return {
      totalTrades: recentTrades.length,
      profitableTrades: profitableTrades.length,
      losingTrades: losingTrades.length,
      totalPnL: totalPnL,
      winRate: profitableTrades.length / recentTrades.length,
      averageProfit: profitableTrades.length > 0 ? 
        profitableTrades.reduce((sum, trade) => sum + trade.pnl, 0) / profitableTrades.length : 0,
      averageLoss: losingTrades.length > 0 ? 
        losingTrades.reduce((sum, trade) => sum + trade.pnl, 0) / losingTrades.length : 0,
      profitFactor: this.calculateProfitFactor(profitableTrades, losingTrades)
    };
  }

  calculateProfitFactor(profitableTrades, losingTrades) {
    const totalProfit = profitableTrades.reduce((sum, trade) => sum + trade.pnl, 0);
    const totalLoss = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.pnl, 0));
    
    return totalLoss > 0 ? totalProfit / totalLoss : 0;
  }

  getLatencyStats() {
    const recentOrders = this.orders.filter(
      order => order.latency && (Date.now() - order.timestamp) <= 300000
    );
    
    if (recentOrders.length === 0) {
      return { averageLatency: 0, maxLatency: 0, minLatency: 0 };
    }
    
    const latencies = recentOrders.map(order => order.latency);
    
    return {
      averageLatency: latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length,
      maxLatency: Math.max(...latencies),
      minLatency: Math.min(...latencies),
      p95Latency: this.calculatePercentile(latencies, 0.95),
      p99Latency: this.calculatePercentile(latencies, 0.99)
    };
  }

  calculatePercentile(values, percentile) {
    const sorted = values.sort((a, b) => a - b);
    const index = Math.ceil(sorted.length * percentile) - 1;
    return sorted[index];
  }
}
```

## External Monitoring Integration

### 1. Prometheus Integration

Create `monitoring/prometheus.js`:

```javascript
// Prometheus metrics integration
const client = require('prom-client');

class PrometheusMonitor {
  constructor() {
    // Create a Registry
    this.register = new client.Registry();
    
    // Add default metrics
    client.collectDefaultMetrics({ register: this.register });
    
    // Custom metrics
    this.httpRequestDuration = new client.Histogram({
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [0.1, 0.5, 1, 2, 5]
    });
    
    this.tradingMetrics = new client.Gauge({
      name: 'trading_pnl_total',
      help: 'Total P&L from trading',
      labelNames: ['strategy', 'symbol']
    });
    
    this.databaseConnections = new client.Gauge({
      name: 'database_connections_active',
      help: 'Number of active database connections'
    });
    
    this.eventLoopLag = new client.Histogram({
      name: 'nodejs_eventloop_lag_seconds',
      help: 'Lag of event loop in seconds',
      buckets: [0.001, 0.01, 0.1, 1, 10]
    });
    
    // Register metrics
    this.register.registerMetric(this.httpRequestDuration);
    this.register.registerMetric(this.tradingMetrics);
    this.register.registerMetric(this.databaseConnections);
    this.register.registerMetric(this.eventLoopLag);
  }

  updateHttpMetrics(method, route, statusCode, duration) {
    this.httpRequestDuration
      .labels(method, route, statusCode)
      .observe(duration / 1000);
  }

  updateTradingMetrics(strategy, symbol, pnl) {
    this.tradingMetrics
      .labels(strategy, symbol)
      .set(pnl);
  }

  updateDatabaseMetrics(activeConnections) {
    this.databaseConnections.set(activeConnections);
  }

  updateEventLoopLag(lag) {
    this.eventLoopLag.observe(lag / 1000);
  }

  getMetrics() {
    return this.register.metrics();
  }
}

module.exports = PrometheusMonitor;
```

### 2. Grafana Dashboard Configuration

Create `monitoring/grafana-dashboard.json`:

```json
{
  "dashboard": {
    "id": null,
    "title": "Meme Coin Trader Performance",
    "tags": ["trading", "performance"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "System Overview",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(process_cpu_user_seconds_total[5m]) * 100",
            "legendFormat": "CPU Usage %"
          },
          {
            "expr": "process_resident_memory_bytes / 1024 / 1024",
            "legendFormat": "Memory MB"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "HTTP Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_request_duration_seconds_count[5m])",
            "legendFormat": "Requests/sec"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 3,
        "title": "Trading P&L",
        "type": "graph",
        "targets": [
          {
            "expr": "trading_pnl_total",
            "legendFormat": "{{strategy}} - {{symbol}}"
          }
        ],
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
      },
      {
        "id": 4,
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "database_connections_active",
            "legendFormat": "Active Connections"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}
      },
      {
        "id": 5,
        "title": "Event Loop Lag",
        "type": "graph",
        "targets": [
          {
            "expr": "nodejs_eventloop_lag_seconds",
            "legendFormat": "Event Loop Lag"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

## Error Reporting Setup

### 1. Built-in Error Reporter

Create `app/src/services/ProductionErrorReporter.js`:

```javascript
// Production error reporting service
class ProductionErrorReporter {
  constructor(config) {
    this.config = config;
    this.errorQueue = [];
    this.maxQueueSize = 1000;
    this.flushInterval = 30000; // 30 seconds
    
    this.setupErrorHandlers();
    this.startErrorProcessor();
  }

  setupErrorHandlers() {
    // Uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.reportError(error, {
        type: 'uncaughtException',
        severity: 'critical'
      });
    });

    // Unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      this.reportError(reason, {
        type: 'unhandledRejection',
        severity: 'high',
        promise: promise
      });
    });

    // Warning events
    process.on('warning', (warning) => {
      this.reportError(warning, {
        type: 'warning',
        severity: 'low'
      });
    });
  }

  reportError(error, context = {}) {
    const errorReport = {
      timestamp: Date.now(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: error.code
      },
      context: {
        ...context,
        pid: process.pid,
        platform: process.platform,
        nodeVersion: process.version,
        memoryUsage: process.memoryUsage(),
        uptime: process.uptime()
      },
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        TRADING_MODE: process.env.TRADING_MODE,
        DATABASE_TYPE: process.env.DATABASE_TYPE
      }
    };

    this.queueError(errorReport);
  }

  queueError(errorReport) {
    this.errorQueue.push(errorReport);
    
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift(); // Remove oldest error
    }

    // Immediate processing for critical errors
    if (errorReport.context.severity === 'critical') {
      this.processError(errorReport);
    }
  }

  startErrorProcessor() {
    setInterval(() => {
      this.flushErrorQueue();
    }, this.flushInterval);
  }

  flushErrorQueue() {
    if (this.errorQueue.length === 0) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    errors.forEach(error => this.processError(error));
  }

  processError(errorReport) {
    // Log to file
    this.logError(errorReport);

    // Send to external services
    if (this.config.sentry?.enabled) {
      this.sendToSentry(errorReport);
    }

    if (this.config.webhook?.enabled) {
      this.sendToWebhook(errorReport);
    }

    if (this.config.email?.enabled) {
      this.sendEmailAlert(errorReport);
    }
  }

  logError(errorReport) {
    const logEntry = {
      timestamp: new Date(errorReport.timestamp).toISOString(),
      level: 'error',
      message: errorReport.error.message,
      error: errorReport.error,
      context: errorReport.context
    };

    console.error(JSON.stringify(logEntry));
  }

  async sendToSentry(errorReport) {
    if (!this.sentryClient) {
      const Sentry = require('@sentry/node');
      Sentry.init({
        dsn: this.config.sentry.dsn,
        environment: process.env.NODE_ENV
      });
      this.sentryClient = Sentry;
    }

    this.sentryClient.captureException(new Error(errorReport.error.message), {
      tags: {
        severity: errorReport.context.severity,
        type: errorReport.context.type
      },
      extra: errorReport.context
    });
  }

  async sendToWebhook(errorReport) {
    try {
      const response = await fetch(this.config.webhook.url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: `🚨 Error in Meme Coin Trader: ${errorReport.error.message}`,
          attachments: [{
            color: this.getSeverityColor(errorReport.context.severity),
            fields: [
              {
                title: 'Error Type',
                value: errorReport.context.type,
                short: true
              },
              {
                title: 'Severity',
                value: errorReport.context.severity,
                short: true
              },
              {
                title: 'Environment',
                value: process.env.NODE_ENV,
                short: true
              },
              {
                title: 'Timestamp',
                value: new Date(errorReport.timestamp).toISOString(),
                short: true
              }
            ]
          }]
        })
      });

      if (!response.ok) {
        console.error('Failed to send webhook:', response.statusText);
      }
    } catch (error) {
      console.error('Error sending webhook:', error);
    }
  }

  getSeverityColor(severity) {
    const colors = {
      critical: '#ff0000',
      high: '#ff8800',
      medium: '#ffaa00',
      low: '#ffdd00'
    };
    return colors[severity] || '#cccccc';
  }
}

module.exports = ProductionErrorReporter;
```

### 2. Sentry Integration

```bash
# Install Sentry
npm install @sentry/node @sentry/tracing

# Configure Sentry
export SENTRY_DSN=your_sentry_dsn
export SENTRY_ENVIRONMENT=production
export SENTRY_RELEASE=1.0.0
```

## Alerting and Notifications

### 1. Alert Manager

Create `monitoring/AlertManager.js`:

```javascript
// Alert management system
class AlertManager {
  constructor(config) {
    this.config = config;
    this.alerts = new Map();
    this.alertHistory = [];
    this.cooldowns = new Map();
  }

  createAlert(type, data, severity = 'medium') {
    const alert = {
      id: this.generateAlertId(),
      type: type,
      severity: severity,
      data: data,
      timestamp: Date.now(),
      status: 'active',
      acknowledged: false
    };

    // Check cooldown
    if (this.isInCooldown(type)) {
      return null;
    }

    this.alerts.set(alert.id, alert);
    this.alertHistory.push(alert);
    this.setCooldown(type);

    this.processAlert(alert);
    return alert;
  }

  processAlert(alert) {
    // Send notifications based on severity
    const channels = this.getNotificationChannels(alert.severity);
    
    channels.forEach(channel => {
      this.sendNotification(channel, alert);
    });

    // Log alert
    console.warn(`Alert [${alert.severity.toUpperCase()}]: ${alert.type}`, alert.data);
  }

  getNotificationChannels(severity) {
    const channelMap = {
      critical: ['email', 'sms', 'webhook', 'slack'],
      high: ['email', 'webhook', 'slack'],
      medium: ['webhook', 'slack'],
      low: ['slack']
    };

    return channelMap[severity] || ['slack'];
  }

  async sendNotification(channel, alert) {
    try {
      switch (channel) {
        case 'email':
          await this.sendEmailNotification(alert);
          break;
        case 'sms':
          await this.sendSMSNotification(alert);
          break;
        case 'webhook':
          await this.sendWebhookNotification(alert);
          break;
        case 'slack':
          await this.sendSlackNotification(alert);
          break;
      }
    } catch (error) {
      console.error(`Failed to send ${channel} notification:`, error);
    }
  }

  isInCooldown(type) {
    const cooldownEnd = this.cooldowns.get(type);
    return cooldownEnd && Date.now() < cooldownEnd;
  }

  setCooldown(type) {
    const cooldownDuration = this.config.alerts?.cooldown || 300000; // 5 minutes
    this.cooldowns.set(type, Date.now() + cooldownDuration);
  }

  acknowledgeAlert(alertId, userId) {
    const alert = this.alerts.get(alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedBy = userId;
      alert.acknowledgedAt = Date.now();
    }
  }

  resolveAlert(alertId, userId) {
    const alert = this.alerts.get(alertId);
    if (alert) {
      alert.status = 'resolved';
      alert.resolvedBy = userId;
      alert.resolvedAt = Date.now();
      this.alerts.delete(alertId);
    }
  }

  getActiveAlerts() {
    return Array.from(this.alerts.values());
  }

  getAlertHistory(limit = 100) {
    return this.alertHistory
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }
}

module.exports = AlertManager;
```

## Dashboard Configuration

### 1. Performance Dashboard

Create `app/src/components/PerformanceDashboard.jsx`:

```jsx
// Performance monitoring dashboard
import React, { useState, useEffect } from 'react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

const PerformanceDashboard = () => {
  const [metrics, setMetrics] = useState({
    system: {},
    application: {},
    trading: {},
    database: {}
  });

  const [alerts, setAlerts] = useState([]);
  const [timeRange, setTimeRange] = useState('1h');

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await fetch(`/api/metrics?range=${timeRange}`);
        const data = await response.json();
        setMetrics(data);
      } catch (error) {
        console.error('Failed to fetch metrics:', error);
      }
    };

    const fetchAlerts = async () => {
      try {
        const response = await fetch('/api/alerts/active');
        const data = await response.json();
        setAlerts(data);
      } catch (error) {
        console.error('Failed to fetch alerts:', error);
      }
    };

    fetchMetrics();
    fetchAlerts();

    const interval = setInterval(() => {
      fetchMetrics();
      fetchAlerts();
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [timeRange]);

  const systemChartData = {
    labels: metrics.system.timestamps || [],
    datasets: [
      {
        label: 'CPU Usage %',
        data: metrics.system.cpu || [],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
      },
      {
        label: 'Memory Usage %',
        data: metrics.system.memory || [],
        borderColor: 'rgb(54, 162, 235)',
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
      }
    ]
  };

  const tradingChartData = {
    labels: metrics.trading.timestamps || [],
    datasets: [
      {
        label: 'P&L',
        data: metrics.trading.pnl || [],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
      }
    ]
  };

  return (
    <div className="performance-dashboard">
      <div className="dashboard-header">
        <h2>Performance Dashboard</h2>
        <select 
          value={timeRange} 
          onChange={(e) => setTimeRange(e.target.value)}
        >
          <option value="1h">Last Hour</option>
          <option value="6h">Last 6 Hours</option>
          <option value="24h">Last 24 Hours</option>
          <option value="7d">Last 7 Days</option>
        </select>
      </div>

      {/* Alerts Section */}
      {alerts.length > 0 && (
        <div className="alerts-section">
          <h3>Active Alerts</h3>
          {alerts.map(alert => (
            <div key={alert.id} className={`alert alert-${alert.severity}`}>
              <span className="alert-type">{alert.type}</span>
              <span className="alert-message">{alert.data.message}</span>
              <span className="alert-time">
                {new Date(alert.timestamp).toLocaleTimeString()}
              </span>
            </div>
          ))}
        </div>
      )}

      {/* System Metrics */}
      <div className="metrics-section">
        <h3>System Metrics</h3>
        <div className="chart-container">
          <Line data={systemChartData} options={{
            responsive: true,
            scales: {
              y: {
                beginAtZero: true,
                max: 100
              }
            }
          }} />
        </div>
      </div>

      {/* Trading Metrics */}
      <div className="metrics-section">
        <h3>Trading Performance</h3>
        <div className="chart-container">
          <Line data={tradingChartData} options={{
            responsive: true
          }} />
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="kpi-section">
        <div className="kpi-card">
          <h4>Response Time</h4>
          <span className="kpi-value">
            {metrics.application.averageResponseTime || 0}ms
          </span>
        </div>
        <div className="kpi-card">
          <h4>Error Rate</h4>
          <span className="kpi-value">
            {((metrics.application.errorRate || 0) * 100).toFixed(2)}%
          </span>
        </div>
        <div className="kpi-card">
          <h4>Active Trades</h4>
          <span className="kpi-value">
            {metrics.trading.activeTrades || 0}
          </span>
        </div>
        <div className="kpi-card">
          <h4>Win Rate</h4>
          <span className="kpi-value">
            {((metrics.trading.winRate || 0) * 100).toFixed(1)}%
          </span>
        </div>
      </div>
    </div>
  );
};

export default PerformanceDashboard;
```

## Performance Optimization

### 1. Optimization Recommendations

Based on monitoring data, implement these optimizations:

```javascript
// Performance optimization recommendations
class PerformanceOptimizer {
  constructor(monitor) {
    this.monitor = monitor;
    this.optimizations = [];
  }

  analyzePerformance() {
    const metrics = this.monitor.getAggregatedMetrics('application', 3600000); // 1 hour
    const recommendations = [];

    // Memory optimization
    if (metrics.memory.heapUtilization > 80) {
      recommendations.push({
        type: 'memory',
        priority: 'high',
        action: 'Increase heap size or implement memory cleanup',
        details: `Current heap utilization: ${metrics.memory.heapUtilization}%`
      });
    }

    // CPU optimization
    if (metrics.cpu.usage > 70) {
      recommendations.push({
        type: 'cpu',
        priority: 'medium',
        action: 'Optimize CPU-intensive operations or scale horizontally',
        details: `Current CPU usage: ${metrics.cpu.usage}%`
      });
    }

    // Event loop optimization
    if (metrics.eventLoop.averageLag > 100) {
      recommendations.push({
        type: 'eventloop',
        priority: 'high',
        action: 'Reduce blocking operations in event loop',
        details: `Average event loop lag: ${metrics.eventLoop.averageLag}ms`
      });
    }

    return recommendations;
  }

  applyOptimizations() {
    const recommendations = this.analyzePerformance();
    
    recommendations.forEach(rec => {
      switch (rec.type) {
        case 'memory':
          this.optimizeMemory();
          break;
        case 'cpu':
          this.optimizeCPU();
          break;
        case 'eventloop':
          this.optimizeEventLoop();
          break;
      }
    });
  }

  optimizeMemory() {
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    // Clear caches
    this.clearCaches();
  }

  optimizeCPU() {
    // Reduce polling frequency
    this.reducePollFrequency();
    
    // Implement request queuing
    this.implementRequestQueuing();
  }

  optimizeEventLoop() {
    // Move CPU-intensive tasks to worker threads
    this.moveToWorkerThreads();
    
    // Implement setImmediate for long-running operations
    this.implementAsyncBreaks();
  }
}
```

---

*This performance monitoring guide provides comprehensive setup instructions for monitoring the Meme Coin Trader application in production. Regular monitoring and optimization are essential for maintaining optimal performance.*