const logger = require('../utils/logger.js');

// Initialize error handlers as null - will use fallback implementations
const ipcErrorHandler = null;

/**
 * @typedef {Object} IPCConfiguration
 * @property {number} defaultTimeout - Default timeout for IPC calls
 * @property {number} retryAttempts - Number of retry attempts
 * @property {number} retryDelay - Delay between retries
 */

/**
 * @typedef {Object} IPCResponse
 * @property {boolean} success - Whether the operation was successful
 * @property {*} [data] - Response data
 * @property {import('../utils/IPCErrorHandler.js').IPCError} [error] - Error information if operation failed
 * @property {number} timestamp - Response timestamp
 */

/**
 * @callback IPCCallback
 * @param {Event} event - IPC event
 * @param {...any} args - Arguments passed with the event
 */

class IPCService {
    constructor() {
        // this.defaultTimeout = 10000; // 10 seconds
        // this.retryAttempts = 3;
        // this.retryDelay = 1000; // 1 second
        // this.errorHandler = ipcErrorHandler || this.createFallbackErrorHandler();
    }

    // Getter for backward compatibility
    get isElectron() {
        return this.isElectronEnvironment();
    }

    /**
     * Create fallback error handler if IPCErrorHandler is not available
     * @returns {Object} Fallback error handler
     */
    createFallbackErrorHandler() {
        const errorCounts = new Map();
        const lastErrorTime = new Map();
        return {
            executeWithFullHandling: async (fn, channel, _priority = 'default') => {
            const startTime = Date.now();
            try {
                const result = await fn();
                const duration = Date.now() - startTime;
                if (duration > 5000) {
                    logger.info(`IPC ${channel} completed in ${duration}ms`);
                }
                return {success, data, timestamp()};
            } catch (error) {
                const duration = Date.now() - startTime;
                const errorCode = this.getErrorCode(error);
                const key = `${channel}:${errorCode}`;
                const currentCount = errorCounts.get(key) || 0;
                errorCounts.set(key, currentCount + 1);
                lastErrorTime.set(key, Date.now());
                logger.error(`IPC Error on ${channel} after ${duration}ms:`, {
                    error,
                    code,
                    stack === 'development' ? error.stack
            })
                ;
                return {
                    success,
                    error: {
                        code,
                        message,
                        channel,
                        timestamp(),
                        context: {duration}
                    },
                    timestamp()
                };
            }
        }
    ,
        createErrorResponse: (code, message, channel, context = null) => ({
            success,
            error: {code, message, channel, timestamp(), context},
            timestamp()
        }),
            getErrorStatistics
    :
        () => {
            const stats = {
                totalErrors,
                errorsByChannel: {},
                errorsByCode: {},
                recentErrors
            };
            const now = Date.now();
            const recentWindow = 300000;
            for (const [key, count] of errorCounts.entries()) {
                const [channel, code] = key.split(':');
                const lastError = lastErrorTime.get(key) || 0;
                stats.totalErrors += count;
                if (!stats.errorsByChannel[channel]) {
                    stats.errorsByChannel[channel] = 0;
                }
                stats.errorsByChannel[channel] += count;
                if (!stats.errorsByCode[code]) {
                    stats.errorsByCode[code] = 0;
                }
                stats.errorsByCode[code] += count;
                if (now - lastError < recentWindow) {
                    stats.recentErrors += count;
                }
            }
            return stats;
        },
            resetStatistics
    :
        () => {
            errorCounts.clear();
            lastErrorTime.clear();
        },
            shouldCircuitBreak
    :
        (channel, threshold = 10, timeWindow = 300000) => {
            const now = Date.now();
            let recentErrors = 0;
            for (const [key, count] of errorCounts.entries()) {
                if (key.startsWith(channel + ':')) {
                    const lastError = lastErrorTime.get(key) || 0;
                    if (now - lastError < timeWindow) {
                        recentErrors += count;
                    }
                }
            }
            return recentErrors >= threshold;
        },
            updateConfiguration
    :
        () => {
        }
    }
        ;
    }

    /**
     * Get standardized error code from error object
     * @param {Error} error - Error object
     * @returns {string}
     */
    getErrorCode(error) {
        const message = error.message.toLowerCase();

        if (message.includes('timeout')) return 'TIMEOUT';
        if (message.includes('not available')) return 'SERVICE_UNAVAILABLE';
        if (message.includes('not initialized')) return 'NOT_INITIALIZED';
        if (message.includes('not found')) return 'NOT_FOUND';
        if (message.includes('permission')) return 'PERMISSION_DENIED';
        if (message.includes('rate limit')) return 'RATE_LIMITED';
        if (message.includes('connection')) return 'CONNECTION_ERROR';
        if (message.includes('database')) return 'DATABASE_ERROR';
        if (message.includes('busy')) return 'DATABASE_BUSY';
        if (message.includes('network')) return 'NETWORK_ERROR';
        if (message.includes('invalid')) return 'INVALID_INPUT';
        if (message.includes('unauthorized')) return 'UNAUTHORIZED';
        if (message.includes('forbidden')) return 'FORBIDDEN';

        return 'UNKNOWN_ERROR';
    }

    // Configuration methods
    getConfiguration() {
        return {
            defaultTimeout,
            retryAttempts,
            retryDelay,
            isElectronAvailable()
        };
    }

    /**
     * Update IPC service configuration
     * @param {IPCConfiguration} config - Configuration object
     */
    updateConfiguration(config) {
        if (config.defaultTimeout) this.defaultTimeout = config.defaultTimeout;
        if (config.retryAttempts) this.retryAttempts = config.retryAttempts;
        if (config.retryDelay) this.retryDelay = config.retryDelay;
    }

    // Utility methods
    isAvailable() {
        return typeof window !== 'undefined' && !!window.electronAPI;
    }

    isElectronEnvironment() {
        return this.isAvailable();
    }

    async testConnectivity() {
        try {
            const result = await this.healthCheck();
            return result.success === true;
        } catch (error) {
            return false;
        }
    }

    // Event handling
    /**
     * Subscribe to IPC events
     * @param {string} channel - Event channel name
     * @param {IPCCallback} callback - Event callback function
     * @returns {Function} Unsubscribe function
     */
    on(channel, callback) {
        if (window.electronAPI && window.electronAPI.on) {
            const unsubscribe = window.electronAPI.on(channel, callback);
            return typeof unsubscribe === 'function' ? unsubscribe : () => {
            };
        }
        return () => {
        }; // Return empty unsubscribe function
    }

    // Enhanced IPC call methods with error handling and retry
    /**
     * Quick IPC call with reduced timeout for non-critical operations
     * @param {Function} fn - Function to call
     * @param {string} channel - Channel name for logging
     * @param {...*} args - Arguments to pass to function
     * @returns {Promise<IPCResponse>} Response object
     */
    async quickIPCCall(fn, channel, ...args) {
        if (!this.isAvailable()) {
            return this.errorHandler.createErrorResponse(
                'SERVICE_UNAVAILABLE',
                'Electron API not available',
                channel,
            );
        }

        return this.errorHandler.executeWithFullHandling(
            () => fn(...args),
            channel,
            'quick',
        );
    }

    /**
     * Standard IPC call with full timeout and retry logic
     * @param {Function} fn - Function to call
     * @param {string} channel - Channel name for logging
     * @param {...*} args - Arguments to pass to function
     * @returns {Promise<IPCResponse>} Response object
     */
    async standardIPCCall(fn, channel, ...args) {
        if (!this.isAvailable()) {
            return this.errorHandler.createErrorResponse(
                'SERVICE_UNAVAILABLE',
                'Electron API not available',
                channel,
            );
        }

        return this.errorHandler.executeWithFullHandling(
            () => fn(...args),
            channel,
            'default',
        );
    }

    /**
     * Critical IPC call with full timeout and retry logic
     * @param {Function} fn - Function to call
     * @param {string} channel - Channel name for logging
     * @param {...*} args - Arguments to pass to function
     * @returns {Promise<IPCResponse>} Response object
     */
    async criticalIPCCall(fn, channel, ...args) {
        if (!this.isAvailable()) {
            return this.errorHandler.createErrorResponse(
                'SERVICE_UNAVAILABLE',
                'Electron API not available',
                channel,
            );
        }

        return this.errorHandler.executeWithFullHandling(
            () => fn(...args),
            channel,
            'critical',
        );
    }

    /**
     * Trading-specific IPC call with critical error handling
     * @param {Function} fn - Function to call
     * @param {string} channel - Channel name for logging
     * @param {...*} args - Arguments to pass to function
     * @returns {Promise<IPCResponse>} Response object
     */
    async tradingIPCCall(fn, channel, ...args) {
        if (!this.isAvailable()) {
            return this.errorHandler.createErrorResponse(
                'SERVICE_UNAVAILABLE',
                'Electron API not available',
                channel,
            );
        }

        return this.errorHandler.executeWithFullHandling(
            () => fn(...args),
            channel,
            'trading',
        );
    }

    /**
     * Safe IPC call that doesn't throw errors
     * @param {Function} fn - Function to call
     * @param {string} channel - Channel name for logging
     * @param {...*} args - Arguments to pass to function
     * @returns {Promise<IPCResponse>} Response object
     */
    async safeIPCCall(fn, channel, ...args) {
        return this.quickIPCCall(fn, channel, ...args);
    }

    // Core bot control methods
    async startBot() {
        return this.criticalIPCCall(
            () => window.electronAPI.startBot(),
            'startBot',
        );
    }

    async stopBot() {
        return this.criticalIPCCall(
            () => window.electronAPI.stopBot(),
            'stopBot',
        );
    }

    async getBotStatus() {
        return this.quickIPCCall(
            () => window.electronAPI.getBotStatus(),
            'getBotStatus',
        );
    }

    // Real-time status and health monitoring
    async getRealTimeStatus() {
        return this.standardIPCCall(
            () => window.electronAPI.getRealTimeStatus(),
            'getRealTimeStatus',
        );
    }

    async getSystemHealth() {
        return this.standardIPCCall(
            () => window.electronAPI.getSystemHealth(),
            'getSystemHealth',
        );
    }

    /**
     * Get health status of a specific component
     * @param {string} componentName - Name of the component
     * @returns {Promise<IPCResponse>} Component health status
     */
    async getComponentHealth(componentName) {
        return this.standardIPCCall(
            () => window.electronAPI.getComponentHealth(componentName),
            'getComponentHealth',
        );
    }

    async getSystemInfo() {
        return this.standardIPCCall(
            () => window.electronAPI.getSystemInfo(),
            'getSystemInfo',
        );
    }

    async healthCheck() {
        return this.standardIPCCall(
            () => window.electronAPI.healthCheck(),
            'healthCheck',
        );
    }

    // System metrics and monitoring
    async getSystemMetrics() {
        return this.standardIPCCall(
            () => window.electronAPI.getSystemMetrics(),
            'getSystemMetrics',
        );
    }

    async getActiveBots() {
        return this.standardIPCCall(
            () => window.electronAPI.getActiveBots(),
            'getActiveBots',
        );
    }

    async getSystemAlerts() {
        return this.standardIPCCall(
            () => window.electronAPI.getSystemAlerts(),
            'getSystemAlerts',
        );
    }

    async startHealthMonitoring() {
        return this.standardIPCCall(
            () => window.electronAPI.startHealthMonitoring(),
            'startHealthMonitoring',
        );
    }

    async stopHealthMonitoring() {
        return this.standardIPCCall(
            () => window.electronAPI.stopHealthMonitoring(),
            'stopHealthMonitoring',
        );
    }

    async getMonitoringStatistics() {
        return this.standardIPCCall(
            () => window.electronAPI.getMonitoringStatistics(),
            'getMonitoringStatistics',
        );
    }

    /**
     * Run health check for a specific component
     * @param {string} [componentName] - Name of the component (optional)
     * @returns {Promise<IPCResponse>} Health check results
     */
    async runHealthCheck(componentName) {
        return this.standardIPCCall(
            () => window.electronAPI.runHealthCheck(componentName),
            'runHealthCheck',
        );
    }

    async initializeTrading() {
        return this.criticalIPCCall(
            () => window.electronAPI.initializeTrading(),
            'initializeTrading',
        );
    }

    // Portfolio and trading data
    async getPortfolioSummary() {
        return this.standardIPCCall(
            () => window.electronAPI.getPortfolioSummary(),
            'getPortfolioSummary',
        );
    }

    /**
     * Get performance metrics
     * @returns {Promise<IPCResponse>} Performance metrics
     */
    async getPerformanceMetrics() {
        return this.standardIPCCall(
            () => window.electronAPI.getPerformanceMetrics(),
            'getPerformanceMetrics',
        );
    }

    /**
     * Get trade history
     * @param {number} [limit=100] - Maximum number of trades to return
     * @returns {Promise<IPCResponse>} Trade history
     */
    async getTradeHistory(limit = 100) {
        return this.standardIPCCall(
            () => window.electronAPI.getTradeHistory(limit),
            'getTradeHistory',
        );
    }

    // Market data
    /**
     * Get market data for a symbol
     * @param {string} symbol - Trading symbol
     * @param {string} [_timeframe] - Timeframe for data (unused parameter)
     * @returns {Promise<IPCResponse>} Market data
     */
    async getMarketData(symbol, _timeframe) {
        if (typeof symbol !== 'string') {
            return this.errorHandler.createErrorResponse(
                'INVALID_INPUT',
                'Symbol must be a string',
                'getMarketData',
            );
        }
        return this.standardIPCCall(
            () => window.electronAPI.getMarketData(symbol),
            'getMarketData',
        );
    }

    async getMarketOverview() {
        return this.standardIPCCall(
            () => window.electronAPI.getMarketOverview(),
            'getMarketOverview',
        );
    }

    // Specialized trading features
    async getWhaleSignals() {
        return this.standardIPCCall(
            () => window.electronAPI.getWhaleSignals(),
            'getWhaleSignals',
        );
    }

    async getMemeCoinOpportunities() {
        return this.standardIPCCall(
            () => window.electronAPI.getMemeCoinOpportunities(),
            'getMemeCoinOpportunities',
        );
    }

    async startMemeScanner() {
        return this.tradingIPCCall(
            () => window.electronAPI.startMemeCoinScanner(),
            'startMemeScanner',
        );
    }

    async stopMemeScanner() {
        return this.tradingIPCCall(
            () => window.electronAPI.stopMemeCoinScanner(),
            'stopMemeScanner',
        );
    }

    // Grid bot management
    /**
     * Start a grid bot with specified configuration
     * @param {Object} config - Grid bot configuration
     * @returns {Promise<IPCResponse>} Start result
     */
    async startGridBot(config) {
        return this.tradingIPCCall(
            () => window.electronAPI.startGrid(config),
            'startGridBot',
        );
    }

    /**
     * Stop a grid bot for a specific symbol
     * @param {string} symbol - Trading symbol
     * @returns {Promise<IPCResponse>} Stop result
     */
    async stopGridBot(symbol) {
        if (typeof symbol !== 'string') {
            return this.errorHandler.createErrorResponse(
                'INVALID_INPUT',
                'Symbol must be a string',
                'stopGridBot',
            );
        }
        return this.tradingIPCCall(
            () => window.electronAPI.stopGrid(symbol),
            'stopGridBot',
        );
    }

    async stopAllGridBots() {
        return this.tradingIPCCall(
            () => window.electronAPI.stopAllGrids(),
            'stopAllGridBots',
        );
    }

    /**
     * Get grid bot history for a specific symbol
     * @param {string} symbol - Trading symbol
     * @returns {Promise<IPCResponse>} Grid bot history
     */
    async getGridBotHistory(symbol) {
        return this.standardIPCCall(
            () => window.electronAPI.getGridHistory(symbol),
            'getGridBotHistory',
        );
    }

    // Autonomous trading
    async startAutonomousTrading() {
        return this.criticalIPCCall(
            () => window.electronAPI.startAutonomousTrading(),
            'startAutonomousTrading',
        );
    }

    async stopAutonomousTrading() {
        return this.criticalIPCCall(
            () => window.electronAPI.stopAutonomousTrading(),
            'stopAutonomousTrading',
        );
    }

    async getAutonomousStatus() {
        return this.quickIPCCall(
            () => window.electronAPI.getAutonomousStatus(),
            'getAutonomousStatus',
        );
    }

    // Database operations
    async getDatabaseStatus() {
        return this.standardIPCCall(
            () => window.electronAPI.getDatabaseStatus(),
            'getDatabaseStatus',
        );
    }

    async getDatabaseMetrics() {
        return this.standardIPCCall(
            () => window.electronAPI.getDatabaseMetrics(),
            'getDatabaseMetrics',
        );
    }

    async checkDatabaseReady() {
        return this.quickIPCCall(
            () => window.electronAPI.checkDatabaseReady(),
            'checkDatabaseReady',
        );
    }

    // Trading operations
    /**
     * Execute a trade with specified parameters
     * @param {Object} tradeParams - Trade parameters
     * @returns {Promise<IPCResponse>} Trade execution result
     */
    async executeTrade(tradeParams) {
        return this.criticalIPCCall(
            () => window.electronAPI.executeTrade(tradeParams),
            'executeTrade',
        );
    }

    /**
     * Get open orders for a symbol
     * @param {string} [symbol] - Trading symbol (optional)
     * @returns {Promise<IPCResponse>} Open orders
     */
    async getOpenOrders(symbol) {
        return this.standardIPCCall(
            () => window.electronAPI.getOpenOrders(symbol),
            'getOpenOrders',
        );
    }

    /**
     * Cancel a specific order
     * @param {string} id - Order ID
     * @param {string} symbol - Trading symbol
     * @returns {Promise<IPCResponse>} Cancel result
     */
    async cancelOrder(id, symbol) {
        if (typeof id !== 'string' || typeof symbol !== 'string') {
            return this.errorHandler.createErrorResponse(
                'INVALID_INPUT',
                'Order ID and symbol must be strings',
                'cancelOrder',
            );
        }
        return this.tradingIPCCall(
            () => window.electronAPI.cancelOrder(id),
            'cancelOrder',
        );
    }

    /**
     * Cancel all orders for a symbol
     * @param {string} symbol - Trading symbol
     * @returns {Promise<IPCResponse>} Cancel result
     */
    async cancelAllOrders(symbol) {
        if (typeof symbol !== 'string') {
            return this.errorHandler.createErrorResponse(
                'INVALID_INPUT',
                'Symbol must be a string',
                'cancelAllOrders',
            );
        }
        return this.tradingIPCCall(
            () => window.electronAPI.cancelAllOrders(),
            'cancelAllOrders',
        );
    }

    // Logging
    /**
     * Get system logs
     * @param {string} [level='info'] - Log level filter
     * @param {number} [limit=50] - Maximum number of logs
     * @returns {Promise<IPCResponse>} Log data
     */
    async getLogs(level = 'info', limit = 50) {
        return this.standardIPCCall(
            () => window.electronAPI.getLogs(level, limit),
            'getLogs',
        );
    }

    // Error recovery methods
    /**
     * Restart a component with specified strategy
     * @param {string} componentName - Name of component to restart
     * @param {string} [strategy='immediate'] - Restart strategy
     * @param {Object} [context={}] - Additional context for restart
     * @returns {Promise<IPCResponse>} Restart result
     */
    async restartComponent(componentName, strategy = 'immediate', context = {}) {
        return this.criticalIPCCall(
            () => window.electronAPI.restartComponent({componentName, strategy, context}),
            'restartComponent',
        );
    }

    /**
     * Recover from an error condition
     * @param {Object} errorContext - Error context
     * @returns {Promise<IPCResponse>} Recovery result
     */
    async recoverFromError(errorContext) {
        return this.criticalIPCCall(
            () => window.electronAPI.invoke('recover-from-error', errorContext),
            'recoverFromError',
        );
    }

    /**
     * Isolate a component
     * @param {string} componentName - Name of component to isolate
     * @param {Object} [context={}] - Additional context
     * @returns {Promise<IPCResponse>} Isolation result
     */
    async isolateComponent(componentName, context = {}) {
        return this.criticalIPCCall(
            () => window.electronAPI.invoke('isolate-component', {componentName, context}),
            'isolateComponent',
        );
    }

    /**
     * Recover trading system
     * @param {Object} [params={}] - Recovery parameters
     * @returns {Promise<IPCResponse>} Recovery result
     */
    async recoverTradingSystem(params = {}) {
        return this.criticalIPCCall(
            () => window.electronAPI.invoke('recover-trading-system', params),
            'recoverTradingSystem',
        );
    }

    /**
     * Trigger emergency protocols
     * @param {string} componentName - Component name
     * @param {*} error - Error object
     * @param {Object} [context={}] - Additional context
     * @returns {Promise<IPCResponse>} Emergency protocol result
     */
    async triggerEmergencyProtocols(componentName, error, context = {}) {
        return this.criticalIPCCall(
            () => window.electronAPI.invoke('trigger-emergency-protocols', {componentName, error, context}),
            'triggerEmergencyProtocols',
        );
    }

    // Additional methods referenced in TypeScript definitions
    /**
     * Get tracked whale wallets
     * @returns {Promise<IPCResponse>} Tracked whales
     */
    async getTrackedWhales() {
        return this.standardIPCCall(
            () => window.electronAPI.getTrackedWhales(),
            'getTrackedWhales',
        );
    }

    /**
     * Get sentiment data for a symbol
     * @param {string} symbol - Trading symbol
     * @returns {Promise<IPCResponse>} Sentiment data
     */
    async getSentimentData(symbol) {
        if (typeof symbol !== 'string') {
            return this.errorHandler.createErrorResponse(
                'INVALID_INPUT',
                'Symbol must be a string',
                'getSentimentData',
            );
        }
        return this.standardIPCCall(
            () => window.electronAPI.getSentimentData(symbol),
            'getSentimentData',
        );
    }

    /**
     * Get price history for a symbol
     * @param {string} symbol - Trading symbol
     * @param {string} [timeframe] - Timeframe for data (unused parameter)
     * @returns {Promise<IPCResponse>} Price history
     */
    async getPriceHistory(symbol, timeframe) {
        if (typeof symbol !== 'string') {
            return this.errorHandler.createErrorResponse(
                'INVALID_INPUT',
                'Symbol must be a string',
                'getPriceHistory',
            );
        }
        return this.standardIPCCall(
            () => window.electronAPI.getPriceHistory(symbol, timeframe),
            'getPriceHistory',
        );
    }

    /**
     * Get asset allocation
     * @returns {Promise<IPCResponse>} Asset allocation data
     */
    async getAssetAllocation() {
        return this.standardIPCCall(
            () => window.electronAPI.getAssetAllocation(),
            'getAssetAllocation',
        );
    }

    /**
     * Get cross-exchange balance
     * @returns {Promise<IPCResponse>} Cross-exchange balance
     */
    async getCrossExchangeBalance() {
        return this.standardIPCCall(
            () => window.electronAPI.getCrossExchangeBalance(),
            'getCrossExchangeBalance',
        );
    }

    /**
     * Get order history
     * @param {string} [symbol] - Trading symbol (optional)
     * @returns {Promise<IPCResponse>} Order history
     */
    async getOrderHistory(symbol) {
        const limit = symbol ? 100;
        return this.standardIPCCall(
            () => window.electronAPI.getOrderHistory(limit),
            'getOrderHistory',
        );
    }

    /**
     * Get PnL report
     * @returns {Promise<IPCResponse>} PnL report
     */
    async getPnLReport() {
        return this.standardIPCCall(
            () => window.electronAPI.getPnLReport({timeframe: '24h'}),
            'getPnLReport',
        );
    }

    /**
     * Update grid bot configuration
     * @param {Object} config - Grid bot configuration
     * @returns {Promise<IPCResponse>} Update result
     */
    async updateGridBotConfig(config) {
        return this.tradingIPCCall(
            () => window.electronAPI.updateGridBotConfig(config),
            'updateGridBotConfig',
        );
    }

    /**
     * Get grid history (all grids)
     * @returns {Promise<IPCResponse>} Grid history
     */
    async getGridHistory() {
        return this.standardIPCCall(
            () => window.electronAPI.getGridHistory(),
            'getGridHistory',
        );
    }

    /**
     * Generate database health report
     * @returns {Promise<IPCResponse>} Database health report
     */
    async generateDatabaseHealthReport() {
        return this.standardIPCCall(
            () => window.electronAPI.generateDatabaseHealthReport(),
            'generateDatabaseHealthReport',
        );
    }

    /**
     * Get database statistics
     * @returns {Promise<IPCResponse>} Database statistics
     */
    async getDatabaseStatistics() {
        return this.standardIPCCall(
            () => window.electronAPI.getDatabaseStatistics(),
            'getDatabaseStatistics',
        );
    }

    /**
     * Store trading transaction
     * @param {Object} transaction - Transaction data
     * @returns {Promise<IPCResponse>} Storage result
     */
    async storeTradingTransaction(transaction) {
        return this.criticalIPCCall(
            () => window.electronAPI.storeTradingTransaction(transaction),
            'storeTradingTransaction',
        );
    }

    /**
     * Get trading transactions
     * @param {Object} [filters={}] - Filter criteria
     * @returns {Promise<IPCResponse>} Trading transactions
     */
    async getTradingTransactions(filters = {}) {
        return this.standardIPCCall(
            () => window.electronAPI.getTradingTransactions(filters),
            'getTradingTransactions',
        );
    }

    // Error monitoring and statistics methods
    async resetIPCErrorStatistics() {
        return this.quickIPCCall(
            () => {
                // this.errorHandler.resetStatistics();
                return Promise.resolve({message: 'IPC error statistics reset successfully'});
            },
            'resetIPCErrorStatistics',
        );
    }

    /**
     * Check if circuit breaker is active for a channel
     * @param {string} channel - Channel name
     * @returns {boolean} Whether circuit breaker is active
     */
    isCircuitBreakerActive(channel) {
        return this.errorHandler.shouldCircuitBreak(channel);
    }

    /**
     * Get configuration for debugging
     * @returns {Object} Current configuration
     */
    getDebugConfiguration() {
        return {
            ...this.getConfiguration(),
            errorHandler: {
                statistics(),
                circuitBreakers()
            }
        };
    }

    /**
     * Get active circuit breakers
     * @returns {string[]} List of channels with active circuit breakers
     */
    getActiveCircuitBreakers() {
        const stats = this.errorHandler.getErrorStatistics();
        const activeBreakers = [];

        for (const channel of Object.keys(stats.errorsByChannel)) {
            if (this.errorHandler.shouldCircuitBreak(channel)) {
                activeBreakers.push(channel);
            }
        }

        return activeBreakers;
    }

    /**
     * Force reset circuit breaker for a specific channel
     * @param {string} channel - Channel name
     * @returns {boolean} Whether reset was successful
     */
    resetCircuitBreaker(channel) {
        try {
            // Clear errors for this channel
            const keysToDelete = [];
            for (const key of this.errorHandler.errorCounts.keys()) {
                if (key.startsWith(channel + ':')) {
                    keysToDelete.push(key);
                }
            }

            keysToDelete.forEach((key) => {
                // this.errorHandler.errorCounts.delete(key);
                // this.errorHandler.lastErrorTime.delete(key);
            });

            logger.info(`Circuit breaker reset for channel: ${channel}`);
            return true;
        } catch (error) {
            logger.error(`Failed to reset circuit breaker for ${channel}:`, error);
            return false;
        }
    }

    /**
     * Test IPC connectivity with comprehensive diagnostics
     * @returns {Promise<Object>} Connectivity test results
     */
    async testIPCConnectivity() {
        const results = {
            electronAPI(),
            healthCheck,
            systemInfo,
            botStatus,
            errors,
            timestamp()
        };

        // Test basic health check
        try {
            const healthResult = await this.healthCheck();
            results.healthCheck = healthResult.success;
            if (!healthResult.success) {
                results.errors.push(`Health check failed: ${healthResult.error?.message || 'Unknown error'}`);
            }
        } catch (error) {
            results.errors.push(`Health check error: ${error.message}`);
        }

        // Test system info
        try {
            const systemResult = await this.getSystemInfo();
            results.systemInfo = systemResult.success;
            if (!systemResult.success) {
                results.errors.push(`System info failed: ${systemResult.error?.message || 'Unknown error'}`);
            }
        } catch (error) {
            results.errors.push(`System info error: ${error.message}`);
        }

        // Test bot status
        try {
            const statusResult = await this.getBotStatus();
            results.botStatus = statusResult.success;
            if (!statusResult.success) {
                results.errors.push(`Bot status failed: ${statusResult.error?.message || 'Unknown error'}`);
            }
        } catch (error) {
            results.errors.push(`Bot status error: ${error.message}`);
        }

        return results;
    }

    /**
     * Validate all IPC methods are properly connected
     * @returns {Promise<Object>} Validation results
     */
    async validateConnections() {
        const validation = {
            timestamp Date().toISOString(),
            electronAPIAvailable(),
            connectivity this.testConnectivity(),
            errorHandlerAvailable: !!this.errorHandler,
            configuration(),
            errorStatistics
        };

        try {
            const statsResult = await this.getIPCErrorStatistics();
            validation.errorStatistics = statsResult.success ? statsResult.data;
        } catch (error) {
            validation.errorStatistics = {error};
        }

        logger.info('IPC Connection Validation:', validation);
        return validation;
    }

    // Missing methods from TypeScript definitions
    async getTradingStats() {
        return this.standardIPCCall(
            () => window.electronAPI.getTradingStats(),
            'getTradingStats',
        );
    }

    async getIPCErrorStatistics() {
        if (this.errorHandler && this.errorHandler.getErrorStatistics) {
            return {
                success,
                data(),
                timestamp()
            };
        }
        return this.errorHandler.createErrorResponse(
            'SERVICE_UNAVAILABLE',
            'Error statistics not available',
            'getIPCErrorStatistics',
        );
    }

    async getCircuitBreakerStatus() {
        if (this.errorHandler && this.errorHandler.getErrorStatistics) {
            const stats = this.errorHandler.getErrorStatistics();
            return {
                success,
                data: {
                    circuitBreakerChannels || [],
                errorsByChannel || {},
            recentErrors || 0
        },
            timestamp()
        }
            ;
        }
        return this.errorHandler.createErrorResponse(
            'SERVICE_UNAVAILABLE',
            'Circuit breaker status not available',
            'getCircuitBreakerStatus',
        );
    }

    async updateErrorHandlerConfiguration(config) {
        if (this.errorHandler && this.errorHandler.updateConfiguration) {
            // this.errorHandler.updateConfiguration(config.timeouts, config.retryConfig);
            return {
                success,
                data: {message: 'Error handler configuration updated successfully'},
                timestamp()
            };
        }
        return this.errorHandler.createErrorResponse(
            'SERVICE_UNAVAILABLE',
            'Error handler configuration update not available',
            'updateErrorHandlerConfiguration',
        );
    }

    async updatePortfolioPosition(position) {
        return this.criticalIPCCall(
            () => window.electronAPI.updatePortfolioPosition(position),
            'updatePortfolioPosition',
        );
    }

    async getPortfolioPositions() {
        return this.standardIPCCall(
            () => window.electronAPI.getPortfolioPositions(),
            'getPortfolioPositions',
        );
    }

    async storePerformanceMetrics(metrics) {
        return this.criticalIPCCall(
            () => window.electronAPI.storePerformanceMetrics(metrics),
            'storePerformanceMetrics',
        );
    }

    async storeWhaleWallet(wallet) {
        return this.criticalIPCCall(
            () => window.electronAPI.storeWhaleWallet(wallet),
            'storeWhaleWallet',
        );
    }

    async storeWhaleTransaction(transaction) {
        return this.criticalIPCCall(
            () => window.electronAPI.storeWhaleTransaction(transaction),
            'storeWhaleTransaction',
        );
    }

    async getWhaleWallets(filters = {}) {
        return this.standardIPCCall(
            () => window.electronAPI.getWhaleWallets(filters),
            'getWhaleWallets',
        );
    }

    async storeTradingSignal(signal) {
        return this.criticalIPCCall(
            () => window.electronAPI.storeTradingSignal(signal),
            'storeTradingSignal',
        );
    }

    async getTradingSignals(filters = {}) {
        return this.standardIPCCall(
            () => window.electronAPI.getTradingSignals(filters),
            'getTradingSignals',
        );
    }

    async getWhaleTrackingStatus() {
        return this.standardIPCCall(
            () => window.electronAPI.getWhaleTrackingStatus(),
            'getWhaleTrackingStatus',
        );
    }

    async getPerformanceHistory(timeRange) {
        return this.standardIPCCall(
            () => window.electronAPI.getPerformanceHistory(timeRange),
            'getPerformanceHistory',
        );
    }

    async getArbitrageOpportunities() {
        return this.standardIPCCall(
            () => window.electronAPI.getArbitrageOpportunities(),
            'getArbitrageOpportunities',
        );
    }

    async getPortfolioRiskMetrics() {
        return this.standardIPCCall(
            () => window.electronAPI.getPortfolioRiskMetrics(),
            'getPortfolioRiskMetrics',
        );
    }

    async getPortfolioPerformance() {
        return this.standardIPCCall(
            () => window.electronAPI.getPortfolioPerformance(),
            'getPortfolioPerformance',
        );
    }

    async getArbitrageStats() {
        return this.standardIPCCall(
            () => window.electronAPI.getArbitrageStats(),
            'getArbitrageStats',
        );
    }

    async startArbitrageScanning() {
        return this.tradingIPCCall(
            () => window.electronAPI.startArbitrageScanning(),
            'startArbitrageScanning',
        );
    }

    async stopArbitrageScanning() {
        return this.tradingIPCCall(
            () => window.electronAPI.stopArbitrageScanning(),
            'stopArbitrageScanning',
        );
    }

    async executeArbitrage(opportunity) {
        return this.tradingIPCCall(
            () => window.electronAPI.executeArbitrage(opportunity),
            'executeArbitrage',
        );
    }

    async startPortfolioMonitoring() {
        return this.tradingIPCCall(
            () => window.electronAPI.startPortfolioMonitoring(),
            'startPortfolioMonitoring',
        );
    }

    async stopPortfolioMonitoring() {
        return this.tradingIPCCall(
            () => window.electronAPI.stopPortfolioMonitoring(),
            'stopPortfolioMonitoring',
        );
    }

    async getAppVersion() {
        return this.quickIPCCall(
            () => window.electronAPI.getAppVersion(),
            'getAppVersion',
        );
    }

    async clearLogs() {
        return this.standardIPCCall(
            () => window.electronAPI.clearLogs(),
            'clearLogs',
        );
    }

    async exportLogs() {
        return this.standardIPCCall(
            () => window.electronAPI.exportLogs(),
            'exportLogs',
        );
    }

    async setLogLevel(level) {
        return this.standardIPCCall(
            () => window.electronAPI.setLogLevel(level),
            'setLogLevel',
        );
    }

    async reportError(error, context) {
        return this.standardIPCCall(
            () => window.electronAPI.reportError(error, context),
            'reportError',
        );
    }

    async getSettings() {
        return this.standardIPCCall(
            () => window.electronAPI.getSettings(),
            'getSettings',
        );
    }

    async saveSettings(settings) {
        return this.standardIPCCall(
            () => window.electronAPI.saveSettings(settings),
            'saveSettings',
        );
    }

    // Event Subscription Methods
    onArbitrageOpportunity(callback) {
        if (window.electronAPI && window.electronAPI.onArbitrageOpportunity) {
            return window.electronAPI.onArbitrageOpportunity(callback);
        }
        return () => {
        };
    }

    onArbitrageExecuted(callback) {
        if (window.electronAPI && window.electronAPI.onArbitrageExecuted) {
            return window.electronAPI.onArbitrageExecuted(callback);
        }
        return () => {
        };
    }

    // Status reporting
    async getStatusReports(params) {
        const {limit = 50, filter = {}} = params || {};
        return this.standardIPCCall(
            () => window.electronAPI.getStatusReports({limit, filter}),
            'getStatusReports',
        );
    }

    async getRiskMetrics() {
        return this.standardIPCCall(
            () => window.electronAPI.getRiskMetrics(),
            'getRiskMetrics',
        );
    }

    // Error Monitoring and Statistics
    getErrorStatistics() {
        return this.errorHandler.getErrorStatistics();
    }

    resetErrorStatistics() {
        // this.errorHandler.resetStatistics();
    }

    isChannelCircuitBroken(channel) {
        return this.errorHandler.shouldCircuitBreak(channel);
    }

    updateErrorHandlerConfig(timeouts, retryConfig) {
        // this.errorHandler.updateConfiguration(timeouts, retryConfig);
    }

    async testIntegration() {
        const testResults = {
            timestamp Date().toISOString(),
            tests,
            summary: {
                total,
                passed,
                failed,
                errors
            }
        };

        const tests = [
            {name: 'Health Check', method: () => this.healthCheck()},
            {name: 'System Status', method: () => this.getBotStatus()},
            {name: 'Database Status', method: () => this.getDatabaseStatus()},
            {name: 'System Info', method: () => this.getSystemInfo()},
            {name: 'Portfolio Summary', method: () => this.getPortfolioSummary()},
            {name: 'Performance Metrics', method: () => this.getPerformanceMetrics()},
            {name: 'System Health', method: () => this.getSystemHealth()},
            {name: 'Real-time Status', method: () => this.getRealTimeStatus()}];


        for (const test of tests) {
            testResults.summary.total++;

            try {
                const startTime = Date.now();
                const result = await test.method();
                const duration = Date.now() - startTime;

                const testResult = {
                    name,
                    status ? 'PASSED' : 'FAILED',
                    duration,
                    error ? null?.message || 'Unknown error',
                    errorCode ? null?.code
                };

                testResults.tests.push(testResult);

                if (result.success) {
                    testResults.summary.passed++;
                } else {
                    testResults.summary.failed++;
                    testResults.summary.errors.push({
                        test,
                        error,
                        code
                    });
                }

            } catch (error) {
                testResults.summary.failed++;
                testResults.tests.push({
                    name,
                    status: 'ERROR',
                    duration,
                    error,
                    errorCode: 'TEST_EXCEPTION'
                });
                testResults.summary.errors.push({
                    test,
                    error,
                    code: 'TEST_EXCEPTION'
                });
            }
        }

        logger.info('IPC Integration Test Results:', {
            total,
            passed,
            failed,
            successRate: `${Math.round(testResults.summary.passed / testResults.summary.total * 100)}%`
        });

        return testResults;
    }
}

const ipcService = new IPCService();
module.exports = ipcService;