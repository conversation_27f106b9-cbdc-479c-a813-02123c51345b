 
'use strict';

Object.defineProperty(exports, '__esModule', {
    value: true
});
exports.default = void 0;
const _react = _interopRequireWildcard(require('react'));
const _propTypes = _interopRequireDefault(require('prop-types'));
const _logger = _interopRequireDefault(require('../utils/logger'));
const _material = require('@mui/material');
const _iconsMaterial = require('@mui/icons-material');
const _framerMotion = require('framer-motion');
const _recharts = require('recharts');
const _HolographicCard = _interopRequireDefault(require('./HolographicCard'));
const _SystemStatusPanel = _interopRequireDefault(require('./SystemStatusPanel'));
const _RunningOperationsMonitor = _interopRequireDefault(require('./RunningOperationsMonitor'));
const _TradingStatusIndicator = _interopRequireDefault(require('./TradingStatusIndicator'));
const _LiveSystemMonitor = _interopRequireDefault(require('./LiveSystemMonitor'));
const _ipcService = _interopRequireDefault(require('../services/ipcService'));

function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}

function _interopRequireWildcard(e, t) {
    if ('function' == typeof WeakMap) var r = new WeakMap(),
        n = new WeakMap();
    return (_interopRequireWildcard = function (e, t) {
        if (!t && e && e.__esModule) return e;
        let o,
            i,
            f = {
                __proto__: null,
                default: e
            };
        if (null === e || 'object' != typeof e && 'function' != typeof e) return f;
        if (o = t ? n : r) {
            if (o.has(e)) return o.get(e);
            o.set(e, f);
        }
        for (const t in e) 'default' !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]);
        return f;
    })(e, t);
}

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) : e[r] = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i : i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String : Number)(t);
}

/**
 * Autonomous trading dashboard component.
 * @param {Function} showNotification - Function to show notification
 * @returns {ReactElement} - The autonomous trading dashboard component
 */ // @ts-nocheck
// Import logger for consistent logging
// Import IPC service for enhanced communication
const AutonomousDashboard = ({
                                 showNotification
                             }) => {
    let _status$performance, _status$performance$t;
    const [status, setStatus] = (0, _react.useState)({
        isRunning: false,
        activeBots: 0,
        performance: {
            totalProfit: 0,
            totalTrades: 0,
            winRate: 0
        },
        availableCapital: 0,
        bots: []
    });
    const [logs, setLogs] = (0, _react.useState)([]);
    const [autoRefresh, setAutoRefresh] = (0, _react.useState)(true);
    const [loading, setLoading] = (0, _react.useState)(false);
    const [startingSystem, setStartingSystem] = (0, _react.useState)(false);
    const [stoppingSystem, setStoppingSystem] = (0, _react.useState)(false);
    const [systemError, setSystemError] = (0, _react.useState)(null);
    const [connectionStatus, setConnectionStatus] = (0, _react.useState)('checking');
    const [lastUpdateTime, setLastUpdateTime] = (0, _react.useState)(null);

    // Dashboard specific states
    const [tradingStats, setTradingStats] = (0, _react.useState)({});
    const [whaleSignals, setWhaleSignals] = (0, _react.useState)([]);
    const [memeCoinOpportunities, setMemeCoinOpportunities] = (0, _react.useState)([]);
    const [performanceMetrics, setPerformanceMetrics] = (0, _react.useState)({});
    const fetchData = (0, _react.useCallback)(async () => {
        setLoading(true);
        try {
            const [statsRes, whaleRes, memeRes, perfRes] = await Promise.all([_ipcService.default.getTradingStats(), _ipcService.default.getWhaleSignals(), _ipcService.default.getMemeCoinOpportunities(), _ipcService.default.getPerformanceMetrics()]);
            if (statsRes !== null && statsRes !== void 0 && statsRes.success) setTradingStats(statsRes.data || {});
            if (whaleRes !== null && whaleRes !== void 0 && whaleRes.success) setWhaleSignals(whaleRes.data || []);
            if (memeRes !== null && memeRes !== void 0 && memeRes.success) setMemeCoinOpportunities(memeRes.data || []);
            if (perfRes !== null && perfRes !== void 0 && perfRes.success) setPerformanceMetrics(perfRes.data || {});
        } catch (error) {
            _logger.default.error('Error fetching data:', error);
            setSystemError('Failed to fetch dashboard data');
        } finally {
            setLoading(false);
        }
    }, []);
    const fetchAutonomousStatus = (0, _react.useCallback)(async () => {
        try {
            let _window$electronAPI, _window$electronAPI2;
            // Use real-time status for more accurate information
            const [statusResult, botsResult] = await Promise.all([_ipcService.default.quickIPCCall(((_window$electronAPI = window.electronAPI) === null || _window$electronAPI === void 0 ? void 0 : _window$electronAPI.getRealTimeStatus) || (() => Promise.resolve({
                success: false,
                error: 'Not available'
            })), 'getRealTimeStatus'), _ipcService.default.quickIPCCall(((_window$electronAPI2 = window.electronAPI) === null || _window$electronAPI2 === void 0 ? void 0 : _window$electronAPI2.getActiveBots) || (() => Promise.resolve({
                success: false,
                error: 'Not available'
            })), 'getActiveBots')]);
            if (statusResult.success) {
                const statusData = statusResult.data || {};
                const activeBots = botsResult.success ? botsResult.data || [] : [];
                setStatus({
                    isRunning: statusData.isRunning || false,
                    activeBots: activeBots.length,
                    performance: statusData.performance || {
                        totalProfit: 0,
                        totalTrades: 0,
                        winRate: 0
                    },
                    availableCapital: statusData.availableCapital || 0,
                    bots: activeBots
                });
                setSystemError(null); // Clear any previous errors
            } else {
                _logger.default.error('Failed to fetch real-time status:', statusResult.error);
                setSystemError(statusResult.error || 'Failed to fetch system status');
            }
        } catch (error) {
            _logger.default.error('Error fetching autonomous status:', error);
            setSystemError(error.message || 'Failed to fetch system status');
        }
    }, []);

    // Fetch logs for autonomous trader
    const fetchLogs = (0, _react.useCallback)(async () => {
        try {
            const result = await _ipcService.default.getLogs('info', 50);
            if (result.success) {
                setLogs(result.data || []);
            } else {
                _logger.default.error('Failed to fetch logs:', result.error);
            }
        } catch (error) {
            _logger.default.error('Error fetching logs:', error);
        }
    }, []);

    // Check IPC connection status
    const checkConnectionStatus = (0, _react.useCallback)(async () => {
        try {
            let _window$electronAPI3;
            const healthResult = await _ipcService.default.quickIPCCall(((_window$electronAPI3 = window.electronAPI) === null || _window$electronAPI3 === void 0 ? void 0 : _window$electronAPI3.healthCheck) || (() => Promise.resolve({
                success: false,
                error: 'Not available'
            })), 'healthCheck');
            if (healthResult.success) {
                setConnectionStatus('connected');
                setLastUpdateTime(new Date());
            } else {
                setConnectionStatus('disconnected');
            }
        } catch (error) {
            _logger.default.error('Connection check failed:', error);
            setConnectionStatus('error');
        }
    }, []);

    // Start autonomous trading with enhanced IPC communication
    const handleStartAutonomous = async () => {
        setStartingSystem(true);
        setSystemError(null);
        try {
            let _window$electronAPI4, _window$electronAPI5;
            // Show immediate feedback to user
            showNotification('Initializing trading system...', 'info');

            // First initialize the trading system if needed
            const initResult = await _ipcService.default.criticalIPCCall(((_window$electronAPI4 = window.electronAPI) === null || _window$electronAPI4 === void 0 ? void 0 : _window$electronAPI4.initializeTrading) || (() => Promise.resolve({
                success: false,
                error: 'IPC not available'
            })), 'initializeTrading');
            if (!initResult.success) {
                throw new Error(initResult.error || 'Failed to initialize trading system');
            }

            // Show progress update
            showNotification('Starting trading engines...', 'info');

            // Then start the bot using enhanced IPC call
            const result = await _ipcService.default.criticalIPCCall(((_window$electronAPI5 = window.electronAPI) === null || _window$electronAPI5 === void 0 ? void 0 : _window$electronAPI5.startBot) || (() => Promise.resolve({
                success: false,
                error: 'IPC not available'
            })), 'startBot');
            if (result.success) {
                showNotification('Trading system started successfully! 🚀', 'success');

                // Refresh status and logs after successful start
                await Promise.all([fetchAutonomousStatus(), fetchLogs(), fetchData()]);
            } else {
                throw new Error(result.error || 'Failed to start trading system');
            }
        } catch (error) {
            _logger.default.error('Error starting trading system:', error);
            const errorMessage = error.message || 'Error starting trading system';
            setSystemError(errorMessage);
            showNotification(`Failed to start: ${errorMessage}`, 'error');
        } finally {
            setStartingSystem(false);
        }
    };
    const handleStopAutonomous = async () => {
        setStoppingSystem(true);
        setSystemError(null);
        try {
            let _window$electronAPI6;
            // Show immediate feedback to user
            showNotification('Stopping trading system...', 'info');
            const result = await _ipcService.default.criticalIPCCall(((_window$electronAPI6 = window.electronAPI) === null || _window$electronAPI6 === void 0 ? void 0 : _window$electronAPI6.stopBot) || (() => Promise.resolve({
                success: false,
                error: 'IPC not available'
            })), 'stopBot');
            if (result.success) {
                showNotification('Trading system stopped successfully', 'success');

                // Refresh status and logs after successful stop
                await Promise.all([fetchAutonomousStatus(), fetchLogs(), fetchData()]);
            } else {
                throw new Error(result.error || 'Failed to stop trading system');
            }
        } catch (error) {
            _logger.default.error('Error stopping trading system:', error);
            const errorMessage = error.message || 'Error stopping trading system';
            setSystemError(errorMessage);
            showNotification(`Failed to stop: ${errorMessage}`, 'error');
        } finally {
            setStoppingSystem(false);
        }
    };
    const handleStopBot = async botId => {
        try {
            let _window$electronAPI7;
            const result = await _ipcService.default.tradingIPCCall(((_window$electronAPI7 = window.electronAPI) === null || _window$electronAPI7 === void 0 ? void 0 : _window$electronAPI7.stopGrid) || (() => Promise.resolve({
                success: false,
                error: 'IPC not available'
            })), 'stopGrid', botId);
            if (result.success) {
                showNotification('Bot stopped successfully', 'success');
                await fetchAutonomousStatus();
            } else {
                throw new Error(result.error || 'Failed to stop bot');
            }
        } catch (error) {
            _logger.default.error('Error stopping bot:', error);
            showNotification(error.message || 'Error stopping bot', 'error');
        }
    };

    // Auto-refresh effect
    (0, _react.useEffect)(() => {
        // Initial load
        const initializeData = async () => {
            await checkConnectionStatus();
            await Promise.all([fetchData(), fetchAutonomousStatus(), fetchLogs()]);
        };
        initializeData();
        if (autoRefresh) {
            const interval = setInterval(async () => {
                await checkConnectionStatus();
                await Promise.all([fetchData(), fetchAutonomousStatus(), fetchLogs()]);
            }, 5000); // Refresh every 5 seconds

            return () => clearInterval(interval);
        }
    }, [autoRefresh, fetchData, fetchAutonomousStatus, fetchLogs, checkConnectionStatus]);

    // Get P&L data from performance metrics or use empty array
    const getPnLData = () => {
        if (performanceMetrics !== null && performanceMetrics !== void 0 && performanceMetrics.pnlHistory && Array.isArray(performanceMetrics.pnlHistory)) {
            return performanceMetrics.pnlHistory;
        }
        return [];
    };
    return /*#__PURE__*/_react.default.createElement(_material.Grid, {
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'quantum',
        elevation: 'medium'
    }, /*#__PURE__*/_react.default.createElement(_material.CardContent, null, /*#__PURE__*/_react.default.createElement(_TradingStatusIndicator.default, {
        showDetails: true,
        refreshInterval: 3000,
        onStatusChange: newStatus => {
            // Update local status when real-time status changes
            setStatus(prevStatus => _objectSpread(_objectSpread({}, prevStatus), {}, {
                isRunning: newStatus.system.isRunning,
                activeBots: newStatus.trading.activeBots.length,
                performance: newStatus.trading.performance
            }));
        }
    })))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 4
    }, /*#__PURE__*/_react.default.createElement(_SystemStatusPanel.default, {
        refreshInterval: 3000
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 8
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'quantum',
        elevation: 'high'
    }, /*#__PURE__*/_react.default.createElement(_material.CardContent, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h5',
        component: 'div'
    }, 'Trading System'), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            mb: 1.5,
            flexWrap: 'wrap'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        color: 'text.secondary'
    }, 'System Status:', /*#__PURE__*/_react.default.createElement(_material.Chip, {
        icon: status.isRunning ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.CheckCircle, null) : /*#__PURE__*/_react.default.createElement(_iconsMaterial.Error, null),
        label: startingSystem ? 'Starting...' : stoppingSystem ? 'Stopping...' : status.isRunning ? 'Running' : 'Stopped',
        color: startingSystem || stoppingSystem ? 'warning' : status.isRunning ? 'success' : 'error',
        size: 'small',
        sx: {
            ml: 1
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        color: 'text.secondary',
        variant: 'body2'
    }, 'Connection:', /*#__PURE__*/_react.default.createElement(_material.Chip, {
        icon: connectionStatus === 'connected' ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.CheckCircle, null) : connectionStatus === 'checking' ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.Circle, null) : /*#__PURE__*/_react.default.createElement(_iconsMaterial.Error, null),
        label: connectionStatus === 'connected' ? 'Connected' : connectionStatus === 'checking' ? 'Checking...' : connectionStatus === 'disconnected' ? 'Disconnected' : 'Error',
        color: connectionStatus === 'connected' ? 'success' : connectionStatus === 'checking' ? 'default' : 'error',
        size: 'small',
        sx: {
            ml: 1
        }
    })), status.activeBots > 0 && /*#__PURE__*/_react.default.createElement(_material.Typography, {
        color: 'text.secondary',
        variant: 'body2'
    }, 'Active Operations:', /*#__PURE__*/_react.default.createElement(_material.Chip, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.TrendingUp, null),
        label: `${status.activeBots} bots running`,
        color: 'primary',
        size: 'small',
        sx: {
            ml: 1
        }
    })), ((_status$performance = status.performance) === null || _status$performance === void 0 ? void 0 : _status$performance.totalTrades) > 0 && /*#__PURE__*/_react.default.createElement(_material.Typography, {
        color: 'text.secondary',
        variant: 'body2'
    }, 'Performance:', /*#__PURE__*/_react.default.createElement(_material.Chip, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.AttachMoney, null),
        label: `${status.performance.totalTrades} trades, ${((_status$performance$t = status.performance.totalProfit) === null || _status$performance$t === void 0 ? void 0 : _status$performance$t.toFixed(2)) || '0.00'} P&L`,
        color: status.performance.totalProfit > 0 ? 'success' : status.performance.totalProfit < 0 ? 'error' : 'default',
        size: 'small',
        sx: {
            ml: 1
        }
    }))), lastUpdateTime && /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'caption',
        color: 'text.secondary',
        sx: {
            display: 'block',
            mb: 1
        }
    }, 'Last updated: ', lastUpdateTime.toLocaleTimeString()), systemError && /*#__PURE__*/_react.default.createElement(_material.Alert, {
        severity: 'error',
        sx: {
            mt: 1,
            mb: 1
        },
        action: /*#__PURE__*/_react.default.createElement(_material.Button, {
            color: 'inherit',
            size: 'small',
            onClick: () => {
                setSystemError(null);
                checkConnectionStatus();
            }
        }, 'Retry')
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2'
    }, /*#__PURE__*/_react.default.createElement('strong', null, 'System Error:'), ' ', systemError)), (startingSystem || stoppingSystem) && /*#__PURE__*/_react.default.createElement(_material.Alert, {
        severity: 'info',
        sx: {
            mt: 1,
            mb: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            gap: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        animate: {
            rotate: 360
        },
        transition: {
            duration: 1,
            repeat: Infinity,
            ease: 'linear'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Circle, {
        sx: {
            fontSize: '1rem'
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2'
    }, startingSystem ? 'Initializing trading system components...' : 'Gracefully shutting down trading operations...')))), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            gap: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.FormControlLabel, {
        control: /*#__PURE__*/_react.default.createElement(_material.Switch, {
            checked: autoRefresh,
            onChange: e => setAutoRefresh(e.target.checked),
            color: 'primary'
        }),
        label: 'Auto-refresh'
    }), /*#__PURE__*/_react.default.createElement(_material.Button, {
        variant: 'outlined',
        size: 'small',
        onClick: async () => {
            await checkConnectionStatus();
            await Promise.all([fetchData(), fetchAutonomousStatus(), fetchLogs()]);
        },
        disabled: loading || startingSystem || stoppingSystem,
        sx: {
            minWidth: 80
        }
    }, 'Refresh'), /*#__PURE__*/_react.default.createElement(_material.Button, {
        variant: 'contained',
        size: 'large',
        color: startingSystem || stoppingSystem ? 'warning' : status.isRunning ? 'error' : 'success',
        startIcon: startingSystem || stoppingSystem ? /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
            animate: {
                rotate: 360
            },
            transition: {
                duration: 1,
                repeat: Infinity,
                ease: 'linear'
            }
        }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Circle, null)) : status.isRunning ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.Stop, null) : /*#__PURE__*/_react.default.createElement(_iconsMaterial.PlayArrow, null),
        onClick: status.isRunning ? handleStopAutonomous : handleStartAutonomous,
        disabled: loading || startingSystem || stoppingSystem,
        sx: {
            minWidth: 120,
            height: 48,
            fontSize: '1.1rem',
            fontWeight: 'bold',
            boxShadow: startingSystem || stoppingSystem ? '0 0 20px rgba(255, 193, 7, 0.5)' : status.isRunning ? '0 0 20px rgba(244, 67, 54, 0.3)' : '0 0 20px rgba(76, 175, 80, 0.3)',
            '&:hover': {
                boxShadow: startingSystem || stoppingSystem ? '0 0 25px rgba(255, 193, 7, 0.7)' : status.isRunning ? '0 0 25px rgba(244, 67, 54, 0.5)' : '0 0 25px rgba(76, 175, 80, 0.5)'
            }
        }
    }, startingSystem ? 'STARTING...' : stoppingSystem ? 'STOPPING...' : status.isRunning ? 'STOP SYSTEM' : 'START SYSTEM')))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 7
    }, /*#__PURE__*/_react.default.createElement(ActiveBotsTable, {
        bots: status.bots,
        onStopBot: handleStopBot
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 5
    }, /*#__PURE__*/_react.default.createElement(_RunningOperationsMonitor.default, {
        refreshInterval: 3000
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(_LiveSystemMonitor.default, {
        refreshInterval: 2000
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(ActivityLog, {
        logs: logs
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h5',
        sx: {
            color: '#a259ff',
            mb: 3,
            fontWeight: 600
        }
    }, 'Performance Overview'), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3,
        sx: {
            mb: 4
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'quantum',
        elevation: 'medium',
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#00eaff',
            mb: 2
        }
    }, 'Profit & Loss Timeline'), /*#__PURE__*/_react.default.createElement(_recharts.ResponsiveContainer, {
        width: '100%',
        height: 300
    }, /*#__PURE__*/_react.default.createElement(_recharts.AreaChart, {
        data: getPnLData()
    }, /*#__PURE__*/_react.default.createElement('defs', null, /*#__PURE__*/_react.default.createElement('linearGradient', {
        id: 'colorPnL',
        x1: '0',
        y1: '0',
        x2: '0',
        y2: '1'
    }, /*#__PURE__*/_react.default.createElement('stop', {
        offset: '5%',
        stopColor: '#00eaff',
        stopOpacity: 0.8
    }), /*#__PURE__*/_react.default.createElement('stop', {
        offset: '95%',
        stopColor: '#00eaff',
        stopOpacity: 0.1
    }))), /*#__PURE__*/_react.default.createElement(_recharts.CartesianGrid, {
        strokeDasharray: '3 3',
        stroke: 'rgba(255,255,255,0.1)'
    }), /*#__PURE__*/_react.default.createElement(_recharts.XAxis, {
        dataKey: 'time',
        stroke: '#888'
    }), /*#__PURE__*/_react.default.createElement(_recharts.YAxis, {
        stroke: '#888'
    }), /*#__PURE__*/_react.default.createElement(_recharts.Tooltip, {
        contentStyle: {
            backgroundColor: 'rgba(24,26,32,0.95)',
            border: '1px solid #00eaff'
        },
        labelStyle: {
            color: '#00eaff'
        }
    }), /*#__PURE__*/_react.default.createElement(_recharts.Area, {
        type: 'monotone',
        dataKey: 'pnl',
        stroke: '#00eaff',
        fillOpacity: 1,
        fill: 'url(#colorPnL)'
    }))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'premium',
        elevation: 'medium',
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#a259ff',
            mb: 2
        }
    }, 'Trade Success Distribution'), /*#__PURE__*/_react.default.createElement(_recharts.ResponsiveContainer, {
        width: '100%',
        height: 300
    }, !(tradingStats !== null && tradingStats !== void 0 && tradingStats.totalTrades) ? /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            height: 300,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        sx: {
            color: '#888'
        }
    }, 'No trade data available')) : /*#__PURE__*/_react.default.createElement(_recharts.PieChart, null, /*#__PURE__*/_react.default.createElement(_recharts.Pie, {
        data: [{
            name: 'Winning Trades',
            value: (tradingStats === null || tradingStats === void 0 ? void 0 : tradingStats.winningTrades) || 0,
            color: '#4caf50'
        }, {
            name: 'Losing Trades',
            value: ((tradingStats === null || tradingStats === void 0 ? void 0 : tradingStats.totalTrades) || 0) - ((tradingStats === null || tradingStats === void 0 ? void 0 : tradingStats.winningTrades) || 0),
            color: '#f44336'
        }],
        cx: '50%',
        cy: '50%',
        labelLine: false,
        label: ({
                    percent
                }) => `${(percent * 100).toFixed(0)}%`,
        outerRadius: 100,
        fill: '#8884d8',
        dataKey: 'value'
    }, /*#__PURE__*/_react.default.createElement(_recharts.Cell, {
        fill: '#4caf50'
    }), /*#__PURE__*/_react.default.createElement(_recharts.Cell, {
        fill: '#f44336'
    })), /*#__PURE__*/_react.default.createElement(_recharts.Tooltip, null), /*#__PURE__*/_react.default.createElement(_recharts.Legend, null))))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h5',
        sx: {
            color: '#00eaff',
            mb: 3,
            fontWeight: 600
        }
    }, 'Market Insights'), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'quantum',
        elevation: 'medium',
        sx: {
            p: 3,
            height: '300px'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#00eaff',
            mb: 2,
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Anchor, {
        sx: {
            mr: 1
        }
    }), 'Whale Signals'), /*#__PURE__*/_react.default.createElement(_material.List, {
        sx: {
            maxHeight: '200px',
            overflow: 'auto'
        }
    }, (whaleSignals === null || whaleSignals === void 0 ? void 0 : whaleSignals.length) > 0 ? whaleSignals.map((signal, index) => /*#__PURE__*/_react.default.createElement(_material.ListItem, {
        key: `whale-${signal.symbol}-${index}`,
        sx: {
            px: 0
        }
    }, /*#__PURE__*/_react.default.createElement(_material.ListItemText, {
        primary: /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#fff',
                fontWeight: 600
            }
        }, signal.symbol, ' - ', signal.type),
        secondary: /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#888',
                fontSize: '0.8rem'
            }
        }, signal.amount, ' | ', signal.timestamp)
    }))) : /*#__PURE__*/_react.default.createElement(_material.Typography, {
        sx: {
            color: '#888',
            textAlign: 'center',
            mt: 4
        }
    }, 'No whale signals detected')))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'premium',
        elevation: 'medium',
        sx: {
            p: 3,
            height: '300px'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#a259ff',
            mb: 2
        }
    }, 'Meme Coin Opportunities'), /*#__PURE__*/_react.default.createElement(_material.List, {
        sx: {
            maxHeight: '200px',
            overflow: 'auto'
        }
    }, (memeCoinOpportunities === null || memeCoinOpportunities === void 0 ? void 0 : memeCoinOpportunities.length) > 0 ? memeCoinOpportunities.map((opportunity, index) => /*#__PURE__*/_react.default.createElement(_material.ListItem, {
        key: `meme-${opportunity.symbol}-${index}`,
        sx: {
            px: 0
        }
    }, /*#__PURE__*/_react.default.createElement(_material.ListItemText, {
        primary: /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#fff',
                fontWeight: 600
            }
        }, opportunity.symbol),
        secondary: /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#888',
                fontSize: '0.8rem'
            }
        }, 'Score: ', opportunity.score, ' | ', opportunity.reason)
    }))) : /*#__PURE__*/_react.default.createElement(_material.Typography, {
        sx: {
            color: '#888',
            textAlign: 'center',
            mt: 4
        }
    }, 'Scanning for opportunities...'))))))));
};
AutonomousDashboard.propTypes = {
    showNotification: _propTypes.default.func
};
AutonomousDashboard.defaultProps = {
    showNotification: () => {
    }
};
const ActiveBotsTable = ({
                             bots,
                             onStopBot
                         }) => /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
    variant: 'default',
    elevation: 'medium'
}, /*#__PURE__*/_react.default.createElement(_material.CardContent, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
    variant: 'h6',
    gutterBottom: true
}, 'Active Grid Bots'), bots.length === 0 ? /*#__PURE__*/_react.default.createElement(_material.Alert, {
    severity: 'info'
}, 'No active bots. Start the trading system to deploy bots automatically.') : /*#__PURE__*/_react.default.createElement(_material.TableContainer, null, /*#__PURE__*/_react.default.createElement(_material.Table, {
    size: 'small'
}, /*#__PURE__*/_react.default.createElement(_material.TableHead, null, /*#__PURE__*/_react.default.createElement(_material.TableRow, null, /*#__PURE__*/_react.default.createElement(_material.TableCell, null, 'Symbol'), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, 'Exchange'), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, 'Grid Range'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
    align: 'right'
}, 'Investment'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
    align: 'right'
}, 'Profit'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
    align: 'right'
}, 'Trades'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
    align: 'center'
}, 'Action'))), /*#__PURE__*/_react.default.createElement(_material.TableBody, null, /*#__PURE__*/_react.default.createElement(_framerMotion.AnimatePresence, null, bots.map(bot => /*#__PURE__*/_react.default.createElement(_framerMotion.motion.tr, {
    key: bot.id,
    initial: {
        opacity: 0
    },
    animate: {
        opacity: 1
    },
    exit: {
        opacity: 0
    }
}, /*#__PURE__*/_react.default.createElement(_material.TableCell, null, bot.symbol), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, bot.exchange), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
    variant: 'caption'
}, '$', bot.parameters.lowerPrice.toFixed(4), ' - $', bot.parameters.upperPrice.toFixed(4))), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
    align: 'right'
}, '$', bot.parameters.investment.toFixed(2)), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
    align: 'right'
}, /*#__PURE__*/_react.default.createElement(_material.Typography, {
    color: bot.profit >= 0 ? 'success.main' : 'error.main',
    fontWeight: 'bold'
}, '$', bot.profit.toFixed(2))), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
    align: 'right'
}, bot.trades), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
    align: 'center'
}, /*#__PURE__*/_react.default.createElement(_material.IconButton, {
    size: 'small',
    color: 'error',
    onClick: () => onStopBot(bot.id)
}, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Delete, {
    fontSize: 'small'
})))))))))));
ActiveBotsTable.propTypes = {
    bots: _propTypes.default.array.isRequired,
    onStopBot: _propTypes.default.func.isRequired
};
const getLogIcon = type => {
    switch (type) {
        case 'success':
            return /*#__PURE__*/_react.default.createElement(_iconsMaterial.CheckCircle, {
                color: 'success'
            });
        case 'error':
            return /*#__PURE__*/_react.default.createElement(_iconsMaterial.Error, {
                color: 'error'
            });
        case 'warning':
            return /*#__PURE__*/_react.default.createElement(_iconsMaterial.Warning, {
                color: 'warning'
            });
        case 'trade':
            return /*#__PURE__*/_react.default.createElement(_iconsMaterial.AttachMoney, {
                color: 'primary'
            });
        default:
            return /*#__PURE__*/_react.default.createElement(_iconsMaterial.Circle, {
                sx: {
                    fontSize: '0.7rem',
                    mr: 0.5
                },
                color: 'inherit'
            });
    }
};
const getLogColor = type => {
    switch (type) {
        case 'success':
            return 'success.main';
        case 'error':
            return 'error.main';
        case 'warning':
            return 'warning.main';
        case 'trade':
            return 'primary.main';
        default:
            return 'text.secondary';
    }
};
const ActivityLog = ({
                         logs
                     }) => /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
    variant: 'default',
    elevation: 'medium'
}, /*#__PURE__*/_react.default.createElement(_material.CardContent, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
    variant: 'h6',
    gutterBottom: true
}, 'Activity Log'), /*#__PURE__*/_react.default.createElement(_material.List, {
    sx: {
        maxHeight: 400,
        overflow: 'auto'
    }
}, /*#__PURE__*/_react.default.createElement(_framerMotion.AnimatePresence, null, logs.map((log, index) => /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
    key: `${log.timestamp}-${index}`,
    initial: {
        opacity: 0,
        y: -10
    },
    animate: {
        opacity: 1,
        y: 0
    },
    exit: {
        opacity: 0,
        x: -20
    }
}, /*#__PURE__*/_react.default.createElement(_material.ListItem, {
    alignItems: 'flex-start',
    sx: {
        p: 0
    }
}, /*#__PURE__*/_react.default.createElement(_material.ListItemIcon, {
    sx: {
        minWidth: 32,
        mt: 0.5
    }
}, getLogIcon(log.type)), /*#__PURE__*/_react.default.createElement(_material.ListItemText, {
    primary: /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: getLogColor(log.type),
            wordBreak: 'break-word'
        }
    }, log.message),
    secondary: /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'caption',
        color: 'text.secondary'
    }, new Date(log.timestamp).toLocaleString())
})), /*#__PURE__*/_react.default.createElement(_material.Divider, {
    variant: 'inset',
    component: 'li'
})))))));
ActivityLog.propTypes = {
    logs: _propTypes.default.array.isRequired
};
const _default = exports.default = AutonomousDashboard;