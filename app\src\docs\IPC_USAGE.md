# IPC Communication in Renderer Process

This document provides a comprehensive guide for implementing IPC calls in the renderer process for the Electron Trader
application.

## Overview

The renderer process communicates with the main process through a secure IPC (Inter-Process Communication) layer. This
is implemented using Electron's `contextBridge` and `ipcRenderer` APIs.

## Architecture

### 1. Preload Script (`app/preload.js`)

- Exposes a secure API to the renderer process
- Uses `contextBridge` to maintain security
- Provides type-safe methods for IPC communication

### 2. IPC Service (`app/src/services/ipcService.js`)

- Central service for all IPC communication
- Provides fallback for non-Electron environments
- Handles error handling and response formatting

### 3. React Hooks (`app/src/hooks/useIPC.js`)

- Custom hooks for easy IPC integration
- Loading states and error handling
- Data fetching and mutation patterns

### 4. Type Definitions (`app/src/types/electron.d.ts`)

- TypeScript definitions for the electronAPI
- Ensures type safety across the application

## Usage Examples

### Basic IPC Call

```javascript
import ipcService from '../services/ipcService';

// Get bot status
const result = await ipcService.getBotStatus();
if (result.success) {
  console.log('Bot status:', result.data);
} else {
  console.error('Error:', result.error);
}
```

### Using React Hooks

```javascript
import { useIPC, useBotStatus } from '../hooks/useIPC';

function MyComponent() {
  const { callIPC, loading, error } = useIPC();
  const { data: botStatus, loading: botLoading } = useBotStatus();

  const handleStartBot = async () => {
    try {
      await callIPC('startBot');
    } catch (err) {
      console.error('Failed to start bot:', err);
    }
  };

  return (
    <div>
      {botLoading ? 'Loading...' : JSON.stringify(botStatus)}
      <button onClick={handleStartBot} disabled={loading}>
        Start Bot
      </button>
    </div>
  );
}
```

### Available IPC Methods

#### Trading Engine

- `initializeTrading(config)` - Initialize trading system
- `startBot()` - Start the trading bot
- `stopBot()` - Stop the trading bot
- `getBotStatus()` - Get current bot status
- `emergencyShutdown()` - Emergency stop all trading

#### Market Data

- `getMarketData(symbol)` - Get market data for a symbol
- `getMarketOverview()` - Get overall market overview
- `getPriceHistory(symbol, timeframe)` - Get historical price data
- `fetchCryptoData(coin)` - Fetch data from CoinGecko

#### Portfolio Management

- `getPortfolioSummary()` - Get portfolio summary
- `getAssetAllocation()` - Get current asset allocation
- `rebalancePortfolio(target)` - Rebalance portfolio
- `getWalletBalance()` - Get wallet balance

#### Coin Management

- `getCoins()` - Get all coins
- `saveCoin(coin)` - Save a new coin
- `updateCoin(coinId, updates)` - Update existing coin
- `deleteCoin(coinId)` - Delete a coin

#### Grid Trading

- `startGrid(config)` - Start grid trading
- `stopGrid(gridId)` - Stop specific grid
- `getGridPositions()` - Get active grid positions
- `updateGridConfig(gridId, config)` - Update grid configuration

#### DCA Trading

- `startDCA(config)` - Start DCA strategy
- `stopDCA(dcaId)` - Stop DCA strategy
- `getDCAPositions()` - Get active DCA positions

#### Order Management

- `getOpenOrders()` - Get all open orders
- `placeLimitOrder(params)` - Place limit order
- `placeMarketOrder(params)` - Place market order
- `cancelOrder(orderId)` - Cancel specific order
- `cancelAllOrders()` - Cancel all orders

#### Whale Tracking

- `getWhaleSignals()` - Get whale trading signals
- `addWhaleWallet(address)` - Add whale wallet to track
- `removeWhaleWallet(address)` - Remove whale wallet
- `getTrackedWhales()` - Get list of tracked whales

#### Meme Coin Scanner

- `startMemeCoinScanner()` - Start meme coin scanning
- `getMemeCoinOpportunities()` - Get meme coin opportunities
- `updateScannerConfig(config)` - Update scanner configuration

#### Arbitrage Engine

- `startArbitrageEngine()` - Start arbitrage engine
- `getArbitrageOpportunities()` - Get arbitrage opportunities
- `executeArbitrage(opportunity)` - Execute arbitrage trade

#### Settings

- `getSettings()` - Get all settings
- `saveSettings(settings)` - Save settings
- `resetSettings()` - Reset to default settings
- `exportSettings()` - Export settings
- `importSettings(settings)` - Import settings

#### Exchange Management

- `getExchanges()` - Get configured exchanges
- `addExchange(config)` - Add new exchange
- `removeExchange(exchangeId)` - Remove exchange
- `testExchangeConnection(exchangeId)` - Test exchange connection

#### Analytics

- `getTradingStats()` - Get trading statistics
- `getPerformanceMetrics()` - Get performance metrics
- `getTradeHistory(limit)` - Get trade history
- `getPnLReport(timeframe)` - Get P&L report
- `getRiskMetrics()` - Get risk metrics

## Integration Example

### In a React Component

```javascript
import React from 'react';
import { useIPC, useBotStatus, usePortfolio } from '../hooks/useIPC';
import { Button, Card, CardContent } from '@mui/material';

function TradingDashboard() {
  const { callIPC, loading, error } = useIPC();
  const { data: botStatus, loading: botLoading } = useBotStatus();
  const { data: portfolio, loading: portfolioLoading } = usePortfolio();

  const handleStartTrading = async () => {
    try {
      await callIPC('startBot');
    } catch (err) {
      console.error('Failed to start trading:', err);
    }
  };

  return (
    <Card>
      <CardContent>
        <h2>Trading Dashboard</h2>
        {botLoading ? 'Loading...' : JSON.stringify(botStatus)}
        {portfolioLoading ? 'Loading...' : JSON.stringify(portfolio)}
        <Button onClick={handleStartTrading} disabled={loading}>
          Start Trading
        </Button>
      </CardContent>
    </Card>
  );
}
```

### Error Handling

All IPC methods return a standardized response format:

```javascript
{
  success: boolean,
  data?: any,
  error?: string
}
```

### Environment Detection

The IPC service automatically detects if it's running in an Electron environment:

- In Electron: Uses actual IPC communication
- In web/browser: Uses mock data for development/testing

### Security Features

1. **Context Isolation**: Prevents direct Node.js access
2. **Channel Validation**: Only allowed channels can be used
3. **Input Sanitization**: Prevents injection attacks
4. **Response Validation**: Ensures safe data transfer

## Testing

### Mock Mode

When not in Electron, the IPC service provides mock responses for all methods, allowing development and testing without
the full Electron environment.

### Example Test

```javascript
import ipcService from '../services/ipcService';

test('getBotStatus returns mock data in non-Electron', async () => {
  const result = await ipcService.getBotStatus();
  expect(result.success).toBe(true);
  expect(result.data).toBeDefined();
});
```

## Best Practices

1. **Always check `success`** before using `data`
2. **Handle errors gracefully** in the UI
3. **Use the provided hooks** for React components
4. **Don't call IPC methods directly** in render functions
5. **Use loading states** for better UX
6. **Implement proper error boundaries** for IPC failures

## Troubleshooting

### Common Issues

1. **"electronAPI is not defined"**: Ensure preload.js is properly configured
2. **IPC calls failing**: Check if main process handlers are registered
3. **TypeScript errors**: Ensure type definitions are included

### Debug Mode

Enable debug logging:

```javascript
// In renderer process
window.electronAPI.debug = true;