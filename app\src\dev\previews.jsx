import React from 'react';
import {ComponentPreview, Previews} from '@react-buddy/ide-toolbox';
import {PaletteTree} from './palette';
import ParticleBackground from '../components/ParticleBackground';

/**
 * ComponentPreviews is a React component that renders a collection of component previews.
 * The previews are grouped into a single `Previews` component, which is wrapped in a `PaletteTree` component.
 * The `PaletteTree` component is used to group the previews by category.
 * The previews are rendered in the order they are declared.
 * Each preview is rendered as a `ComponentPreview` component, which is passed the `path` prop.
 * The `path` prop is used to determine the route for the preview.
 * The `ComponentPreview` component is also passed the `children` prop, which is the component to be rendered.
 * The `children` prop is optional.
 * The `ComponentPreview` component is also passed the `palette` prop, which is the `PaletteTree` component.
 * The `palette` prop is optional.
 * The `ComponentPreviews` component is also passed the `palette` prop, which is the `PaletteTree` component.
 * The `palette` prop is required.
 * The `ComponentPreviews` component is also passed the `children` prop, which is the component to be rendered.
 * The `children` prop is optional.
 */

const ComponentPreviews = () => {
    return (
        <Previews palette={<PaletteTree/>}>
            <ComponentPreview path="/ParticleBackground">
                <ParticleBackground/>
            </ComponentPreview>
        </Previews>
    );
};

export default ComponentPreviews;
export {ComponentPreviews};
export {PaletteTree} from './palette';
export {useInitial} from './useInitial';
export {ExampleLoaderComponent} from './palette';
