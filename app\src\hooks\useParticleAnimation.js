import {useCallback, useEffect, useRef, useState} from 'react';

/**
 * Custom hook for managing particle animations
 * Integrates with the particle system defined in animations.css
 */
const useParticleAnimation = (options = {}) => {
    const containerRef = useRef(null);
    const [isActive, setIsActive] = useState(false);
    const particlesRef = useRef([]);

    const {
        colors = ['cyan', 'purple', 'gold', 'green', 'pink'],
        animationDuration = 15000,
        spawnRate = 200,
        enabled = true
    } = options;

    const startAnimation = useCallback(() => {
        setIsActive(true);
    }, []);

    const stopAnimation = useCallback(() => {
        setIsActive(false);
        if (containerRef.current) {
            containerRef.current.innerHTML = '';
        }
        particlesRef.current = [];
    }, []);

    const createParticle = useCallback(() => {
        if (!containerRef.current || !enabled) return;

        /**
         * Generates a cryptographically secure random number between 0 and 1.
         * Utilizes the Web Crypto API for better randomness quality compared to Math.random().
         * @returns {number} A random number in the range [0, 1).
         */

        const random = () => {
            const array = new Uint32Array(1);
            window.crypto.getRandomValues(array);
            return array[0] / (Math.pow(2, 32) - 1);
        };

        const particle = document.createElement('div');
        const colorClass = colors[Math.floor(random() * colors.length)];

        particle.className = `particle particle-${colorClass} gpu-accelerated`;

        // Random starting position
        particle.style.left = `${random() * 100}%`;
        const duration = animationDuration + random() * 5000;
        particle.style.animationDuration = `${duration}ms`;
        particle.style.animationDelay = `${random() * 2000}ms`;

        containerRef.current.appendChild(particle);
        particlesRef.current.push(particle);

        // Remove particle after animation completes
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
            particlesRef.current = particlesRef.current.filter(p => p !== particle);
        }, duration + 2000);
    }, [enabled, colors, animationDuration]);

    const toggleAnimation = useCallback(() => {
        if (isActive) {
            stopAnimation();
        } else {
            startAnimation();
        }
    }, [isActive, startAnimation, stopAnimation]);

    useEffect(() => {
        if (!isActive || !enabled) return;

        const interval = setInterval(createParticle, spawnRate);

        return () => {
            clearInterval(interval);
        };
    }, [isActive, enabled, spawnRate, createParticle]);

    // Auto-start if enabled
    useEffect(() => {
        if (enabled) {
            startAnimation();
        }

        return () => {
            stopAnimation();
        };
    }, [enabled, startAnimation, stopAnimation]);

    // Handle reduced motion preference
    useEffect(() => {
        const mediaQuery = window.matchMedia('(prefers-reduced-motion)');

        /**
         * Handles changes to the prefers-reduced-motion media query.
         * If prefers-reduced-motion is enabled, stops the animation.
         * If prefers-reduced-motion is disabled and the animation is enabled, starts the animation.
         */
        const handleChange = () => {
            if (mediaQuery.matches) {
                stopAnimation();
            } else if (enabled) {
                startAnimation();
            }
        };

        mediaQuery.addEventListener('change', handleChange);

        handleChange(); // Check initial state

        return () => {
            mediaQuery.removeEventListener('change', handleChange);
        };
    }, [enabled, startAnimation, stopAnimation]);

    return {
        containerRef,
        isActive,
        startAnimation,
        stopAnimation,
        toggleAnimation,
        particleCount
    };
};

export default useParticleAnimation;
