# Production Deployment Troubleshooting Guide

## Meme Coin Trader - Troubleshooting Guide

This guide provides solutions to common issues encountered during production deployment and runtime.

---

## Table of Contents

1. [Build Issues](#build-issues)
2. [Runtime Issues](#runtime-issues)
3. [Database Issues](#database-issues)
4. [Trading System Issues](#trading-system-issues)
5. [Performance Issues](#performance-issues)
6. [Network and API Issues](#network-and-api-issues)
7. [Windows-Specific Issues](#windows-specific-issues)
8. [Security Issues](#security-issues)
9. [Diagnostic Tools](#diagnostic-tools)
10. [Recovery Procedures](#recovery-procedures)

---

## Build Issues

### 1. Node.js Version Mismatch

**Problem:** Build fails with Node.js version errors
```
Error: The engine "node" is incompatible with this module.
Expected version ">=18.0.0". Got "16.14.0"
```

**Solution:**
```bash
# Check current Node.js version
node --version

# Install correct version using nvm (recommended)
nvm install 18.0.0
nvm use 18.0.0

# Or download from https://nodejs.org/
# Verify installation
node --version  # Should be 18.0.0+
npm --version   # Should be 8.0.0+
```

**Prevention:**
- Use `.nvmrc` file to specify Node.js version
- Add version check to build scripts
- Document Node.js requirements clearly

### 2. Native Module Compilation Errors

**Problem:** Native modules fail to compile
```
Error: node-gyp rebuild failed
gyp ERR! stack Error: Could not find MSBuild
```

**Windows Solution:**
```bash
# Install Visual Studio Build Tools
npm install --global windows-build-tools

# Or install Visual Studio Community with C++ workload
# Set MSBuild path if needed
set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe"

# Rebuild native modules
npm run rebuild-native
```

**Alternative Solutions:**
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Use pre-built binaries if available
npm install --prefer-binary
```

### 3. Memory Issues During Build

**Problem:** Build fails with out-of-memory errors
```
FATAL ERROR: Ineffective mark-compacts near heap limit
Allocation failed - JavaScript heap out of memory
```

**Solution:**
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"

# Or set in package.json scripts
"build:production": "cross-env NODE_OPTIONS=--max-old-space-size=4096 webpack --config webpack.config.production.js"

# For Windows PowerShell
$env:NODE_OPTIONS="--max-old-space-size=4096"
```

**Prevention:**
- Monitor build memory usage
- Optimize webpack configuration
- Use code splitting to reduce bundle size

### 4. Webpack Bundle Size Issues

**Problem:** Bundle size exceeds limits
```
WARNING: asset size limit: The following asset(s) exceed the recommended size limit (244 KiB):
  main.js (512 KiB)
```

**Solution:**
```bash
# Analyze bundle composition
npm run analyze-bundle

# Enable code splitting in webpack.config.js
optimization: {
  splitChunks: {
    chunks: 'all',
    maxSize: 200000
  }
}

# Remove unused dependencies
npm run analyze-dependencies
npm uninstall unused-package

# Use dynamic imports for large components
const LazyComponent = React.lazy(() => import('./LazyComponent'));
```

### 5. TypeScript Compilation Errors

**Problem:** TypeScript compilation fails
```
error TS2307: Cannot find module '@types/node'
```

**Solution:**
```bash
# Install missing type definitions
npm install --save-dev @types/node @types/react @types/react-dom

# Check TypeScript configuration
npx tsc --noEmit

# Update tsconfig.json if needed
{
  "compilerOptions": {
    "target": "es2020",
    "lib": ["dom", "dom.iterable", "es6"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx"
  }
}
```

---

## Runtime Issues

### 1. Application Won't Start

**Problem:** Electron app fails to launch

**Diagnostic Steps:**
```bash
# Check for errors in console
npm run electron-dev

# Verify build integrity
npm run validate:build

# Check system requirements
npm run system-info

# Test main process
node main.js

# Check preload script
node -e "console.log(require('./preload.js'))"
```

**Common Solutions:**
```bash
# Reset configuration
npm run config:reset

# Clear application data
rm -rf %APPDATA%/meme-coin-trader  # Windows
rm -rf ~/Library/Application\ Support/meme-coin-trader  # macOS
rm -rf ~/.config/meme-coin-trader  # Linux

# Reinstall dependencies
rm -rf node_modules
npm install
```

### 2. White Screen on Startup

**Problem:** Application shows blank white screen

**Solution:**
```javascript
// Check main.js for proper window creation
function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: false,  // Don't show until ready
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // Load the app
  const isDev = process.env.ELECTRON_IS_DEV === '1';
  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
  } else {
    mainWindow.loadFile(path.join(__dirname, 'build/index.html'));
  }

  // Show when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Debug in development
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }
}
```

### 3. IPC Communication Failures

**Problem:** Frontend cannot communicate with backend

**Diagnostic Steps:**
```javascript
// Test IPC in renderer process (DevTools console)
window.electronAPI.testConnection()
  .then(result => console.log('IPC working:', result))
  .catch(error => console.error('IPC failed:', error));

// Check preload.js exposure
console.log('Available APIs:', Object.keys(window.electronAPI));
```

**Solution:**
```javascript
// Verify preload.js setup
const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  testConnection: () => ipcRenderer.invoke('test-connection'),
  // ... other API methods
});

// Verify main.js handlers
const { ipcMain } = require('electron');

ipcMain.handle('test-connection', async () => {
  return { status: 'connected', timestamp: Date.now() };
});
```

### 4. Module Resolution Errors

**Problem:** Cannot resolve modules at runtime
```
Error: Cannot find module 'trading/TradingOrchestrator'
```

**Solution:**
```javascript
// Use absolute paths in main.js
const path = require('path');
const TradingOrchestrator = require(path.join(__dirname, 'trading', 'TradingOrchestrator'));

// Or fix module resolution in package.json
{
  "main": "main.js",
  "paths": {
    "trading/*": ["./trading/*"]
  }
}
```

---

## Database Issues

### 1. Database Connection Failures

**Problem:** Cannot connect to database
```
Error: SQLITE_CANTOPEN: unable to open database file
```

**Solution:**
```bash
# Check database file permissions
ls -la databases/
chmod 644 databases/trading_system.db

# Verify database path
cd trading
node -e "console.log(require('path').resolve('./databases/trading_system.db'))"

# Test database connection
npm run test:database

# Initialize database if missing
npm run init-db
```

### 2. Database Corruption

**Problem:** SQLite database is corrupted
```
Error: database disk image is malformed
```

**Solution:**
```bash
# Check database integrity
sqlite3 databases/trading_system.db "PRAGMA integrity_check;"

# Attempt to repair
sqlite3 databases/trading_system.db ".recover" | sqlite3 databases/trading_system_recovered.db

# Restore from backup
npm run restore:database

# Or rebuild database
cd trading
npm run init-db --force
```

### 3. Migration Failures

**Problem:** Database schema migration fails
```
Error: Migration failed at version 002_add_whale_tracking
```

**Solution:**
```bash
# Check migration status
cd trading
npm run migration:status

# Rollback failed migration
npm run migration:rollback

# Apply migrations manually
npm run migration:up

# Check migration files
ls -la migrations/
```

### 4. Database Lock Issues

**Problem:** Database is locked
```
Error: SQLITE_BUSY: database is locked
```

**Solution:**
```bash
# Check for running processes
ps aux | grep "meme-coin-trader"
pkill -f "meme-coin-trader"

# Remove lock files
rm -f databases/*.db-wal
rm -f databases/*.db-shm

# Increase busy timeout in database configuration
DB_BUSY_TIMEOUT=30000
```

---

## Trading System Issues

### 1. Exchange API Connection Failures

**Problem:** Cannot connect to exchange APIs
```
Error: Invalid API key
Error: Request timeout
```

**Solution:**
```bash
# Verify API credentials
cd trading
npm run config:validate

# Test API connectivity
npm run test:api-keys

# Check API permissions on exchange
# Ensure trading permissions are enabled

# Verify network connectivity
curl -I https://api.binance.com/api/v3/ping
```

**API Key Troubleshooting:**
```javascript
// Test API key validity
const ccxt = require('ccxt');

async function testApiKey() {
  const exchange = new ccxt.binance({
    apiKey: process.env.BINANCE_API_KEY,
    secret: process.env.BINANCE_API_SECRET,
    testnet: process.env.BINANCE_TESTNET === 'true'
  });

  try {
    const balance = await exchange.fetchBalance();
    console.log('API key valid:', Object.keys(balance.total).length > 0);
  } catch (error) {
    console.error('API key invalid:', error.message);
  }
}
```

### 2. Trading Engine Startup Failures

**Problem:** Trading engines fail to initialize
```
Error: TradingOrchestrator initialization failed
```

**Solution:**
```bash
# Check component dependencies
cd trading
npm run validate:components

# Test individual components
node -e "
const TradingOrchestrator = require('./TradingOrchestrator');
const orchestrator = new TradingOrchestrator();
orchestrator.initialize().then(() => console.log('OK')).catch(console.error);
"

# Check configuration
npm run config:validate

# Review logs
tail -f logs/trading.log
```

### 3. Real-time Data Issues

**Problem:** Market data not updating
```
Warning: No market data received for 60 seconds
```

**Solution:**
```bash
# Check WebSocket connections
npm run test:websockets

# Verify exchange status
curl https://api.binance.com/api/v3/exchangeInfo

# Check rate limiting
# Review API usage in logs

# Restart data collection
npm run restart:data-collector
```

### 4. Position Management Errors

**Problem:** Positions not updating correctly
```
Error: Position size mismatch
Error: Insufficient balance
```

**Solution:**
```javascript
// Sync positions with exchange
async function syncPositions() {
  const exchange = new ccxt.binance(/* config */);
  const positions = await exchange.fetchPositions();
  
  // Update local database
  for (const position of positions) {
    await updateLocalPosition(position);
  }
}

// Check balance before trading
async function checkBalance(symbol, amount) {
  const balance = await exchange.fetchBalance();
  const currency = symbol.split('/')[1];
  
  if (balance[currency].free < amount) {
    throw new Error(`Insufficient ${currency} balance`);
  }
}
```

---

## Performance Issues

### 1. Slow Application Startup

**Problem:** Application takes too long to start

**Diagnostic:**
```bash
# Measure startup time
time npm run electron

# Profile startup
npm run performance:startup

# Check component initialization times
npm run debug:startup
```

**Solutions:**
```javascript
// Lazy load components
const LazyDashboard = React.lazy(() => import('./components/Dashboard'));

// Defer non-critical initialization
setTimeout(() => {
  initializeNonCriticalComponents();
}, 1000);

// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  // Component logic
});
```

### 2. High Memory Usage

**Problem:** Application consumes too much memory

**Diagnostic:**
```bash
# Monitor memory usage
npm run memory:monitor

# Check for memory leaks
npm run memory:analyze

# Profile memory usage
node --inspect main.js
```

**Solutions:**
```javascript
// Implement memory monitoring
function monitorMemory() {
  const usage = process.memoryUsage();
  console.log('Memory usage:', {
    rss: Math.round(usage.rss / 1024 / 1024) + 'MB',
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024) + 'MB',
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + 'MB'
  });
  
  // Trigger garbage collection if needed
  if (usage.heapUsed > 512 * 1024 * 1024) { // 512MB threshold
    if (global.gc) {
      global.gc();
    }
  }
}

// Clean up intervals and listeners
function cleanup() {
  clearInterval(dataUpdateInterval);
  removeAllListeners();
}
```

### 3. Database Performance Issues

**Problem:** Database queries are slow

**Diagnostic:**
```bash
# Enable query logging
LOG_DATABASE_QUERIES=true

# Analyze slow queries
npm run database:analyze

# Check database size
ls -lh databases/
```

**Solutions:**
```sql
-- Add indexes for frequently queried columns
CREATE INDEX idx_trades_timestamp ON trades(timestamp);
CREATE INDEX idx_positions_symbol ON positions(symbol);

-- Optimize database settings
PRAGMA journal_mode=WAL;
PRAGMA synchronous=NORMAL;
PRAGMA cache_size=10000;
PRAGMA temp_store=memory;

-- Clean up old data
DELETE FROM trades WHERE timestamp < datetime('now', '-30 days');
VACUUM;
```

### 4. UI Performance Issues

**Problem:** UI is slow or unresponsive

**Solutions:**
```javascript
// Use React.useMemo for expensive calculations
const expensiveValue = React.useMemo(() => {
  return calculateExpensiveValue(data);
}, [data]);

// Debounce frequent updates
const debouncedUpdate = useCallback(
  debounce((value) => {
    updateState(value);
  }, 300),
  []
);

// Virtualize large lists
import { FixedSizeList as List } from 'react-window';

const VirtualizedList = ({ items }) => (
  <List
    height={600}
    itemCount={items.length}
    itemSize={50}
    itemData={items}
  >
    {Row}
  </List>
);
```

---

## Network and API Issues

### 1. Rate Limiting

**Problem:** API requests are being rate limited
```
Error: Rate limit exceeded
HTTP 429: Too Many Requests
```

**Solution:**
```javascript
// Implement exponential backoff
async function makeRequestWithBackoff(requestFn, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn();
    } catch (error) {
      if (error.status === 429 && i < maxRetries - 1) {
        const delay = Math.pow(2, i) * 1000; // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      throw error;
    }
  }
}

// Rate limiting configuration
const rateLimiter = {
  requests: 0,
  windowStart: Date.now(),
  maxRequests: 1200, // per minute
  windowSize: 60000   // 1 minute
};

function checkRateLimit() {
  const now = Date.now();
  if (now - rateLimiter.windowStart > rateLimiter.windowSize) {
    rateLimiter.requests = 0;
    rateLimiter.windowStart = now;
  }
  
  if (rateLimiter.requests >= rateLimiter.maxRequests) {
    throw new Error('Rate limit exceeded');
  }
  
  rateLimiter.requests++;
}
```

### 2. Network Connectivity Issues

**Problem:** Intermittent network failures
```
Error: ENOTFOUND api.binance.com
Error: ECONNRESET
```

**Solution:**
```javascript
// Implement connection retry logic
const axios = require('axios');

const apiClient = axios.create({
  timeout: 30000,
  retry: 3,
  retryDelay: 1000
});

// Add retry interceptor
apiClient.interceptors.response.use(
  response => response,
  async error => {
    const config = error.config;
    
    if (!config || !config.retry) {
      return Promise.reject(error);
    }
    
    config.retryCount = config.retryCount || 0;
    
    if (config.retryCount >= config.retry) {
      return Promise.reject(error);
    }
    
    config.retryCount++;
    
    const delay = config.retryDelay * Math.pow(2, config.retryCount - 1);
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return apiClient(config);
  }
);
```

### 3. WebSocket Connection Issues

**Problem:** WebSocket connections keep dropping
```
WebSocket connection closed unexpectedly
Error: WebSocket is not open
```

**Solution:**
```javascript
class ReconnectingWebSocket {
  constructor(url, options = {}) {
    this.url = url;
    this.options = options;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
    this.reconnectInterval = options.reconnectInterval || 5000;
    this.connect();
  }
  
  connect() {
    this.ws = new WebSocket(this.url);
    
    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.reconnectAttempts = 0;
      this.onopen && this.onopen();
    };
    
    this.ws.onmessage = (event) => {
      this.onmessage && this.onmessage(event);
    };
    
    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.reconnect();
    };
    
    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.onerror && this.onerror(error);
    };
  }
  
  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      setTimeout(() => this.connect(), this.reconnectInterval);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }
}
```

---

## Windows-Specific Issues

### 1. Antivirus Blocking Application

**Problem:** Windows Defender or antivirus blocks the application
```
Windows Defender SmartScreen prevented an unrecognized app from starting
```

**Solutions:**
1. **Code sign the application** (recommended)
2. **Add exclusions to antivirus**
3. **Provide user instructions:**

```powershell
# Add Windows Defender exclusion
Add-MpPreference -ExclusionPath "C:\Program Files\Meme Coin Trader"
Add-MpPreference -ExclusionProcess "Meme Coin Trader.exe"
```

### 2. Permission Errors

**Problem:** Permission denied errors during installation or runtime
```
Error: EACCES: permission denied, open 'C:\Program Files\...'
```

**Solutions:**
```powershell
# Run as administrator
Right-click installer -> "Run as administrator"

# Or install to user directory
# Change installation path to %LOCALAPPDATA%\Programs\Meme Coin Trader

# Fix file permissions
icacls "C:\Program Files\Meme Coin Trader" /grant Users:F /T
```

### 3. DLL Loading Issues

**Problem:** Missing or incompatible DLLs
```
The application was unable to start correctly (0xc000007b)
```

**Solutions:**
```powershell
# Install Visual C++ Redistributable
# Download from Microsoft website

# Check for missing DLLs using Dependency Walker
# Or use PowerShell
Get-Process "Meme Coin Trader" | Select-Object -ExpandProperty Modules

# Ensure all required DLLs are bundled
```

### 4. Registry Issues

**Problem:** Registry access errors
```
Error: Registry key access denied
```

**Solutions:**
```powershell
# Grant registry permissions
reg add "HKLM\SOFTWARE\Meme Coin Trader" /f
reg add "HKCU\SOFTWARE\Meme Coin Trader" /f

# Or use HKEY_CURRENT_USER instead of HKEY_LOCAL_MACHINE
```

---

## Security Issues

### 1. Certificate Validation Errors

**Problem:** SSL/TLS certificate validation fails
```
Error: unable to verify the first certificate
Error: certificate has expired
```

**Solutions:**
```javascript
// Update certificate bundle
npm update ca-certificates

// For development only - disable certificate validation
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

// Better solution - implement certificate pinning
const https = require('https');
const crypto = require('crypto');

const expectedFingerprint = 'AA:BB:CC:DD:EE:FF...';

const agent = new https.Agent({
  checkServerIdentity: (host, cert) => {
    const fingerprint = crypto
      .createHash('sha256')
      .update(cert.raw)
      .digest('hex')
      .toUpperCase()
      .match(/.{2}/g)
      .join(':');
    
    if (fingerprint !== expectedFingerprint) {
      throw new Error('Certificate fingerprint mismatch');
    }
  }
});
```

### 2. API Key Security Issues

**Problem:** API keys exposed or compromised

**Solutions:**
```javascript
// Use secure storage
const keytar = require('keytar');

// Store API keys securely
await keytar.setPassword('meme-coin-trader', 'binance-api-key', apiKey);

// Retrieve API keys
const apiKey = await keytar.getPassword('meme-coin-trader', 'binance-api-key');

// Encrypt API keys in database
const crypto = require('crypto');

function encryptApiKey(apiKey, encryptionKey) {
  const cipher = crypto.createCipher('aes-256-gcm', encryptionKey);
  let encrypted = cipher.update(apiKey, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}
```

---

## Diagnostic Tools

### 1. System Information Script

Create `scripts/system-info.js`:

```javascript
#!/usr/bin/env node

const os = require('os');
const fs = require('fs');
const path = require('path');

function getSystemInfo() {
  console.log('🖥️  System Information');
  console.log('===================');
  console.log(`Platform: ${os.platform()}`);
  console.log(`Architecture: ${os.arch()}`);
  console.log(`OS Release: ${os.release()}`);
  console.log(`Node.js: ${process.version}`);
  console.log(`npm: ${process.env.npm_version || 'unknown'}`);
  console.log(`Total Memory: ${Math.round(os.totalmem() / 1024 / 1024 / 1024)}GB`);
  console.log(`Free Memory: ${Math.round(os.freemem() / 1024 / 1024 / 1024)}GB`);
  console.log(`CPU Cores: ${os.cpus().length}`);
  console.log(`CPU Model: ${os.cpus()[0].model}`);
  
  console.log('\n📁 Application Information');
  console.log('========================');
  console.log(`Working Directory: ${process.cwd()}`);
  console.log(`Executable Path: ${process.execPath}`);
  console.log(`Process ID: ${process.pid}`);
  console.log(`Process Title: ${process.title}`);
  
  // Check important files
  const importantFiles = [
    'package.json',
    'main.js',
    'preload.js',
    'build/index.html',
    'trading/package.json',
    'databases/trading_system.db'
  ];
  
  console.log('\n📄 File Status');
  console.log('=============');
  importantFiles.forEach(file => {
    const exists = fs.existsSync(file);
    const status = exists ? '✅' : '❌';
    console.log(`${status} ${file}`);
    
    if (exists) {
      const stats = fs.statSync(file);
      console.log(`   Size: ${stats.size} bytes, Modified: ${stats.mtime.toISOString()}`);
    }
  });
  
  // Check environment variables
  console.log('\n🌍 Environment Variables');
  console.log('======================');
  const envVars = [
    'NODE_ENV',
    'ELECTRON_IS_DEV',
    'REACT_APP_VERSION',
    'DB_TYPE',
    'LOG_LEVEL'
  ];
  
  envVars.forEach(varName => {
    const value = process.env[varName];
    const status = value ? '✅' : '❌';
    console.log(`${status} ${varName}: ${value || 'not set'}`);
  });
}

if (require.main === module) {
  getSystemInfo();
}

module.exports = { getSystemInfo };
```

### 2. Health Check Script

Create `scripts/health-check.js`:

```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

async function healthCheck() {
  console.log('🏥 Health Check Starting...');
  
  const checks = [];
  
  // Check Node.js version
  checks.push({
    name: 'Node.js Version',
    check: () => {
      const version = process.version;
      const major = parseInt(version.slice(1).split('.')[0]);
      return major >= 18;
    },
    message: `Node.js ${process.version} (requires 18+)`
  });
  
  // Check npm version
  checks.push({
    name: 'npm Version',
    check: () => {
      try {
        const { execSync } = require('child_process');
        const version = execSync('npm --version', { encoding: 'utf8' }).trim();
        const major = parseInt(version.split('.')[0]);
        return major >= 8;
      } catch {
        return false;
      }
    },
    message: 'npm version check'
  });
  
  // Check database file
  checks.push({
    name: 'Database File',
    check: () => fs.existsSync('databases/trading_system.db'),
    message: 'Trading database file exists'
  });
  
  // Check build directory
  checks.push({
    name: 'Build Directory',
    check: () => fs.existsSync('build') && fs.existsSync('build/index.html'),
    message: 'Production build exists'
  });
  
  // Check trading system
  checks.push({
    name: 'Trading System',
    check: () => fs.existsSync('trading/package.json'),
    message: 'Trading system package.json exists'
  });
  
  // Run checks
  let passed = 0;
  let failed = 0;
  
  for (const check of checks) {
    try {
      const result = await check.check();
      if (result) {
        console.log(`✅ ${check.name}: ${check.message}`);
        passed++;
      } else {
        console.log(`❌ ${check.name}: ${check.message}`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${check.name}: Error - ${error.message}`);
      failed++;
    }
  }
  
  console.log(`\n📊 Health Check Summary:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All health checks passed!');
    return true;
  } else {
    console.log('\n⚠️  Some health checks failed. Please review the issues above.');
    return false;
  }
}

if (require.main === module) {
  healthCheck().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { healthCheck };
```

### 3. Log Analyzer Script

Create `scripts/analyze-logs.js`:

```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function analyzeLogs() {
  console.log('📊 Log Analysis');
  console.log('==============');
  
  const logFiles = [
    'logs/trading.log',
    'logs/error.log',
    'logs/combined.log'
  ];
  
  const analysis = {
    totalLines: 0,
    errorCount: 0,
    warningCount: 0,
    infoCount: 0,
    recentErrors: [],
    commonErrors: new Map()
  };
  
  logFiles.forEach(logFile => {
    if (!fs.existsSync(logFile)) {
      console.log(`⚠️  Log file not found: ${logFile}`);
      return;
    }
    
    console.log(`\n📄 Analyzing ${logFile}:`);
    
    const content = fs.readFileSync(logFile, 'utf8');
    const lines = content.split('\n').filter(line => line.trim());
    
    analysis.totalLines += lines.length;
    
    lines.forEach(line => {
      if (line.includes('ERROR') || line.includes('error')) {
        analysis.errorCount++;
        
        // Extract error message
        const errorMatch = line.match(/ERROR.*?:(.*?)(?:\n|$)/);
        if (errorMatch) {
          const errorMsg = errorMatch[1].trim();
          analysis.commonErrors.set(errorMsg, (analysis.commonErrors.get(errorMsg) || 0) + 1);
          
          // Keep recent errors
          if (analysis.recentErrors.length < 10) {
            analysis.recentErrors.push({
              timestamp: line.match(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)?.[0] || 'unknown',
              message: errorMsg
            });
          }
        }
      } else if (line.includes('WARN') || line.includes('warn')) {
        analysis.warningCount++;
      } else if (line.includes('INFO') || line.includes('info')) {
        analysis.infoCount++;
      }
    });
    
    console.log(`  Lines: ${lines.length}`);
    console.log(`  Size: ${Math.round(fs.statSync(logFile).size / 1024)}KB`);
  });
  
  console.log('\n📈 Summary:');
  console.log(`Total log lines: ${analysis.totalLines}`);
  console.log(`Errors: ${analysis.errorCount}`);
  console.log(`Warnings: ${analysis.warningCount}`);
  console.log(`Info: ${analysis.infoCount}`);
  
  if (analysis.commonErrors.size > 0) {
    console.log('\n🔥 Most Common Errors:');
    const sortedErrors = Array.from(analysis.commonErrors.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);
    
    sortedErrors.forEach(([error, count]) => {
      console.log(`  ${count}x: ${error}`);
    });
  }
  
  if (analysis.recentErrors.length > 0) {
    console.log('\n🕒 Recent Errors:');
    analysis.recentErrors.forEach(error => {
      console.log(`  ${error.timestamp}: ${error.message}`);
    });
  }
  
  // Recommendations
  console.log('\n💡 Recommendations:');
  if (analysis.errorCount > 100) {
    console.log('  - High error count detected. Review error patterns above.');
  }
  if (analysis.warningCount > analysis.errorCount * 5) {
    console.log('  - Many warnings detected. Consider addressing warning conditions.');
  }
  if (analysis.totalLines > 100000) {
    console.log('  - Large log files detected. Consider implementing log rotation.');
  }
}

if (require.main === module) {
  analyzeLogs();
}

module.exports = { analyzeLogs };
```

---

## Recovery Procedures

### 1. Factory Reset

Create `scripts/factory-reset.js`:

```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

async function factoryReset() {
  console.log('🏭 Factory Reset Procedure');
  console.log('=========================');
  console.log('This will reset the application to default settings.');
  console.log('⚠️  WARNING: This will delete all configuration and data!');
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const answer = await new Promise(resolve => {
    rl.question('Are you sure you want to continue? (yes/no): ', resolve);
  });
  
  rl.close();
  
  if (answer.toLowerCase() !== 'yes') {
    console.log('Factory reset cancelled.');
    return;
  }
  
  console.log('\n🗑️  Removing application data...');
  
  const itemsToRemove = [
    'databases/trading_system.db',
    'databases/trading_system.db-wal',
    'databases/trading_system.db-shm',
    'logs',
    'backups',
    '.env.local',
    'trading/.env.local',
    'config/user-settings.json'
  ];
  
  itemsToRemove.forEach(item => {
    try {
      if (fs.existsSync(item)) {
        const stats = fs.statSync(item);
        if (stats.isDirectory()) {
          fs.rmSync(item, { recursive: true, force: true });
        } else {
          fs.unlinkSync(item);
        }
        console.log(`✅ Removed: ${item}`);
      } else {
        console.log(`⚠️  Not found: ${item}`);
      }
    } catch (error) {
      console.log(`❌ Failed to remove ${item}: ${error.message}`);
    }
  });
  
  console.log('\n🔄 Reinitializing application...');
  
  // Recreate directories
  const dirsToCreate = ['logs', 'backups', 'databases'];
  dirsToCreate.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✅ Created directory: ${dir}`);
    }
  });
  
  // Initialize database
  try {
    const { execSync } = require('child_process');
    execSync('cd trading && npm run init-db', { stdio: 'inherit' });
    console.log('✅ Database initialized');
  } catch (error) {
    console.log('❌ Failed to initialize database:', error.message);
  }
  
  console.log('\n🎉 Factory reset completed!');
  console.log('Please restart the application.');
}

if (require.main === module) {
  factoryReset().catch(console.error);
}

module.exports = { factoryReset };
```

### 2. Backup and Restore

Create `scripts/backup-restore.js`:

```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const extract = require('extract-zip');

async function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = `backups/backup-${timestamp}.zip`;
  
  console.log(`📦 Creating backup: ${backupPath}`);
  
  const output = fs.createWriteStream(backupPath);
  const archive = archiver('zip', { zlib: { level: 9 } });
  
  return new Promise((resolve, reject) => {
    output.on('close', () => {
      console.log(`✅ Backup created: ${archive.pointer()} bytes`);
      resolve(backupPath);
    });
    
    archive.on('error', reject);
    archive.pipe(output);
    
    // Add files to backup
    const filesToBackup = [
      'databases',
      'config',
      '.env.local',
      'trading/.env.local'
    ];
    
    filesToBackup.forEach(item => {
      if (fs.existsSync(item)) {
        const stats = fs.statSync(item);
        if (stats.isDirectory()) {
          archive.directory(item, item);
        } else {
          archive.file(item, { name: item });
        }
      }
    });
    
    archive.finalize();
  });
}

async function restoreBackup(backupPath) {
  if (!fs.existsSync(backupPath)) {
    throw new Error(`Backup file not found: ${backupPath}`);
  }
  
  console.log(`📥 Restoring backup: ${backupPath}`);
  
  // Create temporary restore directory
  const tempDir = 'temp-restore';
  if (fs.existsSync(tempDir)) {
    fs.rmSync(tempDir, { recursive: true, force: true });
  }
  fs.mkdirSync(tempDir);
  
  try {
    // Extract backup
    await extract(backupPath, { dir: path.resolve(tempDir) });
    
    // Restore files
    const itemsToRestore = fs.readdirSync(tempDir);
    
    itemsToRestore.forEach(item => {
      const sourcePath = path.join(tempDir, item);
      const targetPath = item;
      
      // Backup existing files
      if (fs.existsSync(targetPath)) {
        const backupName = `${targetPath}.backup-${Date.now()}`;
        fs.renameSync(targetPath, backupName);
        console.log(`📋 Backed up existing: ${targetPath} -> ${backupName}`);
      }
      
      // Restore file/directory
      const stats = fs.statSync(sourcePath);
      if (stats.isDirectory()) {
        fs.cpSync(sourcePath, targetPath, { recursive: true });
      } else {
        fs.copyFileSync(sourcePath, targetPath);
      }
      
      console.log(`✅ Restored: ${item}`);
    });
    
    console.log('🎉 Backup restored successfully!');
    
  } finally {
    // Clean up temporary directory
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  }
}

// Command line interface
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'backup') {
    createBackup().catch(console.error);
  } else if (command === 'restore') {
    const backupPath = process.argv[3];
    if (!backupPath) {
      console.error('Usage: node backup-restore.js restore <backup-file>');
      process.exit(1);
    }
    restoreBackup(backupPath).catch(console.error);
  } else {
    console.log('Usage:');
    console.log('  node backup-restore.js backup');
    console.log('  node backup-restore.js restore <backup-file>');
  }
}

module.exports = { createBackup, restoreBackup };
```

---

*Last updated: January 29, 2025*
*Version: 1.0.0*