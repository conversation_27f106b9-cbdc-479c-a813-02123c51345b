import React, {useEffect, useState} from 'react';
import {Chip, Tooltip} from '@mui/material';
import {Sync, Wifi, WifiOff} from '@mui/icons-material';
import {motion} from 'framer-motion';

/**
 * ConnectionStatus Component
 * @description Displays real-time connection status to the trading backend
 * @component
 * @param {Object} props
 * @param {number} [props.refreshInterval=2000] - Refresh interval in milliseconds
 * @returns {JSX.Element} Connection status indicator
 */
const ConnectionStatus = ({refreshInterval = 2000}) => {
    const [status, setStatus] = useState('checking');
    const [lastPing, setLastPing] = useState(null);

    const checkConnection = async () => {
        try {
            const api = window.electronAPI || {
                getSystemHealth: () => Promise.resolve({success: true, data: {status: 'healthy'}})
            };

            const response = await api.getSystemHealth();
            const isConnected = response.success && response.data?.status;

            setStatus(isConnected ? 'connected' : 'disconnected');
            setLastPing(new Date());
        } catch (error) {
            setStatus('disconnected');
            setLastPing(new Date());
        }
    };

    useEffect(() => {
        checkConnection();
        const interval = setInterval(checkConnection, refreshInterval);
        return () => clearInterval(interval);
    }, [refreshInterval]);

    const getStatusConfig = () => {
        switch (status) {
            case 'connected':
                return {
                    icon: <Wifi sx={{fontSize: 16}}/>,
                    label: 'Connected',
                    color: '#4caf50',
                    bgColor: 'rgba(76, 175, 80, 0.1)'
                };
            case 'disconnected':
                return {
                    icon: <WifiOff sx={{fontSize: 16}}/>,
                    label: 'Disconnected',
                    color: '#f44336',
                    bgColor: 'rgba(244, 67, 54, 0.1)'
                };
            default:
                return {
                    icon: <Sync sx={{fontSize: 16}}/>,
                    label: 'Checking...',
                    color: '#ff9800',
                    bgColor: 'rgba(255, 152, 0, 0.1)'
                };
        }
    };

    const config = getStatusConfig();

    return (
        <motion.div
            initial={{opacity: 0, scale: 0.9}}
            animate={{opacity: 1, scale: 1}}
            transition={{duration: 0.3}}
        >
            <Tooltip
                title={`${config.label} - Last check: ${lastPing ? lastPing.toLocaleTimeString() : 'Never'}`}
                arrow
                placement="bottom"
            >
                <Chip
                    icon={config.icon}
                    label={config.label}
                    size="small"
                    sx={{
                        backgroundColor: config.bgColor,
                        color: config.color,
                        borderColor: config.color,
                        border: '1px solid',
                        fontSize: '0.75rem',
                        height: 24,
                        '& .MuiChip-icon': {
                            fontSize: 14,
                            color: config.color
                        }
                    }}
                />
            </Tooltip>
        </motion.div>
    );
};

export default ConnectionStatus;
