import {useEffect, useRef, useState} from 'react';
import PropTypes from 'prop-types';
import {Box, IconButton, Tooltip, Typography} from '@mui/material';
import {CheckCircle, Error, PauseCircle, PlayCircle, Refresh, StopCircle, Warning} from '@mui/icons-material';
import {motion} from 'framer-motion';
import logger from '../utils/logger';
import realTimeStatusService from '../services/realTimeStatusService';
import ipcService from '../services/ipcService';

const TradingStatusIndicator = ({
                                    status: initialStatus = 'idle',
                                    onAction = null,
                                    className = ''
                                }) => {
    const [status, setStatus] = useState(initialStatus);
    const [isRunning, setIsRunning] = useState(false);
    const [_lastUpdate, setLastUpdate] = useState(new Date());
    const unsubscribeRef = useRef(null);

    useEffect(() => {
        // Subscribe to system status updates
        const systemStatusUnsubscribe = realTimeStatusService.addListener('system-status', systemStatus => {
            const newStatus = systemStatus.isRunning ? 'running' : 'stopped';
            setStatus(newStatus);
            setIsRunning(systemStatus.isRunning);
            setLastUpdate(new Date(systemStatus.timestamp));
        });

        // Subscribe to trading updates
        const tradingUpdateUnsubscribe = realTimeStatusService.addListener('trading-update', () => {
            setLastUpdate(new Date());
        });

        // Subscribe to startup/shutdown events
        const startupCompleteUnsubscribe = realTimeStatusService.addListener('startup-complete', () => {
            setStatus('running');
            setIsRunning(true);
            setLastUpdate(new Date());
        });

        const shutdownCompleteUnsubscribe = realTimeStatusService.addListener('shutdown-complete', () => {
            setStatus('stopped');
            setIsRunning(false);
            setLastUpdate(new Date());
        });

        const startupErrorUnsubscribe = realTimeStatusService.addListener('startup-error', () => {
            setStatus('error');
            setIsRunning(false);
            setLastUpdate(new Date());
        });

        unsubscribeRef.current = () => {
            systemStatusUnsubscribe();
            tradingUpdateUnsubscribe();
            startupCompleteUnsubscribe();
            shutdownCompleteUnsubscribe();
            startupErrorUnsubscribe();
        };

        // Initial fetch
        fetchTradingStatus();

        return () => {
            if (unsubscribeRef.current) {
                unsubscribeRef.current();
            }
        };
    }, []);

    const fetchTradingStatus = async () => {
        try {
            const statusResult = await ipcService.getRealTimeStatus();
            if (statusResult.success) {
                const systemStatus = statusResult.data;
                const newStatus = systemStatus.isRunning ? 'running' : 'stopped';
                setStatus(newStatus);
                setIsRunning(systemStatus.isRunning);
                setLastUpdate(new Date());
            }

            // Trading stats are available but not currently used
            await ipcService.getTradingStats();
        } catch (error) {
            logger.error('Failed to fetch trading status:', error);
            setStatus('error');
        }
    };

    const getStatusIcon = () => {
        switch (status) {
            case 'running':
                return <PlayCircle sx={{color: '#4caf50', fontSize: 24}}/>;
            case 'paused':
                return <PauseCircle sx={{color: '#ff9800', fontSize: 24}}/>;
            case 'stopped':
                return <StopCircle sx={{color: '#f44336', fontSize: 24}}/>;
            case 'error':
                return <Error sx={{color: '#f44336', fontSize: 24}}/>;
            case 'warning':
                return <Warning sx={{color: '#ff9800', fontSize: 24}}/>;
            default:
                return <CheckCircle sx={{color: '#9e9e9e', fontSize: 24}}/>;
        }
    };

    const getStatusColor = () => {
        switch (status) {
            case 'running':
                return '#4caf50';
            case 'paused':
                return '#ff9800';
            case 'stopped':
                return '#f44336';
            case 'error':
                return '#f44336';
            case 'warning':
                return '#ff9800';
            default:
                return '#9e9e9e';
        }
    };

    const getStatusText = () => {
        switch (status) {
            case 'running':
                return 'Running';
            case 'paused':
                return 'Paused';
            case 'stopped':
                return 'Stopped';
            case 'error':
                return 'Error';
            case 'warning':
                return 'Warning';
            default:
                return 'Unknown';
        }
    };

    return (
        <Box
            className={className}
            sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 2
            }}
        >
            <motion.div
                animate={{
                    scale: [1, 1.1, 1]
                }}
                transition={{
                    duration: 2,
                    repeat: Infinity
                }}
            >
                {getStatusIcon()}
            </motion.div>
            <Box>
                <Typography
                    variant="body2"
                    sx={{
                        color: '#fff',
                        fontWeight: 600
                    }}
                >
                    Trading Status
                </Typography>
                <Typography
                    variant="caption"
                    sx={{
                        color: getStatusColor(),
                        fontWeight: 500
                    }}
                >
                    {getStatusText()}
                </Typography>
            </Box>
            <Box
                sx={{
                    display: 'flex',
                    gap: 1,
                    ml: 'auto'
                }}
            >
                <Tooltip title="Refresh Status">
                    <IconButton
                        size="small"
                        onClick={() => onAction?.('refresh')}
                    >
                        <Refresh sx={{color: '#a259ff'}}/>
                    </IconButton>
                </Tooltip>
                <Tooltip title="Start Trading">
                    <IconButton
                        size="small"
                        onClick={() => onAction?.('start')}
                        disabled={isRunning}
                    >
                        <PlayCircle sx={{color: isRunning ? '#666' : '#4caf50'}}/>
                    </IconButton>
                </Tooltip>
                <Tooltip title="Pause Trading">
                    <IconButton
                        size="small"
                        onClick={() => onAction?.('pause')}
                        disabled={!isRunning}
                    >
                        <PauseCircle sx={{color: !isRunning ? '#666' : '#ff9800'}}/>
                    </IconButton>
                </Tooltip>
                <Tooltip title="Stop Trading">
                    <IconButton
                        size="small"
                        onClick={() => onAction?.('stop')}
                    >
                        <StopCircle sx={{color: '#f44336'}}/>
                    </IconButton>
                </Tooltip>
            </Box>
        </Box>
    );
};

TradingStatusIndicator.propTypes = {
    status: PropTypes.oneOf(['running', 'paused', 'stopped', 'error', 'warning', 'idle']),
    onAction: PropTypes.func,
    className: PropTypes.string
};

export default TradingStatusIndicator;
