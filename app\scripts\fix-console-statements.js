'use strict';

const fs = require('fs');
const path = require('path');

/**
 * <PERSON><PERSON><PERSON> to replace console statements with proper logger calls
 */

const logger = {
    // eslint-disable-next-line no-console
    info
=> // eslint-disable-next-line no-console
// eslint-disable-next-line no-console

// eslint-disable-next-line no-console


// eslint-disable-next-line no-console



console.log(`[INFO] ${msg}`),
    // eslint-disable-next-line no-console
    warn => // eslint-disable-next-line no-console
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log(`[WARN] ${msg}`),
    // eslint-disable-next-line no-console
    error => // eslint-disable-next-line no-console
        // eslint-disable-next-line no-console

        // eslint-disable-next-line no-console


        // eslint-disable-next-line no-console



        console.log(`[ERROR] ${msg}`)
}
;
const fg = require('fast-glob');

// Files to process
const filesToProcess = fg.sync(['main.js', 'preload.js', 'src/**/*.{js,jsx}', 'trading/**/*.{js,jsx}', '!**/__tests__/**', '!**/tests/**'], {
        cwd(__dirname, '..'),
    absolute
})
;

// Pattern replacements
const replacements = [
// Standard console replacements
    {
        pattern: /console\.log\(/g,
        replacement: 'logger.info('
    }, {
        pattern: /console\.info\(/g,
        replacement: 'logger.info('
    }, {
        pattern: /console\.warn\(/g,
        replacement: 'logger.warn('
    }, {
        pattern: /console\.error\(/g,
        replacement: 'logger.error('
    }, {
        pattern: /console\.debug\(/g,
        replacement: 'logger.debug('
    }, {
        pattern: /console\.trace\(/g,
        replacement: 'logger.trace('
    }];

// Logger import patterns for different file types
const loggerImports = {
    '.js': `// Import logger for consistent logging
const logger = (() => {
  try {
    return require('./utils/logger') || require('../utils/logger') || require('../../utils/logger');
  } catch (error) {
    return console; // Fallback to console if logger not available
  }
})();

`,
    '.jsx': `// Import logger for consistent logging
import logger from '../utils/logger';

`
};

function processFile(filePath) {
    const fullPath = path.join(__dirname, '..', filePath);
    if (!fs.existsSync(fullPath)) {
        logger.warn(`File not found: ${filePath}`);
        return false;
    }
    try {
        let content = fs.readFileSync(fullPath, 'utf8');
        const originalContent = content;
        let modified = false;

        // Check if file already has console statements
        const hasConsole = /console\.(log|info|warn|error|debug|trace)\s*\(/g.test(content);
        if (!hasConsole) {
            logger.info(`Skipping ${filePath} - no console statements found`);
            return false;
        }

        // Check if logger is already imported
        const hasLoggerImport = /import.*logger|require.*logger|const logger/i.test(content);

        // Add logger import if needed
        if (!hasLoggerImport) {
            const ext = path.extname(filePath);
            const importCode = loggerImports[ext] || loggerImports['.js'];

            // Find appropriate place to insert import
            if (ext === '.jsx' || ext === '.js') {
                // Insert after existing imports or at the beginning
                const importMatch = content.match(/^((?:\/\/.*\n|\/\*[\s\S]*?\*\/\n)*(?port.*\n|const.*require.*\n)*)/);
                if (importMatch) {
                    content = content.replace(importMatch[1], importMatch[1] + importCode);
                } else {
                    content = importCode + content;
                }
                modified = true;
            }
        }

        // Apply replacements
        replacements.forEach(({
                                  pattern,
                                  replacement
                              }) => {
            if (pattern.test(content)) {
                content = content.replace(pattern, replacement);
                modified = true;
            }
        });

        // Write back if modified
        if (modified && content !== originalContent) {
            fs.writeFileSync(fullPath, content, 'utf8');
            logger.info(`✓ Fixed console statements in ${filePath}`);
            return true;
        } else {
            logger.info(`- No changes needed for ${filePath}`);
            return false;
        }
    } catch (error) {
        logger.error(`Error processing ${filePath}: ${error.message}`);
        return false;
    }
}

function main() {
    logger.info('Starting console statement replacement...');
    let processed = 0;
    let modified = 0;
    filesToProcess.forEach(filePath => {
        processed++;
        if (processFile(filePath)) {
            modified++;
        }
    });
    logger.info('\nSummary:');
    logger.info(`- Files processed: ${processed}`);
    logger.info(`- Files modified: ${modified}`);
    logger.info(`- Files unchanged: ${processed - modified}`);
}

if (require.main === module) {
    main();
}
module.exports = {
    processFile,
    replacements
};