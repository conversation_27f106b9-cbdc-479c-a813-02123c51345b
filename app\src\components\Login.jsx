import {useState} from 'react';
import {useAuth} from '../auth/AuthContext';
import {useNavigate} from 'react-router-dom';

/**
 * Login component for the Trading Dashboard application.
 * Provides a form for users to input their username and password to log in.
 * Utilizes the `useAuth` hook for authentication and `useNavigate` hook for navigation.
 * Displays error messages if authentication fails and shows a loading state during the login process.
 * Upon successful login, navigates users to the dashboard.
 * Includes form fields for username and password, and a submit button to trigger the login process.
 */

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const {login} = useAuth();
  const navigate = useNavigate();

  /**
     * Handles form submission to log in to the app.
     * Calls the `login` function from the `useAuth` hook and navigates to the dashboard
     * if the login is successful, or sets an error message if the login fails.
     * @param {import('react').FormEvent<HTMLFormElement>} e - The form submission event
     */
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(username, password);
    if (result.success) {
      navigate('/dashboard');
    } else {
      setError(result.error || 'Login failed');
    }
    setLoading(false);
  };

  return (
    <div className="login-container">
      <div className="login-form">
        <h2>Trading Dashboard Login</h2>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label>Username</label>
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
              placeholder="admin"
            />
          </div>
          <div className="form-group">
            <label>Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="trading123"
            />
          </div>
          {error && <div className="error">{error}</div>}
          <button type="submit" disabled={loading}>
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Login;
