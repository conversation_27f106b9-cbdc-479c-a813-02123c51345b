import React from 'react';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import ApplicationErrorBoundary, {
  DashboardErrorBoundary,
  NetworkErrorBoundary,
  PortfolioErrorBoundary,
  TradingErrorBoundary
} from '../../components/ApplicationErrorBoundary';
import {ErrorReporter} from '../../services/ErrorReporter';

// Mock the ErrorReporter
jest.mock('../../services/ErrorReporter');

// Component that throws an error for testing
const ThrowError = ({shouldThrow = false, errorType = 'generic'}) => {
    if (shouldThrow) {
        if (errorType === 'network') {
            throw new Error('Network request failed');
        } else if (errorType === 'chunk') {
            throw new Error('Loading chunk 1 failed');
        } else {
            throw new Error('Test error');
        }
    }
    return <div>No error</div>;
};

describe('ApplicationErrorBoundary', () => {
    let mockErrorReporter;

    beforeEach(() => {
        mockErrorReporter = {
            report: jest.fn().mockResolvedValue({})
        };
        ErrorReporter.mockImplementation(() => mockErrorReporter);

        // Mock console.error to avoid noise in tests
        jest.spyOn(console, 'error').mockImplementation(() => {
        });
        jest.spyOn(console, 'warn').mockImplementation(() => {
        });
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    describe('Basic Error Boundary Functionality', () => {
        it('should render children when no error occurs', () => {
            render(
                <ApplicationErrorBoundary>
                    <ThrowError shouldThrow={false}/>
                </ApplicationErrorBoundary>,
            );

            expect(screen.getByText('No error')).toBeInTheDocument();
        });

        it('should catch and display error when child component throws', () => {
            render(
                <ApplicationErrorBoundary>
                    <ThrowError shouldThrow={true}/>
                </ApplicationErrorBoundary>,
            );

            expect(screen.getByText(/Test error/)).toBeInTheDocument();
        });

        it('should report error to ErrorReporter when error occurs', async () => {
            render(
                <ApplicationErrorBoundary componentName="TestComponent">
                    <ThrowError shouldThrow={true}/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(mockErrorReporter.report).toHaveBeenCalledWith(
                    expect.objectContaining({
                        type: 'application_error_boundary',
                        message: 'Test error',
                        componentName: 'TestComponent'
                    }),
                );
            });
        });
    });

    describe('Error Type Detection', () => {
        it('should detect network errors correctly', () => {
            render(
                <ApplicationErrorBoundary>
                    <ThrowError shouldThrow={true} errorType="network"/>
                </ApplicationErrorBoundary>,
            );

            expect(screen.getByText(/Network request failed/)).toBeInTheDocument();
        });

        it('should detect chunk loading errors correctly', () => {
            render(
                <ApplicationErrorBoundary>
                    <ThrowError shouldThrow={true} errorType="chunk"/>
                </ApplicationErrorBoundary>,
            );

            expect(screen.getByText(/Failed to load application resources/)).toBeInTheDocument();
        });
    });

    describe('Retry Functionality', () => {
        it('should allow retry when retry count is below maximum', async () => {
            const {rerender} = render(
                <ApplicationErrorBoundary>
                    <ThrowError shouldThrow={true}/>
                </ApplicationErrorBoundary>,
            );

            // Find and click retry button
            const retryButton = screen.getByText(/Try Again|Retry/i);
            expect(retryButton).toBeInTheDocument();

            // Mock successful retry
            fireEvent.click(retryButton);

            // Rerender with no error to simulate successful retry
            rerender(
                <ApplicationErrorBoundary>
                    <ThrowError shouldThrow={false}/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(screen.getByText('No error')).toBeInTheDocument();
            });
        });

        it('should disable retry after maximum attempts', () => {
            // This would require more complex state manipulation
            // For now, we'll test that the component handles max retries
            render(
                <ApplicationErrorBoundary>
                    <ThrowError shouldThrow={true}/>
                </ApplicationErrorBoundary>,
            );

            expect(screen.getByText(/Try Again|Retry/i)).toBeInTheDocument();
        });
    });

    describe('Custom Fallback Components', () => {
        const CustomFallback = ({error, onRetry}) => (
            <div>
                <div>Custom Error: {error.message}</div>
                <button onClick={onRetry}>Custom Retry</button>
            </div>
        );

        it('should render custom fallback component when provided', () => {
            render(
                <ApplicationErrorBoundary fallbackComponent={CustomFallback}>
                    <ThrowError shouldThrow={true}/>
                </ApplicationErrorBoundary>,
            );

            expect(screen.getByText('Custom Error: Test error')).toBeInTheDocument();
            expect(screen.getByText('Custom Retry')).toBeInTheDocument();
        });
    });

    describe('Specialized Error Boundaries', () => {
        it('should render DashboardErrorBoundary with correct component name', async () => {
            render(
                <DashboardErrorBoundary>
                    <ThrowError shouldThrow={true}/>
                </DashboardErrorBoundary>,
            );

            await waitFor(() => {
                expect(mockErrorReporter.report).toHaveBeenCalledWith(
                    expect.objectContaining({
                        componentName: 'Dashboard',
                        errorType: 'dashboard'
                    }),
                );
            });
        });

        it('should render TradingErrorBoundary with correct component name', async () => {
            render(
                <TradingErrorBoundary>
                    <ThrowError shouldThrow={true}/>
                </TradingErrorBoundary>,
            );

            await waitFor(() => {
                expect(mockErrorReporter.report).toHaveBeenCalledWith(
                    expect.objectContaining({
                        componentName: 'Trading',
                        errorType: 'trading'
                    }),
                );
            });
        });

        it('should render PortfolioErrorBoundary with correct component name', async () => {
            render(
                <PortfolioErrorBoundary>
                    <ThrowError shouldThrow={true}/>
                </PortfolioErrorBoundary>,
            );

            await waitFor(() => {
                expect(mockErrorReporter.report).toHaveBeenCalledWith(
                    expect.objectContaining({
                        componentName: 'Portfolio',
                        errorType: 'portfolio'
                    }),
                );
            });
        });

        it('should render NetworkErrorBoundary with correct component name', async () => {
            render(
                <NetworkErrorBoundary>
                    <ThrowError shouldThrow={true}/>
                </NetworkErrorBoundary>,
            );

            await waitFor(() => {
                expect(mockErrorReporter.report).toHaveBeenCalledWith(
                    expect.objectContaining({
                        componentName: 'Network',
                        errorType: 'network'
                    }),
                );
            });
        });
    });

    describe('Error Data Collection', () => {
        it('should collect comprehensive error data', async () => {
            render(
                <ApplicationErrorBoundary
                    componentName="TestComponent"
                    userId="test-user-123"
                >
                    <ThrowError shouldThrow={true}/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(mockErrorReporter.report).toHaveBeenCalledWith(
                    expect.objectContaining({
                        type: 'application_error_boundary',
                        message: 'Test error',
                        componentName: 'TestComponent',
                        userId: 'test-user-123',
                        url: expect.any(String),
                        userAgent: expect.any(String),
                        timestamp: expect.any(String),
                        sessionId: expect.any(String)
                    }),
                );
            });
        });

        it('should sanitize props before logging', async () => {
            const sensitiveProps = {
                password: 'secret123',
                apiKey: 'api-key-123',
                normalProp: 'normal-value'
            };

            render(
                <ApplicationErrorBoundary {...sensitiveProps}>
                    <ThrowError shouldThrow={true}/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                const reportCall = mockErrorReporter.report.mock.calls[0][0];
                expect(reportCall.props).toEqual({
                    componentName: undefined,
                    errorType: undefined,
                    userId: undefined,
                    password: 'secret123',
                    apiKey: 'api-key-123',
                    normalProp: 'normal-value'
                });
            });
        });
    });

    describe('Event Handlers', () => {
        it('should call custom onError handler when provided', async () => {
            const mockOnError = jest.fn();

            render(
                <ApplicationErrorBoundary onError={mockOnError}>
                    <ThrowError shouldThrow={true}/>
                </ApplicationErrorBoundary>,
            );

            await waitFor(() => {
                expect(mockOnError).toHaveBeenCalledWith(
                    expect.any(Error),
                    expect.any(Object),
                    expect.any(Object),
                );
            });
        });

        it('should call custom onRetry handler when provided', () => {
            const mockOnRetry = jest.fn();

            render(
                <ApplicationErrorBoundary onRetry={mockOnRetry}>
                    <ThrowError shouldThrow={true}/>
                </ApplicationErrorBoundary>,
            );

            const retryButton = screen.getByText(/Try Again|Retry/i);
            fireEvent.click(retryButton);

            expect(mockOnRetry).toHaveBeenCalledWith(1);
        });

        it('should call custom onGoHome handler when provided', () => {
            const mockOnGoHome = jest.fn();

            render(
                <ApplicationErrorBoundary onGoHome={mockOnGoHome}>
                    <ThrowError shouldThrow={true}/>
                </ApplicationErrorBoundary>,
            );

            // Look for home/dashboard button
            const homeButton = screen.getByText(/Go to Dashboard|Home/i);
            fireEvent.click(homeButton);

            expect(mockOnGoHome).toHaveBeenCalled();
        });
    });
});