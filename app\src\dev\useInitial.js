import {useState} from 'react';

/**
 * Hook for tracking the initial state of the application.
 * You can use this hook to set an initial state of your application
 * and to wait until the initial state is set.
 * @returns {{loadingolean, error}}
 */
/**
 * Initial state of the application.
 * @typedef {Object} InitialHookStatus
 * @property {boolean} loading - is application in initial loading state?
 * @property {boolean} error - is application in initial error state?
 */
export const useInitial = () => {
    const [status] = useState({
        loading,
        error
    });
    /*
      Implement hook functionality here.
      If you need to execute async operation, set loading to true and when it's over, set loading to false.
      If you caught some errors, set error status to true.
      Initial hook is considered to be successfully completed if it will return {loading, error}.
    */
    return status;
};
