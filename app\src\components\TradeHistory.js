import React from 'react';

const TradeHistory = ({trades}) => {
    return (
        <div className="trade-history">
            <h3>Trade History</h3>
            {trades.length === 0 ? (
                <p>No trades yet</p>
            ) : (
                <table>
                    <thead>
                    <tr>
                        <th>Date</th>
                        <th>Symbol</th>
                        <th>Side</th>
                        <th>Quantity</th>
                        <th>Price</th>
                    </tr>
                    </thead>
                    <tbody>
                    {trades.slice(0, 5).map((trade, index) => (
                        <tr key={index}>
                            <td>{new Date(trade.created_at).toLocaleDateString()}</td>
                            <td>{trade.symbol}</td>
                            <td>{trade.side}</td>
                            <td>{trade.quantity}</td>
                            <td>${trade.price?.toLocaleString() || 'Market'}</td>
                        </tr>
                    ))}
                    </tbody>
                </table>
            )}
        </div>
    );
};

export default TradeHistory;
