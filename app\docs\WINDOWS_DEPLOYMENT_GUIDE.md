# Windows Production Deployment Guide

## Overview

This guide provides detailed instructions for deploying the Meme Coin Trader application on Windows production environments.

## Prerequisites

### System Requirements

- **Operating System**: Windows 10 (1903) or Windows Server 2019 or later
- **Architecture**: x64 (64-bit)
- **RAM**: Minimum 8GB, Recommended 16GB
- **Storage**: Minimum 10GB free space
- **Network**: Stable internet connection

### Development Tools

```powershell
# Install Node.js (using Chocolatey)
choco install nodejs --version=18.19.0

# Install Python (required for native modules)
choco install python --version=3.11.7

# Install Visual Studio Build Tools
choco install visualstudio2022buildtools --package-parameters "--add Microsoft.VisualStudio.Workload.VCTools"

# Install Git
choco install git
```

### Alternative Installation (Manual)

1. **Node.js**: Download from [nodejs.org](https://nodejs.org/)
2. **Python**: Download from [python.org](https://python.org/)
3. **Visual Studio Build Tools**: Download from [Microsoft](https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022)
4. **Git**: Download from [git-scm.com](https://git-scm.com/)

## Build Environment Setup

### 1. Environment Variables

Create a PowerShell script to set up environment variables:

```powershell
# setup-env.ps1
$env:NODE_ENV = "production"
$env:ELECTRON_ENV = "production"
$env:npm_config_target_platform = "win32"
$env:npm_config_target_arch = "x64"
$env:npm_config_runtime = "electron"
$env:npm_config_build_from_source = "true"

# Visual Studio configuration
$env:GYP_MSVS_VERSION = "2022"
$env:npm_config_msvs_version = "2022"

# Python configuration
$env:npm_config_python = "python"

Write-Host "Environment variables set for Windows production build"
```

### 2. Native Module Compilation

```powershell
# Rebuild native modules for Electron
npm rebuild --runtime=electron --target=31.2.1 --disturl=https://electronjs.org/headers --build-from-source

# Specifically rebuild SQLite3
npm rebuild better-sqlite3 --runtime=electron --target=31.2.1 --disturl=https://electronjs.org/headers --build-from-source
```

## Production Build Process

### 1. Clone and Setup

```powershell
# Clone repository
git clone <repository-url>
cd electrontrader-monorepo

# Install dependencies
npm install

# Setup app dependencies
cd app
npm install

# Setup trading engine dependencies
cd trading
npm install
cd ..
```

### 2. Build Configuration

Create `electron-builder.config.js` for Windows:

```javascript
module.exports = {
  appId: "com.memetrader.app",
  productName: "Meme Coin Trader",
  directories: {
    output: "dist"
  },
  files: [
    "build/**/*",
    "trading/**/*",
    "main.js",
    "preload.js",
    "package.json",
    "!trading/node_modules/**/*",
    "!**/*.map"
  ],
  win: {
    target: [
      {
        target: "nsis",
        arch: ["x64"]
      },
      {
        target: "portable",
        arch: ["x64"]
      },
      {
        target: "msi",
        arch: ["x64"]
      }
    ],
    icon: "assets/icon.ico",
    publisherName: "Meme Coin Trader Inc.",
    verifyUpdateCodeSignature: false,
    requestedExecutionLevel: "asInvoker"
  },
  nsis: {
    oneClick: false,
    allowToChangeInstallationDirectory: true,
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: "Meme Coin Trader",
    include: "installer.nsh"
  },
  msi: {
    createDesktopShortcut: true,
    createStartMenuShortcut: true
  },
  portable: {
    artifactName: "${productName}-${version}-portable.${ext}"
  }
};
```

### 3. Build Commands

```powershell
# Build React application
npm run build:production

# Package for Windows
npm run package:win

# Build all Windows formats
npm run dist:win
```

### 4. Advanced Build Script

Create `build-windows.ps1`:

```powershell
# build-windows.ps1
param(
    [string]$Version = "1.0.0",
    [switch]$Sign = $false,
    [switch]$Publish = $false
)

Write-Host "Building Meme Coin Trader for Windows v$Version" -ForegroundColor Green

# Set environment
$env:NODE_ENV = "production"
$env:ELECTRON_ENV = "production"

try {
    # Clean previous builds
    Write-Host "Cleaning previous builds..." -ForegroundColor Yellow
    Remove-Item -Path "dist" -Recurse -Force -ErrorAction SilentlyContinue
    Remove-Item -Path "build" -Recurse -Force -ErrorAction SilentlyContinue

    # Install dependencies
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm ci --production=false

    # Build React app
    Write-Host "Building React application..." -ForegroundColor Yellow
    npm run build:production

    # Rebuild native modules
    Write-Host "Rebuilding native modules..." -ForegroundColor Yellow
    npm rebuild --runtime=electron --target=31.2.1 --disturl=https://electronjs.org/headers --build-from-source

    # Package application
    Write-Host "Packaging Electron application..." -ForegroundColor Yellow
    if ($Sign) {
        npm run dist:win:signed
    } else {
        npm run dist:win
    }

    # Verify build
    Write-Host "Verifying build..." -ForegroundColor Yellow
    $distFiles = Get-ChildItem -Path "dist" -Filter "*.exe", "*.msi"
    if ($distFiles.Count -eq 0) {
        throw "No installer files found in dist directory"
    }

    Write-Host "Build completed successfully!" -ForegroundColor Green
    Write-Host "Output files:" -ForegroundColor Cyan
    $distFiles | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor Cyan }

    # Publish if requested
    if ($Publish) {
        Write-Host "Publishing build..." -ForegroundColor Yellow
        npm run publish:win
    }

} catch {
    Write-Host "Build failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
```

## Code Signing (Optional but Recommended)

### 1. Obtain Code Signing Certificate

- Purchase from a trusted CA (DigiCert, Sectigo, etc.)
- Or use self-signed certificate for internal distribution

### 2. Configure Code Signing

```powershell
# Set certificate environment variables
$env:CSC_LINK = "path\to\certificate.p12"
$env:CSC_KEY_PASSWORD = "certificate_password"

# Alternative: Use Windows Certificate Store
$env:CSC_LINK = "Subject Name of Certificate"
```

### 3. Sign Build

```powershell
# Build with signing
npm run dist:win:signed
```

## Installation and Deployment

### 1. NSIS Installer Deployment

The NSIS installer provides:
- Custom installation directory selection
- Desktop and Start Menu shortcuts
- Automatic updates support
- Uninstaller creation

```powershell
# Run installer silently
MemeTrader-Setup-1.0.0.exe /S /D=C:\Program Files\Meme Coin Trader
```

### 2. MSI Installer Deployment

For enterprise environments:

```powershell
# Install via MSI
msiexec /i "MemeTrader-1.0.0.msi" /quiet /norestart

# Install to custom location
msiexec /i "MemeTrader-1.0.0.msi" /quiet INSTALLDIR="C:\Apps\MemeTrader"

# Uninstall
msiexec /x "MemeTrader-1.0.0.msi" /quiet
```

### 3. Portable Deployment

```powershell
# Extract portable version
Expand-Archive -Path "MemeTrader-1.0.0-portable.zip" -DestinationPath "C:\Apps\MemeTrader"

# Run portable version
cd "C:\Apps\MemeTrader"
.\MemeTrader.exe
```

## Windows Service Installation

### 1. Create Service Wrapper

Create `windows-service.js`:

```javascript
const Service = require('node-windows').Service;
const path = require('path');

// Create a new service object
const svc = new Service({
  name: 'Meme Coin Trader',
  description: 'Autonomous cryptocurrency trading application',
  script: path.join(__dirname, 'main.js'),
  nodeOptions: [
    '--harmony',
    '--max_old_space_size=4096'
  ],
  env: {
    name: "NODE_ENV",
    value: "production"
  }
});

// Listen for the "install" event
svc.on('install', function() {
  console.log('Service installed successfully');
  svc.start();
});

// Listen for the "start" event
svc.on('start', function() {
  console.log('Service started successfully');
});

// Install the service
svc.install();
```

### 2. Install as Windows Service

```powershell
# Install node-windows
npm install -g node-windows

# Install service
node windows-service.js

# Start service
net start "Meme Coin Trader"

# Stop service
net stop "Meme Coin Trader"
```

## Configuration Management

### 1. Windows Registry Configuration

```powershell
# Create registry entries for configuration
New-Item -Path "HKLM:\SOFTWARE\MemeTrader" -Force
Set-ItemProperty -Path "HKLM:\SOFTWARE\MemeTrader" -Name "InstallPath" -Value "C:\Program Files\Meme Coin Trader"
Set-ItemProperty -Path "HKLM:\SOFTWARE\MemeTrader" -Name "Version" -Value "1.0.0"
Set-ItemProperty -Path "HKLM:\SOFTWARE\MemeTrader" -Name "Environment" -Value "production"
```

### 2. Windows Environment Variables

```powershell
# Set system-wide environment variables
[Environment]::SetEnvironmentVariable("MEME_TRADER_HOME", "C:\Program Files\Meme Coin Trader", "Machine")
[Environment]::SetEnvironmentVariable("MEME_TRADER_ENV", "production", "Machine")
[Environment]::SetEnvironmentVariable("MEME_TRADER_LOG_LEVEL", "info", "Machine")
```

### 3. Configuration File Locations

- **System Configuration**: `C:\ProgramData\MemeTrader\config`
- **User Configuration**: `%APPDATA%\MemeTrader\config`
- **Logs**: `%APPDATA%\MemeTrader\logs`
- **Database**: `%APPDATA%\MemeTrader\databases`

## Performance Optimization

### 1. Windows-Specific Optimizations

```javascript
// Windows performance configuration
const windowsConfig = {
  process: {
    priority: 'high', // Set process priority
    affinity: 0xFF,   // Use all CPU cores
  },
  memory: {
    maxOldSpaceSize: 4096, // 4GB heap limit
    maxSemiSpaceSize: 256,  // 256MB semi-space
  },
  io: {
    useAsyncIO: true,
    bufferSize: 65536,
  }
};

// Apply Windows-specific optimizations
if (process.platform === 'win32') {
  process.env.UV_THREADPOOL_SIZE = '16';
  process.env.NODE_OPTIONS = '--max-old-space-size=4096';
}
```

### 2. Windows Defender Exclusions

Add exclusions to Windows Defender:

```powershell
# Add folder exclusions
Add-MpPreference -ExclusionPath "C:\Program Files\Meme Coin Trader"
Add-MpPreference -ExclusionPath "$env:APPDATA\MemeTrader"

# Add process exclusions
Add-MpPreference -ExclusionProcess "MemeTrader.exe"
Add-MpPreference -ExclusionProcess "node.exe"
```

## Monitoring and Logging

### 1. Windows Event Log Integration

```javascript
// Windows Event Log integration
const EventLogger = require('node-windows').EventLogger;

class WindowsEventLogger {
  constructor() {
    this.log = new EventLogger('Meme Coin Trader');
  }

  logInfo(message) {
    this.log.info(message);
  }

  logWarning(message) {
    this.log.warn(message);
  }

  logError(message) {
    this.log.error(message);
  }
}

module.exports = WindowsEventLogger;
```

### 2. Performance Counters

```javascript
// Windows Performance Counters
const perfmon = require('perfmon');

class WindowsPerformanceMonitor {
  constructor() {
    this.counters = [
      '\\Process(MemeTrader)\\% Processor Time',
      '\\Process(MemeTrader)\\Working Set',
      '\\Process(MemeTrader)\\Private Bytes'
    ];
  }

  async getMetrics() {
    return new Promise((resolve, reject) => {
      perfmon(this.counters, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
  }
}
```

## Security Configuration

### 1. Windows Firewall Rules

```powershell
# Allow application through Windows Firewall
New-NetFirewallRule -DisplayName "Meme Coin Trader" -Direction Inbound -Program "C:\Program Files\Meme Coin Trader\MemeTrader.exe" -Action Allow
New-NetFirewallRule -DisplayName "Meme Coin Trader" -Direction Outbound -Program "C:\Program Files\Meme Coin Trader\MemeTrader.exe" -Action Allow

# Allow specific ports
New-NetFirewallRule -DisplayName "Meme Trader API" -Direction Inbound -Protocol TCP -LocalPort 3001 -Action Allow
```

### 2. User Account Control (UAC)

Configure UAC settings in the manifest:

```xml
<!-- app.manifest -->
<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<assembly xmlns="urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">
  <trustInfo xmlns="urn:schemas-microsoft-com:asm.v3">
    <security>
      <requestedPrivileges>
        <requestedExecutionLevel level="asInvoker" uiAccess="false"/>
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>
```

## Troubleshooting

### Common Issues

#### 1. Native Module Compilation Errors

```powershell
# Clear npm cache
npm cache clean --force

# Rebuild with verbose logging
npm rebuild --verbose --runtime=electron --target=31.2.1 --disturl=https://electronjs.org/headers --build-from-source

# Install windows-build-tools globally
npm install -g windows-build-tools
```

#### 2. Permission Errors

```powershell
# Run as Administrator
Start-Process PowerShell -Verb RunAs

# Fix npm permissions
npm config set cache C:\npm-cache --global
npm config set prefix C:\npm-global --global
```

#### 3. Antivirus Interference

```powershell
# Temporarily disable real-time protection
Set-MpPreference -DisableRealtimeMonitoring $true

# Re-enable after build
Set-MpPreference -DisableRealtimeMonitoring $false
```

### Diagnostic Tools

#### 1. System Information Script

```powershell
# system-info.ps1
Write-Host "=== System Information ===" -ForegroundColor Green
Write-Host "OS: $(Get-WmiObject -Class Win32_OperatingSystem | Select-Object -ExpandProperty Caption)"
Write-Host "Architecture: $env:PROCESSOR_ARCHITECTURE"
Write-Host "RAM: $([math]::Round((Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory / 1GB, 2)) GB"
Write-Host "Node.js: $(node --version)"
Write-Host "npm: $(npm --version)"
Write-Host "Python: $(python --version 2>&1)"

Write-Host "`n=== Environment Variables ===" -ForegroundColor Green
Get-ChildItem Env: | Where-Object { $_.Name -like "*NODE*" -or $_.Name -like "*npm*" -or $_.Name -like "*PYTHON*" } | Format-Table Name, Value

Write-Host "`n=== Installed Visual Studio Components ===" -ForegroundColor Green
Get-ChildItem "HKLM:\SOFTWARE\Microsoft\VisualStudio" -ErrorAction SilentlyContinue | ForEach-Object {
    $version = $_.PSChildName
    Write-Host "Visual Studio $version"
}
```

#### 2. Build Verification Script

```powershell
# verify-build.ps1
param([string]$BuildPath = "dist")

Write-Host "Verifying build in $BuildPath..." -ForegroundColor Green

# Check if build directory exists
if (-not (Test-Path $BuildPath)) {
    Write-Host "Build directory not found!" -ForegroundColor Red
    exit 1
}

# Check for installer files
$installers = Get-ChildItem -Path $BuildPath -Filter "*.exe", "*.msi"
if ($installers.Count -eq 0) {
    Write-Host "No installer files found!" -ForegroundColor Red
    exit 1
}

Write-Host "Found installers:" -ForegroundColor Cyan
$installers | ForEach-Object { 
    Write-Host "  - $($_.Name) ($([math]::Round($_.Length / 1MB, 2)) MB)" -ForegroundColor Cyan
}

# Verify file signatures (if signed)
foreach ($installer in $installers) {
    $signature = Get-AuthenticodeSignature $installer.FullName
    if ($signature.Status -eq "Valid") {
        Write-Host "✓ $($installer.Name) is properly signed" -ForegroundColor Green
    } elseif ($signature.Status -eq "NotSigned") {
        Write-Host "⚠ $($installer.Name) is not signed" -ForegroundColor Yellow
    } else {
        Write-Host "✗ $($installer.Name) has invalid signature" -ForegroundColor Red
    }
}

Write-Host "Build verification completed!" -ForegroundColor Green
```

## Automated Deployment

### 1. PowerShell Deployment Script

```powershell
# deploy-windows.ps1
param(
    [Parameter(Mandatory=$true)]
    [string]$Version,
    
    [string]$TargetPath = "C:\Program Files\Meme Coin Trader",
    [switch]$CreateService,
    [switch]$StartService
)

Write-Host "Deploying Meme Coin Trader v$Version to Windows" -ForegroundColor Green

try {
    # Stop existing service if running
    if (Get-Service "Meme Coin Trader" -ErrorAction SilentlyContinue) {
        Write-Host "Stopping existing service..." -ForegroundColor Yellow
        Stop-Service "Meme Coin Trader" -Force
    }

    # Download installer
    $installerUrl = "https://releases.example.com/MemeTrader-Setup-$Version.exe"
    $installerPath = "$env:TEMP\MemeTrader-Setup-$Version.exe"
    
    Write-Host "Downloading installer..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $installerUrl -OutFile $installerPath

    # Verify installer
    if (-not (Test-Path $installerPath)) {
        throw "Failed to download installer"
    }

    # Install application
    Write-Host "Installing application..." -ForegroundColor Yellow
    Start-Process -FilePath $installerPath -ArgumentList "/S", "/D=$TargetPath" -Wait

    # Verify installation
    if (-not (Test-Path "$TargetPath\MemeTrader.exe")) {
        throw "Installation failed - executable not found"
    }

    # Create Windows service if requested
    if ($CreateService) {
        Write-Host "Creating Windows service..." -ForegroundColor Yellow
        # Service creation logic here
    }

    # Start service if requested
    if ($StartService -and (Get-Service "Meme Coin Trader" -ErrorAction SilentlyContinue)) {
        Write-Host "Starting service..." -ForegroundColor Yellow
        Start-Service "Meme Coin Trader"
    }

    Write-Host "Deployment completed successfully!" -ForegroundColor Green

} catch {
    Write-Host "Deployment failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
} finally {
    # Cleanup
    if (Test-Path $installerPath) {
        Remove-Item $installerPath -Force
    }
}
```

### 2. Group Policy Deployment

For enterprise environments, create an MSI package and deploy via Group Policy:

1. Create GPO for software installation
2. Add MSI package to Computer Configuration > Software Installation
3. Configure deployment options (assigned vs. published)
4. Set installation properties and transforms if needed

## Maintenance

### 1. Update Script

```powershell
# update-windows.ps1
param([string]$NewVersion)

Write-Host "Updating Meme Coin Trader to version $NewVersion" -ForegroundColor Green

# Backup current installation
$backupPath = "$env:TEMP\MemeTrader-Backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
Copy-Item -Path "C:\Program Files\Meme Coin Trader" -Destination $backupPath -Recurse

# Download and install update
# ... update logic here ...

Write-Host "Update completed successfully!" -ForegroundColor Green
Write-Host "Backup created at: $backupPath" -ForegroundColor Cyan
```

### 2. Health Check Script

```powershell
# health-check-windows.ps1
Write-Host "Performing health check..." -ForegroundColor Green

# Check if application is running
$process = Get-Process "MemeTrader" -ErrorAction SilentlyContinue
if ($process) {
    Write-Host "✓ Application is running (PID: $($process.Id))" -ForegroundColor Green
} else {
    Write-Host "✗ Application is not running" -ForegroundColor Red
}

# Check service status
$service = Get-Service "Meme Coin Trader" -ErrorAction SilentlyContinue
if ($service) {
    Write-Host "✓ Service status: $($service.Status)" -ForegroundColor Green
} else {
    Write-Host "⚠ Service not installed" -ForegroundColor Yellow
}

# Check API endpoint
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/health" -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ API endpoint responding" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ API endpoint not responding" -ForegroundColor Red
}

# Check log files
$logPath = "$env:APPDATA\MemeTrader\logs"
if (Test-Path $logPath) {
    $logFiles = Get-ChildItem -Path $logPath -Filter "*.log"
    Write-Host "✓ Found $($logFiles.Count) log files" -ForegroundColor Green
} else {
    Write-Host "⚠ Log directory not found" -ForegroundColor Yellow
}

Write-Host "Health check completed!" -ForegroundColor Green
```

---

*This Windows deployment guide is part of the comprehensive production deployment documentation. For other platforms, see the macOS and Linux deployment guides.*