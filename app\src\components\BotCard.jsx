/**
 * @fileoverview Bot Card Component
 * @description Displays individual trading bot information with status, performance metrics,
 * and control actions. Part of the trading dashboard interface.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */

import React from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Button,
  Card,
  CardActions,
  CardContent,
  Chip,
  Grid,
  IconButton,
  LinearProgress,
  Tooltip,
  Typography
} from '@mui/material';
import {PlayArrow, Settings, ShowChart, Stop, TrendingDown, TrendingUp} from '@mui/icons-material';
import {motion} from 'framer-motion';
// Import UI constants
import {CSS_VALUES, THEME_COLORS} from '../constants/ui-constants';

/**
 * BotCard - Individual Trading Bot Display Component
 *
 * @description Renders a card showing trading bot information including status,
 * performance metrics, profit/loss data, and control buttons for start/stop/configure.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Object} props.bot - Bot configuration and data object
 * @param {string} props.bot.id - Unique bot identifier
 * @param {string} props.bot.name - Display name for the bot
 * @param {string} props.bot.type - Bot type (grid, dca, futures, etc.)
 * @param {string} props.bot.status - Current status (active, stopped, error)
 * @param {string} props.bot.symbol - Trading pair symbol
 * @param {number} props.bot.profit - Total profit/loss amount
 * @param {number} props.bot.profitPercent - Profit percentage
 * @param {number} props.bot.totalTrades - Number of completed trades
 * @param {number} props.bot.successRate - Success rate percentage
 * @param {Function} props.onStart - Callback when start button is clicked
 * @param {Function} props.onStop - Callback when stop button is clicked
 * @param {Function} props.onConfigure - Callback when settings button is clicked
 * @param {Function} [props.onViewChart] - Optional callback for viewing bot chart
 *
 * @returns {React.ReactElement} The rendered bot card component
 *
 * @example
 * // Basic usage
 * <BotCard
 *   bot={{
 *     id: 'grid_bot_1',
 *     name: 'BTC Grid Bot',
 *     type: 'grid',
 *     status: 'active',
 *     symbol: 'BTC/USDT',
 *     profit: 125.50,
 *     profitPercent: 2.5,
 *     totalTrades: 42,
 *     successRate: 78.5
 *   }}
 *   onStart={(bot) => handleStartBot(bot)}
 *   onStop={(bot) => handleStopBot(bot)}
 *   onConfigure={(bot) => openBotSettings(bot)}
 * />
 *
 * @example
 * // With chart viewing capability
 * <BotCard
 *   bot={botData}
 *   onStart={startHandler}
 *   onStop={stopHandler}
 *   onConfigure={configHandler}
 *   onViewChart={(bot) => openChartModal(bot)}
 * />
 */
const BotCard = ({
                     bot,
                     onStart,
                     onStop,
                     onConfigure,
                     onViewChart
                 }) => {
    const getStatusColor = (status) => {
        switch (status.toLowerCase()) {
            case 'active':
                return 'success';
            case 'stopped':
                return 'default';
            case 'error':
                return 'error';
            case 'paused':
                return 'warning';
            default:
                return 'default';
        }
    };

    const getStatusIcon = (status) => {
        switch (status.toLowerCase()) {
            case 'active':
                return <PlayArrow/>;
            case 'stopped':
                return <Stop/>;
            default:
                return null;
        }
    };

    const getProfitColor = (profit) => {
        if (profit > 0) return 'success.main';
        if (profit < 0) return 'error.main';
        return 'text.primary';
    };

    const getProfitIcon = (profit) => {
        if (profit > 0) return <TrendingUp color="success"/>;
        if (profit < 0) return <TrendingDown color="error"/>;
        return null;
    };

    return (
        <motion.div
            initial={{opacity: 0, y: 20}}
            animate={{opacity: 1, y: 0}}
            transition={{duration: 0.3}}
        >
            <Card
                sx={{
                    background: `linear-gradient(135deg, ${THEME_COLORS.PRIMARY}1A, ${THEME_COLORS.SECONDARY}1A)`,
                    border: `1px solid ${THEME_COLORS.PRIMARY}33`,
                    backdropFilter: 'blur(10px)',
                    height: '100%',
                    display: CSS_VALUES.DISPLAY_FLEX,
                    flexDirection: 'column'
                }}
            >
                <CardContent sx={{flexGrow: 1}}>
                    {/* Header with bot name and status */}
                    <Box display={CSS_VALUES.DISPLAY_FLEX} justifyContent={CSS_VALUES.JUSTIFY_SPACE_BETWEEN}
                         alignItems={CSS_VALUES.ALIGN_CENTER} mb={2}>
                        <Typography variant="h6" component="h3" noWrap>
                            {bot.name}
                        </Typography>
                        <Chip
                            icon={getStatusIcon(bot.status)}
                            label={bot.status.toUpperCase()}
                            color={getStatusColor(bot.status)}
                            size="small"
                        />
                    </Box>

                    {/* Bot type and symbol */}
                    <Box display={CSS_VALUES.DISPLAY_FLEX} justifyContent={CSS_VALUES.JUSTIFY_SPACE_BETWEEN}
                         alignItems={CSS_VALUES.ALIGN_CENTER} mb={2}>
                        <Typography variant="body2" color="text.secondary">
                            {bot.type.toUpperCase()} Bot
                        </Typography>
                        <Typography variant="body2" fontWeight="bold">
                            {bot.symbol}
                        </Typography>
                    </Box>

                    {/* Profit/Loss display */}
                    <Box display={CSS_VALUES.DISPLAY_FLEX} alignItems={CSS_VALUES.ALIGN_CENTER}
                         justifyContent={CSS_VALUES.JUSTIFY_SPACE_BETWEEN} mb={2}>
                        <Box display={CSS_VALUES.DISPLAY_FLEX} alignItems={CSS_VALUES.ALIGN_CENTER} gap={1}>
                            {getProfitIcon(bot.profit)}
                            <Typography
                                variant="h6"
                                color={getProfitColor(bot.profit)}
                                fontWeight="bold"
                            >
                                ${Math.abs(bot.profit).toFixed(2)}
                            </Typography>
                        </Box>
                        <Typography
                            variant="body2"
                            color={getProfitColor(bot.profit)}
                            fontWeight="bold"
                        >
                            {bot.profit >= 0 ? '+' : ''}{bot.profitPercent.toFixed(2)}%
                        </Typography>
                    </Box>

                    {/* Trading statistics */}
                    <Grid container spacing={1} mb={2}>
                        <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">
                                Total Trades
                            </Typography>
                            <Typography variant="body1" fontWeight="bold">
                                {bot.totalTrades || 0}
                            </Typography>
                        </Grid>
                        <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">
                                Success Rate
                            </Typography>
                            <Typography variant="body1" fontWeight="bold">
                                {(bot.successRate || 0).toFixed(1)}%
                            </Typography>
                        </Grid>
                    </Grid>

                    {/* Success rate progress bar */}
                    <Box mb={2}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                            Performance
                        </Typography>
                        <LinearProgress
                            variant="determinate"
                            value={bot.successRate || 0}
                            sx={{
                                height: 6,
                                borderRadius: 3,
                                backgroundColor: 'rgba(255,255,255,0.1)',
                                '& .MuiLinearProgress-bar': {
                                    borderRadius: 3,
                                    background: bot.successRate >= 70
                                        ? `linear-gradient(90deg, ${THEME_COLORS.SUCCESS}, #8bc34a)`
                                        : bot.successRate >= 50
                                            ? `linear-gradient(90deg, #ff9800, ${THEME_COLORS.WARNING})`
                                            : `linear-gradient(90deg, ${THEME_COLORS.ERROR}, #e57373)`
                                }
                            }}
                        />
                    </Box>
                </CardContent>

                {/* Action buttons */}
                <CardActions sx={{justifyContent: 'space-between', px: 2, pb: 2}}>
                    <Box>
                        {bot.status === 'active' ? (
                            <Button
                                variant="outlined"
                                color="error"
                                size="small"
                                startIcon={<Stop/>}
                                onClick={() => onStop(bot)}
                            >
                                Stop
                            </Button>
                        ) : (
                            <Button
                                variant="outlined"
                                color="success"
                                size="small"
                                startIcon={<PlayArrow/>}
                                onClick={() => onStart(bot)}
                            >
                                Start
                            </Button>
                        )}
                    </Box>

                    <Box display={CSS_VALUES.DISPLAY_FLEX} gap={1}>
                        {onViewChart && (
                            <Tooltip title="View Chart">
                                <IconButton
                                    size="small"
                                    onClick={() => onViewChart(bot)}
                                    sx={{color: 'primary.main'}}
                                >
                                    <ShowChart/>
                                </IconButton>
                            </Tooltip>
                        )}
                        <Tooltip title="Settings">
                            <IconButton
                                size="small"
                                onClick={() => onConfigure(bot)}
                                sx={{color: 'primary.main'}}
                            >
                                <Settings/>
                            </IconButton>
                        </Tooltip>
                    </Box>
                </CardActions>
            </Card>
        </motion.div>
    );
};

BotCard.propTypes = {
    bot: PropTypes.shape({
        id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        type: PropTypes.string.isRequired,
        status: PropTypes.string.isRequired,
        symbol: PropTypes.string.isRequired,
        profit: PropTypes.number.isRequired,
        profitPercent: PropTypes.number.isRequired,
        totalTrades: PropTypes.number,
        successRate: PropTypes.number
    }).isRequired,
    onStart: PropTypes.func.isRequired,
    onStop: PropTypes.func.isRequired,
    onConfigure: PropTypes.func.isRequired,
    onViewChart: PropTypes.func
};

export default BotCard;
