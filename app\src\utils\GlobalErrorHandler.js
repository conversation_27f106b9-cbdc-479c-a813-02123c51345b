const logger = require('./logger.js');

/**
 * GlobalErrorHandler - Handles global application errors
 *
 * Sets up listeners for:
 * - Unhandled promise rejections
 * - Global JavaScript errors
 * - Resource loading errors
 */
class GlobalErrorHandler {
  constructor() {
    this.isInitialized = false;
  }

  initialize() {
    if (this.isInitialized) {
      logger.warn('GlobalErrorHandler already initialized');
      return;
    }

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection);

    // Handle global JavaScript errors
    window.addEventListener('error', this.handleGlobalError);

    // Handle resource loading errors
    window.addEventListener('error', this.handleResourceError, true);

    // Handle beforeunload to save any pending errors
    window.addEventListener('beforeunload', this.handleBeforeUnload);

    this.isInitialized = true;
    logger.info('GlobalErrorHandler initialized');
  }

  handleUnhandledRejection = (event) => {
    logger.error('Unhandled promise rejection:', event.reason);

    const error = event.reason || new Error('Unknown promise rejection');
    const errorData = {
      type: 'unhandled_promise_rejection',
      message: error.message || String(event.reason),
      stack: error.stack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
    };

    // Error reporting would be implemented here

    // Prevent the default browser behavior (logging to console)
    event.preventDefault();
  };

  handleGlobalError = (event) => {
    // Skip if this is a resource loading error (handled separately)
    if (event.target !== window && event.target instanceof Element) {
      return;
    }

    logger.error('Global JavaScript error:', event.error);

    const errorData = {
      type: 'global_javascript_error',
      message: event.message,
      stack: event.error ? event.error.stack : null,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
    };

    // Error reporting would be implemented here
  };

  handleResourceError = (event) => {
    // Only handle resource loading errors
    if (!(event.target instanceof Element)) {
      return;
    }

    const target = event.target;
    const resourceType = target.tagName ? target.tagName.toLowerCase() : 'unknown';
    const resourceUrl = target.src || target.href || 'unknown';

    // Ignore certain trivial errors
    if (resourceUrl.includes('favicon.ico')) {
      return;
    }

    logger.error(`Resource loading error: ${resourceType} ${resourceUrl}`);

    const errorData = {
      type: 'resource_loading_error',
      message: `Failed to load ${resourceType}: ${resourceUrl}`,
      resourceType,
      resourceUrl,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
    };

    // Error reporting would be implemented here
  };

  handleBeforeUnload = () => {
    // Try to retry any failed error reports before the page unloads
    try {
      // Error retry logic would be implemented here
      logger.info('Attempting to save pending errors before unload');
    } catch (error) {
      logger.warn('Failed to retry error reports on unload:', error);
    }
  };

  // Method to manually report errors
  reportError(error, context = {}) {
    const errorData = {
      type: 'manual_error_report',
      message: error.message,
      stack: error.stack,
      context,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
    };

    logger.error('Manual error report:', errorData);
    // Error reporting implementation would go here
    return Promise.resolve();
  }

  // Method to get error statistics
  getErrorStats() {
    // Error statistics implementation would go here
    return {
      totalErrors: 0,
      errorsByType: {},
      timestamp: new Date().toISOString(),
    };
  }

  // Method to clear stored errors
  clearErrors() {
    // Error clearing implementation would go here
    logger.info('Cleared stored errors');
  }

  // Cleanup method
  destroy() {
    if (!this.isInitialized) {
      return;
    }

    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
    window.removeEventListener('error', this.handleGlobalError);
    window.removeEventListener('error', this.handleResourceError, true);
    window.removeEventListener('beforeunload', this.handleBeforeUnload);

    this.isInitialized = false;
    logger.info('GlobalErrorHandler destroyed');
  }
}

// Create singleton instance
const globalErrorHandler = new GlobalErrorHandler();

module.exports = globalErrorHandler;