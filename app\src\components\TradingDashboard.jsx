import React, {useEffect, useState} from 'react';
import {useAuth} from '../auth/AuthContext';
import PortfolioSummary from './PortfolioSummary';
import PositionManager from './PositionManager';
import TradeHistory from './TradeHistory';
import logger from '../utils/logger';

const TradingDashboard = () => {
    const {user, logout} = useAuth();
    const [portfolio, setPortfolio] = useState({positions: []});
    const [trades, setTrades] = useState([]);
    const [botStatus, setBotStatus] = useState('stopped');
    const [loading, setLoading] = useState(true);

    const fetchData = async () => {
        try {
            const [portfolioRes, tradesRes, statusRes] = await Promise.all([
                window.electronAPI.getPortfolioSummary(),
                window.electronAPI.getTradeHistory(),
                window.electronAPI.getBotStatus()]);

            if (portfolioRes.success) setPortfolio(portfolioRes.data);
            if (tradesRes.success) setTrades(tradesRes.data);
            if (statusRes.success) setBotStatus(statusRes.data);
            setLoading(false);
        } catch (error) {
            logger.error('Error fetching data:', error);
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 5000);
        return () => clearInterval(interval);
    }, []);

    const handleStartBot = async () => {
        try {
            const result = await window.electronAPI.startBot();
            if (result.success) {
                setBotStatus('running');
            } else {
                logger.error('Failed to start bot:', result.error);
            }
        } catch (error) {
            logger.error('Error starting bot:', error);
        }
    };

    const handleStopBot = async () => {
        try {
            const result = await window.electronAPI.stopBot();
            if (result.success) {
                setBotStatus('stopped');
            } else {
                logger.error('Failed to stop bot:', result.error);
            }
        } catch (error) {
            logger.error('Error stopping bot:', error);
        }
    };

    if (loading) {
        return (
            <div className="dashboard">
                <header>
                    <h1>Trading Dashboard</h1>
                    <div className="user-info">
                        <span>Welcome, {user?.username}</span>
                        <button onClick={logout}>Logout</button>
                    </div>
                </header>
                <div className="loading-container">
                    <div className="loading-spinner">Loading trading data...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="dashboard">
            <header>
                <h1>Trading Dashboard</h1>
                <div className="user-info">
                    <span>Welcome, {user?.username}</span>
                    <button onClick={logout}>Logout</button>
                </div>
                <div className="bot-controls">
                    <button onClick={handleStartBot} disabled={botStatus === 'running'}>
                        Start Bot
                    </button>
                    <button onClick={handleStopBot} disabled={botStatus !== 'running'}>
                        Stop Bot
                    </button>
                    <span className={`status ${botStatus}`}>{botStatus}</span>
                </div>
            </header>
            <div className="dashboard-content">
                <div className="left-panel">
                    <PortfolioSummary positions={portfolio.positions}/>
                </div>
                <div className="right-panel">
                    <PositionManager positions={portfolio.positions}/>
                    <TradeHistory trades={trades}/>
                </div>
            </div>
        </div>
    );
};

export default TradingDashboard;
