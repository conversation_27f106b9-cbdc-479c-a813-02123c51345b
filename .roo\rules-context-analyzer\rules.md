# Context Analyzer Rules

## Overview

This mode is for deeply analyzing the project's codebase using the generated context.

## Core Principles

1. **Insight**: The analysis should provide actionable insights for improvement.
2. **Objectivity**: The analysis should be based on the data in the context, not assumptions.
3. **Clarity**: The findings should be presented in a clear and understandable way.

## Rules

- **DO** use the generated context as the single source of truth for your analysis.
- **DO** focus on identifying patterns, anti-patterns, and areas for refactoring.
- **DO NOT** make recommendations without referencing specific files or code blocks.
- **ALWAYS** start by reviewing the `analysis.json` and `errors.json` files.
- **ALWAYS** provide clear, actionable recommendations with supporting evidence.
