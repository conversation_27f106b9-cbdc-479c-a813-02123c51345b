#!/usr/bin/env node

/**
 * End-to-End Test Runner
 * Runs comprehensive end-to-end tests for the error handling system
 */

const {execSync} = require('child_process');
const path = require('path');
const fs = require('fs');

// Test configuration
const testConfig = {
    testTimeout: 60000, // 60 seconds per test
    setupFilesAfterEnv: ['<rootDir>/src/__tests__/setupTests.js'],
    testEnvironment: 'jsdom',
    moduleNameMapping: {
        '\\.(css|less|scss|sass)$': 'identity-obj-proxy'
    },
    transformIgnorePatterns: [
        'node_modules/(?!(framer-motion)/)']
};

// Test suites to run
const testSuites = [
    {
        name: 'Error Handling Workflow',
        file: 'error-handling-workflow.test.js',
        description: 'Tests complete error handling system workflow'
    },
    {
        name: 'Complete User Workflow',
        file: 'complete-user-workflow.test.js',
        description: 'Tests end-to-end user experience with error handling'
    }];

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

function logSection(title) {
    console.log(`\n${'='.repeat(60)}`);
    console.log(colorize(title, 'cyan'));
    console.log('='.repeat(60));
}

function logSubsection(title) {
    console.log(`\n${colorize(title, 'yellow')}`);
    console.log('-'.repeat(40));
}

function logSuccess(message) {
    console.log(colorize(`✅ ${message}`, 'green'));
}

function logError(message) {
    console.log(colorize(`❌ ${message}`, 'red'));
}

function logWarning(message) {
    console.log(colorize(`⚠️  ${message}`, 'yellow'));
}

function logInfo(message) {
    console.log(colorize(`ℹ️  ${message}`, 'blue'));
}

// Check if required dependencies are installed
function checkDependencies() {
    logSubsection('Checking Dependencies');

    const requiredDeps = [
        '@testing-library/react',
        '@testing-library/jest-dom',
        '@testing-library/user-event',
        'jest'];

    const packageJsonPath = path.join(process.cwd(), 'package.json');

    if (!fs.existsSync(packageJsonPath)) {
        logError('package.json not found');
        return false;
    }

    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
    };

    let allPresent = true;

    for (const dep of requiredDeps) {
        if (allDeps[dep]) {
            logSuccess(`${dep} is installed`);
        } else {
            logError(`${dep} is missing`);
            allPresent = false;
        }
    }

    return allPresent;
}

// Setup test environment
function setupTestEnvironment() {
    logSubsection('Setting up Test Environment');

    // Create test setup file if it doesn't exist
    const setupTestsPath = path.join(process.cwd(), 'src', '__tests__', 'setupTests.js');

    if (!fs.existsSync(setupTestsPath)) {
        const setupContent = `
// Test setup for end-to-end tests
require('@testing-library/jest-dom');

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
    constructor() {}
    disconnect() {}
    observe() {}
    unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
    constructor() {}
    disconnect() {}
    observe() {}
    unobserve() {}
};

// Increase test timeout for e2e tests
jest.setTimeout(60000);

// Suppress console warnings in tests
const originalWarn = console.warn;
console.warn = (...args) => {
    if (args[0]?.includes && (
        args[0].includes('Warning is deprecated') ||
        args[0].includes('Warning')
    )) {
        return;
    }
    originalWarn.apply(console, args);
};
`;

        fs.writeFileSync(setupTestsPath, setupContent);
        logSuccess('Created test setup file');
    } else {
        logSuccess('Test setup file exists');
    }
}

// Run individual test suite
function runTestSuite(suite) {
    logSubsection(`Running ${suite.name}`);
    logInfo(suite.description);

    const testFilePath = path.join(__dirname, suite.file);

    if (!fs.existsSync(testFilePath)) {
        logError(`Test file not found: ${suite.file}`);
        return false;
    }

    try {
        const jestCommand = `npx jest "${testFilePath}" --verbose --detectOpenHandles --forceExit`;

        logInfo(`Executing: ${jestCommand}`);

        const result = execSync(jestCommand, {
            stdio: 'inherit',
            cwd: process.cwd(),
            env: {
                ...process.env,
                NODE_ENV: 'test',
                CI: 'true'
            }
        });

        logSuccess(`${suite.name} completed successfully`);
        return true;

    } catch (error) {
        logError(`${suite.name} failed`);
        console.error(error.message);
        return false;
    }
}

// Generate test report
function generateTestReport(results) {
    logSubsection('Test Report');

    const totalTests = results.length;
    const passedTests = results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;

    console.log(`\nTotal Test Suites: ${totalTests}`);
    console.log(colorize(`Passed: ${passedTests}`, 'green'));

    if (failedTests > 0) {
        console.log(colorize(`Failed: ${failedTests}`, 'red'));
    }

    console.log('\nDetailed Results:');
    results.forEach(result => {
        const status = result.passed ?
            colorize('PASS', 'green') : colorize('FAIL', 'red');
        console.log(`  ${status} ${result.suite.name}`);
    });

    const successRate = (passedTests / totalTests) * 100;
    console.log(`\nSuccess Rate: ${successRate.toFixed(1)}%`);

    if (successRate === 100) {
        logSuccess('All tests passed! 🎉');
    } else if (successRate >= 80) {
        logWarning('Most tests passed, but some issues need attention');
    } else {
        logError('Multiple test failures detected - system needs attention');
    }

    return successRate === 100;
}

// Main execution function
async function main() {
    logSection('End-to-End Test Runner');
    logInfo('Running comprehensive error handling system tests');

    // Check dependencies
    if (!checkDependencies()) {
        logError('Missing required dependencies. Please run: npm install --save-dev @testing-library/react @testing-library/jest-dom @testing-library/user-event jest');
        process.exit(1);
    }

    // Setup test environment
    setupTestEnvironment();

    // Run test suites
    const results = [];

    for (const suite of testSuites) {
        const passed = runTestSuite(suite);
        results.push({suite, passed});

        // Add delay between test suites to prevent resource conflicts
        if (suite !== testSuites[testSuites.length - 1]) {
            logInfo('Waiting 2 seconds before next test suite...');
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }

    // Generate report
    const allPassed = generateTestReport(results);

    // Exit with appropriate code
    process.exit(allPassed ? 0 : 1);
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
    logError('Uncaught exception in test runner:');
    console.error(error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logError('Unhandled rejection in test runner:');
    console.error('Promise:', promise);
    console.error('Reason:', reason);
    process.exit(1);
});

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        logError('Test runner failed:');
        console.error(error);
        process.exit(1);
    });
}

module.exports = {
    runTestSuite,
    checkDependencies,
    setupTestEnvironment,
    generateTestReport,
    testSuites,
    main
};