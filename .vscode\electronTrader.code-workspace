{"folders": [{"path": ".."}], "settings": {"sqltools.connections": [{"mysqlOptions": {"authProtocol": "default", "enableSsl": "Disabled"}, "previewLimit": 50, "server": "localhost", "port": 3306, "driver": "MySQL", "name": "consolidated_schema", "socketPath": "C:\\Users\\<USER>\\Documents\\keep.n8n!\\N8N-Nodes\\databases\\consolidated_schema.sql", "database": "SQL", "username": "n8n", "password": "010203"}, {"previewLimit": 50, "driver": "SQLite", "database": "${workspaceFolder:electronTrader}/.roo/context.db", "name": "context.db"}], "sqltools.useNodeRuntime": true, "kiroAgent.trustedCommands": ["npm*", "pnpm*", "node*", "cd*", "node *", "node *", "node -e \"\ntry {\n  const TradingOrchestrator = require('./app/trading/engines/trading/orchestration/TradingOrchestrator');\n  console.log('✅ TradingOrchestrator import successful');\n  \n  const orchestrator = new TradingOrchestrator();\n  console.log('✅ TradingOrchestrator instantiation successful');\n  console.log('✅ Component managers integration complete');\n} catch (error) {\n  console.error('❌ Error:', error.message);\n  console.error('First few lines of stack:');\n  console.error(error.stack.split('\\\\n').slice(0, 3).join('\\\\n'));\n  process.exit(1);\n}\n\"", "node -e \"\nasync function testComponentManagers() {\n  try {\n    const TradingOrchestrator = require('./app/trading/engines/trading/orchestration/TradingOrchestrator');\n    console.log('✅ TradingOrchestrator import successful');\n    \n    const orchestrator = new TradingOrchestrator();\n    console.log('✅ TradingOrchestrator instantiation successful');\n    \n    // Initialize the orchestrator\n    await orchestrator.initialize();\n    console.log('✅ TradingOrchestrator initialization successful');\n    \n    // Test component access\n    const components = [\n      'alertManager',\n      'systemInfoManager', \n      'riskManager',\n      'opportunityScanner',\n      'exchangeHealthMonitor',\n      'portfolioMonitor',\n      'drawdownAnalyzer',\n      'arbitrageEngine',\n      'gridBotManager',\n      'tradingExecutor'\n    ];\n    \n    console.log('\\\\n🔍 Testing component availability:');\n    for (const componentName of components) {\n      const component = orchestrator.components[componentName];\n      if (component) {\n        console.log(\\`✅ \\${componentName}: Available\\`);\n      } else {\n        console.log(\\`❌ \\${componentName}: Missing\\`);\n      }\n    }\n    \n    // Test some component methods\n    console.log('\\\\n🧪 Testing component methods:');\n    \n    if (orchestrator.systemInfoManager) {\n      const systemInfo = orchestrator.systemInfoManager.getSystemInfo();\n      console.log(\\`✅ SystemInfoManager.getSystemInfo(): \\${systemInfo.platform}\\`);\n    }\n    \n    if (orchestrator.components.riskManager) {\n      const riskMetrics = await orchestrator.components.riskManager.getRiskMetrics();\n      console.log(\\`✅ RiskManager.getRiskMetrics(): Score \\${riskMetrics.overallRiskScore || 0}\\`);\n    }\n    \n    if (orchestrator.components.alertManager) {\n      const alerts = orchestrator.components.alertManager.getSystemAlerts();\n      console.log(\\`✅ AlertManager.getSystemAlerts(): \\${alerts.length} alerts\\`);\n    }\n    \n    console.log('\\\\n✅ All component managers successfully implemented and integrated!');\n    \n  } catch (error) {\n    console.error('❌ Error:', error.message);\n    console.error('Stack:', error.stack.split('\\\\n').slice(0, 5).join('\\\\n'));\n    process.exit(1);\n  }\n}\n\ntestComponentManagers();\n\"", "npm test -- --testPathPattern=\"start-button-workflow-integration.test.js\" --run", "npm run test:unit -- --testPathPattern=\"start-button-workflow-integration.test.js\"", "npx *", "npm run test -- --testNamePattern=\"RealTimeStatusIntegration\" --run", "npm run test:unit -- --testPathPattern=\"realTimeStatusService.test.js\"", "copy TradingOrchestrator.js TradingOrchestrator.js.backup", "npm run start", "npm list --depth=0"]}}