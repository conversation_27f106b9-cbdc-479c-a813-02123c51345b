/**
 * @jest-environment jsdom
 */
/* eslint-disable no-unused-vars */

/**
 * Complete User Workflow End-to-End Tests
 * Tests the entire user journey from startup to trading operations with error handling
 */

import React from 'react';
import {act, fireEvent, render, screen, waitFor,} from '@testing-library/react';
import '@testing-library/jest-dom';
import {BrowserRouter} from 'react-router-dom';
import App from '../../App';
import GlobalErrorHandler from '../../utils/GlobalErrorHandler';

// Mock electron API
const mockElectronAPI = {
    ipcRenderer: {
        invoke: jest.fn(),
        on: jest.fn(),
        removeAllListeners: jest.fn(),
    },
};

Object.defineProperty(window, 'electronAPI', {
    value: mockElectronAPI,
    writable: true,
});

// Mock localStorage and sessionStorage
const createStorageMock = () => {
    let store = {};
    return {
        getItem: jest.fn(key => store[key] || null),
        setItem: jest.fn((key, value) => {
            store[key] = value.toString();
        }),
        removeItem: jest.fn(key => {
            delete store[key];
        }),
        clear: jest.fn(() => {
            store = {};
        }),
    };
};

Object.defineProperty(window, 'localStorage', {
    value: createStorageMock(),
    writable: true,
});
Object.defineProperty(window, 'sessionStorage', {
    value: createStorageMock(),
    writable: true,
});

// Suppress console output
beforeAll(() => {
    jest.spyOn(console, 'log').mockImplementation(() => {
    });
    jest.spyOn(console, 'info').mockImplementation(() => {
    });
    jest.spyOn(console, 'warn').mockImplementation(() => {
    });
    jest.spyOn(console, 'error').mockImplementation(() => {
    });
    jest.spyOn(console, 'debug').mockImplementation(() => {
    });
});

afterAll(() => {
    jest.restoreAllMocks();
});

describe('Complete User Workflow End-to-End Tests', () => {
    let mockIpcInvoke;

    beforeEach(() => {
        jest.clearAllMocks();
        mockIpcInvoke = mockElectronAPI.ipcRenderer.invoke;

        // Default mock implementations
        mockIpcInvoke.mockImplementation((channel) => {
            const timestamp = new Date().toISOString();
            switch (channel) {
                case 'health-check':
                    return {success: true, data: {initialized: true, running: true, timestamp}};
                case 'get-system-status':
                    return {
                        success: true,
                        data: {isRunning: true, initialized: true, health: 'healthy', message: 'System OK'}
                    };
                case 'get-startup-progress':
                    return {success: true, data: {phase: 'complete', progress: 100, message: 'Ready'}};
                case 'initialize-trading':
                    return {success: true, data: {message: 'Initialized'}};
                case 'get-database-status':
                    return {success: true, data: {initialized: true, healthy: true, timestamp}};
                case 'check-database-ready':
                    return {success: true, data: {ready: true, initialized: true, timestamp}};
                case 'get-real-time-status':
                    return {
                        success: true,
                        data: {isRunning: true, initialized: true, health: 'healthy', timestamp, uptime: 12345}
                    };
                case 'get-system-info':
                    return {
                        success: true,
                        data: {
                            appVersion: '1.0.0',
                            electronVersion: '25.0.0',
                            nodeVersion: '18.0.0',
                            platform: 'test',
                            arch: 'x64',
                            timestamp
                        }
                    };
                case 'get-app-version':
                    return {success: true, data: {version: '1.0.0', name: 'Test App'}};
                case 'register-error-listener':
                case 'log-frontend-errors':
                case 'report-frontend-health':
                case 'emergency-stop-trading':
                case 'recover-trading-system':
                    return {success: true};
                case 'get-error-handler-status':
                    return {
                        success: true,
                        data: {status: 'healthy', circuitBreaker: {isOpen: false}, recovery: {enabled: true}}
                    };
                default:
                    return {success: true, data: {}};
            }
        });
    });

    const renderApp = () =>
        render(
            <BrowserRouter>
                <App/>
            </BrowserRouter>,
        );

    test('should complete full application startup and render main content', async () => {
        await act(async () => {
            renderApp();
        });

        await waitFor(() => {
            const mainContent = screen.queryByTestId('main-app-container') || screen.queryByRole('main');
            expect(mainContent).toBeInTheDocument();
        }, {timeout: 5000});

        expect(mockIpcInvoke).toHaveBeenCalledWith('get-startup-progress');
    });

    test('should handle startup errors gracefully and show an error message', () => {
        mockIpcInvoke.mockImplementation(channel => {
            if (channel === 'initialize-trading') {
                return Promise.reject(new Error('Init failed'));
            }
            return Promise.resolve({success: true, data: {}});
        });

        await act(() => {
            renderApp();
        });

        await waitFor(() => {
            const errorUI = screen.queryByText(/error|failed/i);
            if (errorUI) {
                expect(errorUI).toBeInTheDocument();
            }
        }, {timeout: 5000});
    });

    test('should navigate between sections without crashing', async () => {
        await act(async () => {
            renderApp();
        });
        await waitFor(() => expect(screen.queryByRole('main')).toBeInTheDocument());

        const navLinks = screen.queryAllByRole('link');
        for (const link of navLinks) {
            await act(() => {
                fireEvent.click(link);
            });
            // Check that the app hasn't crashed
            expect(screen.queryByText(/something went wrong/i)).not.toBeInTheDocument();
        }
    });

    test('should display real-time system status updates', async () => {
        await act(async () => {
            renderApp();
        });

        await waitFor(() => {
            expect(mockIpcInvoke).toHaveBeenCalledWith('get-real-time-status');
        });

        // Simulate a health change event from the backend
        act(() => {
            const event = new CustomEvent('system-event', {
                detail: {
                    eventType: 'system-health-changed',
                    data: {currentHealth: 'degraded'},
                },
            });
            window.dispatchEvent(event);
        });

        await waitFor(() => {
            const statusIndicator = screen.queryByText(/degraded/i);
            if (statusIndicator) {
                expect(statusIndicator).toBeInTheDocument();
            }
        });
    });

    test('should provide clear feedback on user-facing errors', async () => {
        await act(async () => {
            renderApp();
        });
        await waitFor(() => expect(screen.queryByRole('main')).toBeInTheDocument());

        // Simulate a user action that causes an error
        act(() => {
            const error = new Error('User action failed');
            window.dispatchEvent(new ErrorEvent('error', {error}));
        });

        await waitFor(() => {
            const errorMessage = screen.queryByText(/operation failed/i);
            if (errorMessage) {
                expect(errorMessage).toBeInTheDocument();
            }
        });
    });

    test('should persist error logs across sessions', async () => {
        // First session: log an error
        await act(async () => {
            const {unmount} = renderApp();
            await waitFor(() => expect(GlobalErrorHandler.isInitialized()).toBe(true));
            GlobalErrorHandler.error('Error to be persisted');
            await waitFor(() => {
                expect(window.localStorage.setItem).toHaveBeenCalledWith(
                    'errorLogger_logs',
                    expect.stringContaining('Error to be persisted'),
                );
            });
            unmount();
        });

        // Second session: check if logs are loaded
        await act(async () => {
            renderApp();
            await waitFor(() => {
                expect(GlobalErrorHandler.getLogs().some(log => log.message === 'Error to be persisted')).toBe(true);
            });
        });
    });
});