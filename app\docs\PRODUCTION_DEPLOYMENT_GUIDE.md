# Production Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Meme Coin Trader application in production environments across Windows, Mac, and Linux platforms.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Production Build Process](#production-build-process)
3. [Environment Configuration](#environment-configuration)
4. [Platform-Specific Deployment](#platform-specific-deployment)
5. [Performance Monitoring Setup](#performance-monitoring-setup)
6. [Error Reporting Configuration](#error-reporting-configuration)
7. [Troubleshooting](#troubleshooting)
8. [Security Considerations](#security-considerations)

## Prerequisites

### System Requirements

- **Node.js**: Version 18.x or higher
- **npm**: Version 8.x or higher
- **Python**: Version 3.8+ (for native module compilation)
- **Git**: Latest version
- **Build Tools**: Platform-specific build tools

### Platform-Specific Prerequisites

#### Windows
- Visual Studio Build Tools 2019 or later
- Windows SDK
- .NET Framework 4.7.2 or later

#### macOS
- Xcode Command Line Tools
- macOS 10.15 (<PERSON>) or later

#### Linux
- GCC/G++ compiler
- Make
- Python development headers
- libsqlite3-dev

## Production Build Process

### 1. Environment Setup

```bash
# Clone the repository
git clone <repository-url>
cd electrontrader-monorepo

# Install root dependencies
npm install

# Install app dependencies
cd app
npm install

# Install trading engine dependencies
cd trading
npm install
cd ..
```

### 2. Build Configuration

The application uses optimized webpack configurations for production:

- `webpack.config.production.js` - Main production configuration
- `webpack.config.performance.js` - Performance-optimized build
- `electron-builder.config.js` - Electron packaging configuration

### 3. Production Build Commands

```bash
# From the app directory
cd app

# Build React application for production
npm run build:production

# Build with performance optimizations
npm run build:webpack

# Package Electron application
npm run dist

# Build for all platforms (requires platform-specific setup)
npm run dist-all
```

### 4. Build Optimization Settings

The production build includes:

- **Code Splitting**: Automatic chunk splitting for optimal loading
- **Tree Shaking**: Dead code elimination
- **Minification**: JavaScript and CSS minification
- **Compression**: Gzip compression for assets
- **Bundle Analysis**: Webpack bundle analyzer integration

## Environment Configuration

### Production Environment Variables

Create a `.env.production` file in the app directory:

```bash
# Application Environment
NODE_ENV=production
ELECTRON_ENV=production

# Database Configuration
DATABASE_TYPE=sqlite
DATABASE_PATH=./databases/trading_system.db
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30000

# Trading Configuration
TRADING_MODE=live
PAPER_TRADING=false
MAX_CONCURRENT_TRADES=5
RISK_MANAGEMENT_ENABLED=true

# API Configuration
API_RATE_LIMIT=100
API_TIMEOUT=10000
EXCHANGE_TIMEOUT=15000

# Security
ENCRYPTION_ENABLED=true
SECURE_CREDENTIALS=true
API_KEY_ENCRYPTION=true

# Monitoring
PERFORMANCE_MONITORING=true
ERROR_REPORTING=true
HEALTH_CHECK_INTERVAL=30000
METRICS_COLLECTION=true

# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_ROTATION=true
MAX_LOG_SIZE=10MB
MAX_LOG_FILES=5

# Network
PROXY_ENABLED=false
NETWORK_TIMEOUT=30000
RETRY_ATTEMPTS=3
```

### Trading Engine Configuration

Configure production settings in `app/trading/config/production.json`:

```json
{
  "environment": "production",
  "trading": {
    "mode": "live",
    "paperTrading": false,
    "maxConcurrentTrades": 5,
    "riskManagement": {
      "enabled": true,
      "maxPositionSize": 0.1,
      "stopLossPercentage": 0.05,
      "takeProfitPercentage": 0.15
    }
  },
  "database": {
    "type": "sqlite",
    "path": "./databases/trading_system.db",
    "poolSize": 10,
    "timeout": 30000,
    "backup": {
      "enabled": true,
      "interval": 3600000,
      "retention": 7
    }
  },
  "monitoring": {
    "healthCheck": {
      "enabled": true,
      "interval": 30000,
      "endpoints": ["/health", "/trading/status"]
    },
    "metrics": {
      "enabled": true,
      "collection": true,
      "retention": 30
    }
  },
  "security": {
    "encryption": true,
    "credentialManager": true,
    "apiKeyEncryption": true,
    "rateLimiting": true
  }
}
```

## Platform-Specific Deployment

### Windows Deployment

#### Build Requirements
```bash
# Install Windows build tools
npm install -g windows-build-tools

# Install native dependencies
npm rebuild --runtime=electron --target=31.2.1 --disturl=https://electronjs.org/headers --build-from-source
```

#### Packaging
```bash
# Build Windows installer
npm run package:win

# Build portable version
npm run package:win:portable

# Build MSI installer
npm run package:win:msi
```

#### Installation Directory
- Default: `C:\Program Files\Meme Coin Trader`
- User Data: `%APPDATA%\meme-coin-trader`
- Logs: `%APPDATA%\meme-coin-trader\logs`

### macOS Deployment

#### Build Requirements
```bash
# Install Xcode command line tools
xcode-select --install

# Rebuild native modules for macOS
npm rebuild --runtime=electron --target=31.2.1 --disturl=https://electronjs.org/headers --build-from-source
```

#### Code Signing (Required for Distribution)
```bash
# Set up code signing certificate
export CSC_LINK="path/to/certificate.p12"
export CSC_KEY_PASSWORD="certificate_password"

# Build signed application
npm run package:mac
```

#### Packaging
```bash
# Build DMG installer
npm run package:mac:dmg

# Build ZIP distribution
npm run package:mac:zip
```

#### Installation Directory
- Application: `/Applications/Meme Coin Trader.app`
- User Data: `~/Library/Application Support/meme-coin-trader`
- Logs: `~/Library/Logs/meme-coin-trader`

### Linux Deployment

#### Build Requirements
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install build-essential libnss3-dev libatk-bridge2.0-dev libdrm2 libxkbcommon-dev libxss1 libasound2-dev

# CentOS/RHEL/Fedora
sudo yum groupinstall "Development Tools"
sudo yum install nss atk at-spi2-atk libdrm libxkbcommon libXScrnSaver alsa-lib
```

#### Packaging
```bash
# Build AppImage
npm run package:linux:appimage

# Build DEB package
npm run package:linux:deb

# Build RPM package
npm run package:linux:rpm

# Build Snap package
npm run package:linux:snap
```

#### Installation Directory
- System-wide: `/opt/meme-coin-trader`
- User Data: `~/.config/meme-coin-trader`
- Logs: `~/.local/share/meme-coin-trader/logs`

## Performance Monitoring Setup

### Built-in Performance Monitoring

The application includes comprehensive performance monitoring:

```javascript
// Performance monitoring configuration
const performanceConfig = {
  enabled: true,
  metrics: {
    cpu: true,
    memory: true,
    disk: true,
    network: true,
    trading: true
  },
  collection: {
    interval: 30000,
    retention: 86400000, // 24 hours
    aggregation: true
  },
  alerts: {
    cpuThreshold: 80,
    memoryThreshold: 85,
    diskThreshold: 90,
    responseTimeThreshold: 5000
  }
};
```

### External Monitoring Integration

#### Prometheus Integration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'meme-coin-trader'
    static_configs:
      - targets: ['localhost:3001']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

#### Grafana Dashboard
Import the provided Grafana dashboard configuration from `app/monitoring/grafana-dashboard.json`.

### Health Check Endpoints

The application exposes health check endpoints:

- `http://localhost:3001/health` - Basic health status
- `http://localhost:3001/health/detailed` - Detailed system status
- `http://localhost:3001/metrics` - Prometheus metrics

## Error Reporting Configuration

### Built-in Error Reporting

Configure error reporting in production:

```javascript
// Error reporting configuration
const errorReportingConfig = {
  enabled: true,
  level: 'error',
  includeStack: true,
  includeContext: true,
  storage: {
    type: 'file',
    path: './logs/errors',
    rotation: true,
    maxSize: '10MB',
    maxFiles: 10
  },
  notifications: {
    email: {
      enabled: false,
      recipients: ['<EMAIL>']
    },
    webhook: {
      enabled: false,
      url: 'https://hooks.slack.com/services/...'
    }
  }
};
```

### External Error Reporting Services

#### Sentry Integration
```javascript
// Install Sentry
npm install @sentry/electron

// Configure in main.js
const Sentry = require('@sentry/electron');

Sentry.init({
  dsn: 'YOUR_SENTRY_DSN',
  environment: 'production'
});
```

#### Custom Error Reporting
```javascript
// Custom error reporter
class ProductionErrorReporter {
  constructor(config) {
    this.config = config;
    this.setupErrorHandlers();
  }

  setupErrorHandlers() {
    process.on('uncaughtException', this.handleError.bind(this));
    process.on('unhandledRejection', this.handleError.bind(this));
  }

  handleError(error) {
    // Log error
    console.error('Production Error:', error);
    
    // Send to external service
    this.sendToExternalService(error);
    
    // Store locally
    this.storeError(error);
  }
}
```

## Security Considerations

### Production Security Checklist

- [ ] Enable HTTPS for all API communications
- [ ] Encrypt sensitive configuration data
- [ ] Use secure credential storage
- [ ] Enable API rate limiting
- [ ] Implement proper authentication
- [ ] Regular security updates
- [ ] Secure database connections
- [ ] Enable audit logging

### Credential Management

```javascript
// Secure credential storage
const credentials = {
  exchanges: {
    binance: {
      apiKey: process.env.BINANCE_API_KEY,
      apiSecret: process.env.BINANCE_API_SECRET,
      encrypted: true
    }
  },
  database: {
    connectionString: process.env.DATABASE_URL,
    encrypted: true
  }
};
```

### Network Security

```javascript
// Network security configuration
const networkConfig = {
  ssl: {
    enabled: true,
    rejectUnauthorized: true
  },
  proxy: {
    enabled: process.env.PROXY_ENABLED === 'true',
    host: process.env.PROXY_HOST,
    port: process.env.PROXY_PORT
  },
  rateLimiting: {
    enabled: true,
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  }
};
```

## Deployment Automation

### CI/CD Pipeline Example

```yaml
# .github/workflows/deploy.yml
name: Production Deployment

on:
  push:
    tags:
      - 'v*'

jobs:
  build-and-deploy:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]

    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: |
          npm install
          cd app && npm install
          cd trading && npm install
          
      - name: Build application
        run: |
          cd app
          npm run build:production
          npm run dist
          
      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.os }}-build
          path: app/dist/
```

### Docker Deployment

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY app/package*.json ./app/
COPY app/trading/package*.json ./app/trading/

# Install dependencies
RUN npm install --production
RUN cd app && npm install --production
RUN cd app/trading && npm install --production

# Copy application code
COPY . .

# Build application
RUN cd app && npm run build:production

# Expose ports
EXPOSE 3001

# Start application
CMD ["npm", "start"]
```

## Post-Deployment Verification

### Verification Checklist

1. **Application Startup**
   - [ ] Application starts without errors
   - [ ] All components initialize successfully
   - [ ] Database connections established

2. **Trading System**
   - [ ] Trading engines start correctly
   - [ ] Exchange connections established
   - [ ] Risk management active

3. **Monitoring**
   - [ ] Health checks responding
   - [ ] Metrics collection active
   - [ ] Error reporting functional

4. **Performance**
   - [ ] Memory usage within limits
   - [ ] CPU usage acceptable
   - [ ] Response times optimal

### Verification Scripts

```bash
# Health check script
#!/bin/bash
echo "Checking application health..."

# Check if application is running
if pgrep -f "meme-coin-trader" > /dev/null; then
    echo "✓ Application is running"
else
    echo "✗ Application is not running"
    exit 1
fi

# Check health endpoint
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    echo "✓ Health endpoint responding"
else
    echo "✗ Health endpoint not responding"
    exit 1
fi

echo "All checks passed!"
```

## Maintenance and Updates

### Update Process

1. **Backup Current Installation**
   ```bash
   # Backup user data and configuration
   cp -r ~/.config/meme-coin-trader ~/.config/meme-coin-trader.backup
   ```

2. **Download New Version**
   ```bash
   # Download and install new version
   wget https://releases.example.com/meme-coin-trader-v2.0.0.AppImage
   chmod +x meme-coin-trader-v2.0.0.AppImage
   ```

3. **Verify Update**
   ```bash
   # Check version
   ./meme-coin-trader --version
   
   # Run health check
   ./scripts/health-check.sh
   ```

### Rollback Procedure

```bash
# Stop current application
pkill -f "meme-coin-trader"

# Restore previous version
cp meme-coin-trader.backup meme-coin-trader

# Restore configuration
cp -r ~/.config/meme-coin-trader.backup ~/.config/meme-coin-trader

# Start application
./meme-coin-trader
```

## Support and Documentation

### Log Locations

- **Windows**: `%APPDATA%\meme-coin-trader\logs`
- **macOS**: `~/Library/Logs/meme-coin-trader`
- **Linux**: `~/.local/share/meme-coin-trader/logs`

### Configuration Files

- **Windows**: `%APPDATA%\meme-coin-trader\config`
- **macOS**: `~/Library/Application Support/meme-coin-trader`
- **Linux**: `~/.config/meme-coin-trader`

### Getting Help

- Documentation: [Link to documentation]
- Issue Tracker: [Link to issues]
- Community Forum: [Link to forum]
- Email Support: <EMAIL>

---

*This guide is maintained by the development team. Last updated: January 2025*