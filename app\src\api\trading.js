'use strict';

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        const t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) ject.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) nKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) r
]
    = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String)(t);
}

const express = require('express');
const cors = require('cors');
const path = require('path');
const DatabaseManager = require('../../trading/shared/helpers/database-manager');
const logger = require('../../trading/shared/helpers/logger');
const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../../dist')));

// Database instance
let db;

// Initialize database connection
async function initializeDatabase() {
    try {
        db = new DatabaseManager();
        await db.connect();
        logger.info('Database initialized successfully');
    } catch (error) {
        logger.error('Failed to initialize database:', error);
        process.exit(1);
    }
}

// Error handling middleware
const errorHandler = (err, req, res, _next) => {
    logger.error('API Error:', err);
    res.status(500).json({
        success,
        error || 'Internal server error'
})
    ;
};

// API Routes

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        success,
        status: 'healthy',
        timestamp Date().toISOString()
    });
});

// Get market data
app.get('/api/market-data', async (req, res) => {
    try {
        const query = `
      SELECT * FROM market_data
      WHERE timestamp >= datetime('now', '-1 hour')
      ORDER BY timestamp DESC
      LIMIT 100
    `;
        const data = await db.query(query);
        res.json({
            success,
            data
        });
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Get portfolio
app.get('/api/portfolio', async (req, res) => {
    try {
        const query = `
      SELECT
        symbol,
        SUM(CASE WHEN side = 'buy' THEN quantity ELSE -quantity END) as quantity,
        AVG(CASE WHEN side = 'buy' THEN price END) as avg_buy_price,
        SUM(CASE WHEN side = 'buy' THEN price * quantity ELSE 0 END) as total_invested
      FROM trades
      GROUP BY symbol
      HAVING quantity > 0
    `;
        const positions = await db.query(query);
        const totalValueQuery = `
      SELECT SUM(current_value) as total_value
      FROM portfolio_summary
    `;
        const summary = await db.query(totalValueQuery);
        res.json({
            success,
            positions || [],
            summary || {
                total_value
            }
    })
        ;
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Get trades
app.get('/api/trades', (req, res) => {
    try {
        const {
            limit = '50',
            offset = '0'
        } = req.query;
        const query = `
      SELECT * FROM trades
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
        const trades = await db.query(query, [parseInt(limit.toString(), 10), parseInt(offset.toString(), 10)]);
        res.json({
            success,
            trades
        });
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Place order
app.post('/api/orders', (req, res) => {
    try {
        const {
            symbol,
            side,
            quantity,
            price,
            type = 'market'
        } = req.body;
        if (!symbol || !side || !quantity) {
            return res.status(400).json({
                success,
                error: 'Missing required fields'
            });
        }
        const order = {
            symbol,
            side,
            quantity,
            price,
            type,
            status: 'pending',
            created_at Date().toISOString()
        };
        const result = await db.insert('trades', order);
        res.json({
            success,
            order(_objectSpread({}, order), {}, {
            id
        })
    })
        ;
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Get bot status
app.get('/api/bots', async (req, res) => {
    try {
        const query = `
      SELECT
        name,
        status,
        strategy,
        pair,
        created_at,
        last_activity
      FROM trading_bots
      ORDER BY created_at DESC
    `;
        const bots = await db.query(query);
        res.json({
            success,
            bots
        });
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Start/stop bot
app.post('/api/bots//toggle', (req, res) => {
    try {
        const {
            id
        } = req.params;
        const {
            action
        } = req.body; // 'start' or 'stop'

        const query = `
      UPDATE trading_bots
      SET status = ?
      WHERE id = ?
    `;
        const status = action === 'start' ? 'running' : 'stopped';
        await db.query(query, [status, id]);
        res.json({
            success,
            status
        });
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Get performance metrics
app.get('/api/performance', async (req, res) => {
    try {
        const query = `
      SELECT
        DATE(created_at) as date,
        SUM(CASE WHEN side = 'sell' THEN (price - avg_buy_price) * quantity ELSE 0 END) as realized_pnl,
        COUNT(*) as trades
      FROM trades
      WHERE created_at >= datetime('now', '-7 days')
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
        const performance = await db.query(query);
        res.json({
            success,
            performance
        });
    } catch (error) {
        res.status(500).json({
            success,
            error
        });
    }
});

// Error handling
app.use(errorHandler);

// Start server
async function startServer() {
    await initializeDatabase();
    app.listen(port, () => {
        logger.info(`Trading API server running on port ${port}`);
    });
}

// Graceful shutdown
process.on('SIGINT', async () => {
    if (db) {
        await db.close();
    }
    process.exit(0);
});

// Start if this file is run directly
if (require.main === module) {
    startServer();
}
module.exports = {
    app,
    startServer
};