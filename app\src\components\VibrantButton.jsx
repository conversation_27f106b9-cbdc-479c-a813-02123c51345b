// @ts-nocheck
import React from 'react';
import PropTypes from 'prop-types';
import {Button} from '@mui/material';

/**
 * A customizable button component with vibrant gradient backgrounds and hover effects.
 *
 * @param {Object} props - The properties object.
 * @param {React.ReactNode} props.children - The content to be displayed inside the button.
 * @param {string} [props.color='primary'] - The color scheme of the button ('primary', 'error', 'success', 'warning').
 * @param {boolean} [props.disabled] - If true, the button will be disabled.
 * @param {boolean} [props.fullWidth] - If true, the button will take the full width of its container.
 * @param {Object} [props.sx] - Additional styles to apply to the button.
 * @param {Function} [props.onClick] - Click handler function.
 * @param {React.ReactNode} [props.startIcon] - Icon to display at the start of the button.
 * @param {React.ReactNode} [props.endIcon] - Icon to display at the end of the button.
 * @param {string} [props.size] - Size of the button ('small', 'medium', 'large').
 * @param {Object} [props.props] - Additional properties to pass to the Material-UI Button component.
 *
 * @returns {JSX.Element} The rendered vibrant button component.
 */

export default function VibrantButton({
                                          children,
                                          color = 'primary',
                                          disabled,
                                          sx,
                                          onClick,
                                          startIcon,
                                          endIcon,
                                          size,
                                          ...props
                                      }) {
    const getButtonStyles = () => {
        if (disabled) {
            return {
                background: 'rgba(255,255,255,0.1)',
                color: 'rgba(255,255,255,0.3)',
                cursor: 'not-allowed'
            };
        }

        switch (color) {
            case 'error':
                return {
                    background: 'linear-gradient(90deg, #f44336 0%, #d32f2f 100%)',
                    '&:hover': {
                        background: 'linear-gradient(90deg, #d32f2f 0%, #b71c1c 100%)',
                        boxShadow: '0 8px 32px rgba(244,67,54,0.4)',
                        transform: 'translateY(-2px)'
                    }
                };
            case 'success':
                return {
                    background: 'linear-gradient(90deg, #4caf50 0%, #388e3c 100%)',
                    '&:hover': {
                        background: 'linear-gradient(90deg, #388e3c 0%, #2e7d32 100%)',
                        boxShadow: '0 8px 32px rgba(76,175,80,0.4)',
                        transform: 'translateY(-2px)'
                    }
                };
            case 'warning':
                return {
                    background: 'linear-gradient(90deg, #ff9800 0%, #f57c00 100%)',
                    '&:hover': {
                        background: 'linear-gradient(90deg, #f57c00 0%, #ef6c00 100%)',
                        boxShadow: '0 8px 32px rgba(255,152,0,0.4)',
                        transform: 'translateY(-2px)'
                    }
                };
            default:
                return {
                    background: 'linear-gradient(90deg, #00eaff 0%, #a259ff 100%)',
                    '&:hover': {
                        background: 'linear-gradient(90deg, #a259ff 0%, #7b1fa2 100%)',
                        boxShadow: '0 8px 32px rgba(162,89,255,0.4)',
                        transform: 'translateY(-2px)'
                    }
                };
        }
    };

    return (
        <Button
            onClick={onClick}
            startIcon={startIcon}
            endIcon={endIcon}
            size={size}
            {...props}
            disabled={disabled}
            sx={{
                ...getButtonStyles(),
                color: disabled ? 'rgba(255,255,255,0.3)' : '#fff',
                fontWeight: 700,
                borderRadius: 3,
                boxShadow: disabled ? 'none' : '0 4px 20px rgba(0,234,255,0.25)',
                textTransform: 'none',
                padding: '12px 24px',
                minHeight: '48px',
                position: 'relative',
                overflow: 'hidden',
                border: 'none',
                cursor: disabled ? 'not-allowed' : 'pointer',
                transition: 'all 0.3s ease',
                '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)',
                    transform: 'translateX(-100%)',
                    transition: 'transform 0.6s'
                },
                '&:hover::before': {
                    transform: disabled ? 'translateX(-100%)' : 'translateX(100%)'
                },
                '&:active': {
                    transform: disabled ? 'none' : 'scale(0.98)'
                },
                ...sx
            }}
        >
            {children}
        </Button>
    );
}

VibrantButton.propTypes = {
    children: PropTypes.node.isRequired,
    color: PropTypes.oneOf(['primary', 'error', 'success', 'warning']),
    disabled: PropTypes.bool,
    sx: PropTypes.object,
    onClick: PropTypes.func,
    startIcon: PropTypes.node,
    endIcon: PropTypes.node,
    size: PropTypes.oneOf(['small', 'medium', 'large']),
    fullWidth: PropTypes.bool
};
