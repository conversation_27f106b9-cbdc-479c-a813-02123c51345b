{"app": {"name": "<PERSON>me Coin Trader (Dev)", "description": "Development build of professional cryptocurrency trading platform"}, "electron": {"isDev": true, "devTools": true, "contextIsolation": true, "nodeIntegration": false, "webSecurity": false, "allowRunningInsecureContent": true, "experimentalFeatures": true}, "logging": {"level": "debug", "console": true, "file": true, "maxFiles": 3, "maxSize": "10m", "compress": false, "datePattern": "YYYY-MM-DD-HH", "auditFile": "logs/dev-audit.json", "errorFile": "logs/dev-error.log", "combinedFile": "logs/dev-combined.log"}, "performance": {"enableMetrics": true, "enableProfiling": true, "memoryLimit": "512mb", "cpuThreshold": 70, "gcOptimization": false, "v8Flags": ["--max-old-space-size=512"], "monitoring": {"interval": 10000, "alertThresholds": {"memory": 0.9, "cpu": 0.95, "heap": 0.9}}}, "security": {"enableCSP": false, "enableHSTS": false, "enableCORS": true, "allowedOrigins": ["http://localhost:3000", "http://127.0.0.1:3000"], "contentSecurityPolicy": {"defaultSrc": ["'self'", "'unsafe-inline'", "'unsafe-eval'"], "scriptSrc": ["'self'", "'unsafe-inline'", "'unsafe-eval'"], "styleSrc": ["'self'", "'unsafe-inline'"], "imgSrc": ["'self'", "data:", "http:", "https:"], "connectSrc": ["'self'", "ws:", "wss:", "http:", "https:"]}, "permissions": {"camera": false, "microphone": false, "geolocation": false, "notifications": true, "persistentStorage": true}}, "features": {"hotReload": true, "autoUpdate": false, "errorReporting": false, "analytics": false, "debugMode": true, "crashReporting": false, "telemetry": false, "autoLaunch": false, "minimizeToTray": false, "singleInstance": false}, "build": {"optimization": false, "minification": false, "compression": false, "sourceMap": true, "bundleAnalysis": true, "treeShaking": false, "deadCodeElimination": false, "assetOptimization": false, "imageOptimization": false, "fontOptimization": false}, "trading": {"environment": "development", "sandbox": true, "rateLimit": {"enabled": false, "requests": 10000, "window": 60000}, "timeout": 10000, "retries": 1, "backoff": "linear", "healthCheck": {"enabled": true, "interval": 30000, "timeout": 5000}}, "database": {"type": "sqlite", "path": "data/development.db", "backup": {"enabled": false, "interval": 7200000, "retention": 5, "compression": false}, "optimization": {"pragma": {"journal_mode": "DELETE", "synchronous": "FULL", "cache_size": 2000, "temp_store": "FILE"}}}, "ui": {"theme": "dark", "animations": true, "notifications": true, "autoRefresh": true, "refreshInterval": 2000, "chartUpdateInterval": 500, "maxHistoryItems": 100, "performance": {"virtualScrolling": false, "lazyLoading": false, "memoization": false, "debounceDelay": 100}}, "monitoring": {"healthChecks": true, "performanceMetrics": true, "errorReporting": false, "userAnalytics": false, "crashReporting": false, "endpoints": {"health": "/api/health", "metrics": "/api/metrics", "errors": "/api/errors"}}, "updates": {"enabled": false, "channel": "dev", "checkInterval": 3600000, "autoDownload": false, "autoInstall": false, "allowPrerelease": true, "feedUrl": "https://dev-updates.electrontrader.com"}, "paths": {"userData": "data/dev", "logs": "logs/dev", "cache": "cache/dev", "temp": "temp/dev", "backups": "backups/dev"}}