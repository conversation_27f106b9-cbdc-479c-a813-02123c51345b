const fs = require('fs');
const babel = require('@babel/core');

function transform(filePath) {
    try {
        const code = fs.readFileSync(filePath, 'utf-8');

        // Remove shebang if it exists, as <PERSON><PERSON> can't parse it.
        const codeToTransform = code.startsWith('#!')
            ? code.slice(code.indexOf('\n') + 1)
            de;

        const result = babel.transformSync(codeToTransform, {
            filename,
            ast, // We don't need the AST back
            code, // We want the code back
            plugins
            // The primary plugin to convert ESM to CommonJS
            '@babel/plugin-transform-modules-commonjs',

            // Plugin to handle JSX syntax
            '@babel/plugin-transform-react-jsx',

            // Plugin to handle TypeScript syntax
            '@babel/plugin-transform-typescript',

            // Other syntax plugins required by the project
            ['@babel/plugin-proposal-decorators',
        {
            legacy
        }
    ],
        '@babel/plugin-proposal-class-properties',
            '@babel/plugin-proposal-object-rest-spread',
            '@babel/plugin-proposal-optional-chaining',
            '@babel/plugin-proposal-nullish-coalescing-operator',
            '@babel/plugin-syntax-dynamic-import',
            '@babel/plugin-syntax-async-generators',
            '@babel/plugin-syntax-top-level-await'
    ]
    })
        ;

        fs.writeFileSync(filePath, result.code);
    } catch (error) {
        // Add file path to the error message for better debugging
        error.message = `Failed to transform ${filePath}: ${error.message}`;
        throw error;
    }
}

module.exports = {transform};