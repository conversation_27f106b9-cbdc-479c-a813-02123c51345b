import React, {useCallback, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {
  <PERSON><PERSON>,
  Badge,
  Box,
  Card,
  CardContent,
  Chip,
  Collapse,
  Divider,
  IconButton,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography
} from '@mui/material';
import {
  AttachMoney,
  Circle,
  ExpandLess,
  ExpandMore,
  Pause,
  PlayArrow,
  Refresh,
  Speed,
  Timeline,
  TrendingUp,
  Warning
} from '@mui/icons-material';
import {AnimatePresence, motion} from 'framer-motion';
import realTimeStatusService from '../services/realTimeStatusService';
import ipcService from '../services/ipcService';

const RunningOperationsMonitor = ({refreshInterval = 3000}) => {
    const [status, setStatus] = useState({
        trading: {
            activeBots: [],
            activeSignals: 0,
            pendingTrades: 0
        },
        operations: {
            whaleSignals: [],
            memeOpportunities: [],
            systemMetrics: null
        },
        system: {
            isRunning: false
        }
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [expanded, setExpanded] = useState(true);
    const [lastUpdate, setLastUpdate] = useState(null);

    // Initialize real-time status service
    useEffect(() => {
        realTimeStatusService.initialize(ipcService);

        // Subscribe to status updates
        const unsubscribe = realTimeStatusService.subscribe('operations-monitor', (newStatus) => {
            setStatus(newStatus);
            setLastUpdate(new Date());
            setLoading(false);
            setError(null);
        });

        // Start polling if not already started
        if (!realTimeStatusService.isPolling) {
            realTimeStatusService.startPolling(refreshInterval);
        }

        return () => {
            unsubscribe();
        };
    }, [refreshInterval]);

    const handleRefresh = useCallback(async () => {
        setLoading(true);
        try {
            await realTimeStatusService.forceUpdate();
        } catch (err) {
            setError(err.message || 'Failed to refresh operations data');
        } finally {
            setLoading(false);
        }
    }, []);

    const getOperationIcon = (type, operationStatus = 'active') => {
        const getColor = () => {
            switch (operationStatus) {
                case 'error':
                    return 'error';
                case 'warning':
                    return 'warning';
                case 'paused':
                    return 'disabled';
                default:
                    return 'inherit';
            }
        };

        const color = getColor();
        const fontSize = 'small';

        switch (type) {
            case 'bot':
                return <PlayArrow fontSize={fontSize} color={color}/>;
            case 'whale':
                return <TrendingUp fontSize={fontSize} color={color}/>;
            case 'meme':
                return <AttachMoney fontSize={fontSize} color={color}/>;
            case 'signal':
                return <Timeline fontSize={fontSize} color={color}/>;
            case 'system':
                return <Speed fontSize={fontSize} color={color}/>;
            default:
                return <Circle fontSize={fontSize} color={color}/>;
        }
    };

    const getOperationColor = (type, operationStatus = 'active') => {
        if (operationStatus === 'error') return 'error';
        if (operationStatus === 'warning') return 'warning';
        if (operationStatus === 'paused') return 'default';

        switch (type) {
            case 'bot':
                return 'primary';
            case 'whale':
                return 'info';
            case 'meme':
                return 'warning';
            case 'signal':
                return 'success';
            default:
                return 'default';
        }
    };

    const formatTimestamp = (timestamp) => {
        if (!timestamp) return 'N/A';
        const date = new Date(timestamp);
        return date.toLocaleTimeString();
    };

    const formatCurrency = (amount) => {
        if (typeof amount !== 'number') return 'N/A';
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    };

    const totalOperations = status.trading.activeBots.length +
        status.operations.whaleSignals.length +
        status.operations.memeOpportunities.length +
        status.trading.activeSignals +
        status.trading.pendingTrades;

    if (loading && totalOperations === 0) {
        return (
            <Card>
                <CardContent>
                    <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2}}>
                        <Typography variant="h6">
                            Running Operations
                        </Typography>
                        <motion.div
                            animate={{rotate: 360}}
                            transition={{duration: 1, repeat: Infinity, ease: 'linear'}}
                        >
                            <Circle sx={{fontSize: '1rem'}}/>
                        </motion.div>
                    </Box>
                    <LinearProgress/>
                </CardContent>
            </Card>
        );
    }

    if (error && totalOperations === 0) {
        return (
            <Card>
                <CardContent>
                    <Typography variant="h6" gutterBottom>
                        Running Operations
                    </Typography>
                    <Alert
                        severity="error"
                        action={
                            <IconButton
                                color="inherit"
                                size="small"
                                onClick={handleRefresh}
                            >
                                <Refresh/>
                            </IconButton>
                        }
                    >
                        {error}
                    </Alert>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardContent>
                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2}}>
                    <Typography variant="h6">
                        <Badge badgeContent={totalOperations} color="primary" max={99}>
                            Running Operations
                        </Badge>
                    </Typography>
                    <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                        {lastUpdate && (
                            <Typography variant="caption" color="text.secondary">
                                {lastUpdate.toLocaleTimeString()}
                            </Typography>
                        )}
                        <IconButton
                            onClick={() => setExpanded(!expanded)}
                            size="small"
                        >
                            {expanded ? <ExpandLess/> : <ExpandMore/>}
                        </IconButton>
                        <IconButton
                            onClick={handleRefresh}
                            size="small"
                            disabled={loading}
                        >
                            <Refresh/>
                        </IconButton>
                    </Box>
                </Box>

                <Collapse in={expanded}>
                    {totalOperations === 0 ? (
                        <Alert
                            severity={status.system.isRunning ? 'info' : 'warning'}
                            icon={status.system.isRunning ? undefined : <Warning/>}
                        >
                            {status.system.isRunning
                                ? 'No active operations detected. Operations may be starting up...'
                                : 'Trading system is not running. Start the system to begin operations.'
                            }
                        </Alert>
                    ) : (
                        <List dense>
                            <AnimatePresence>
                                {/* Active Bots */}
                                {status.trading.activeBots.map((bot, index) => (
                                    <motion.div
                                        key={`bot-${bot.id || index}`}
                                        initial={{opacity: 0, y: -10}}
                                        animate={{opacity: 1, y: 0}}
                                        exit={{opacity: 0, x: -20}}
                                        transition={{duration: 0.2}}
                                    >
                                        <ListItem>
                                            <ListItemIcon>
                                                {getOperationIcon('bot', bot.status)}
                                            </ListItemIcon>
                                            <ListItemText
                                                primary={
                                                    <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                                        <Typography variant="body2" fontWeight="bold">
                                                            Grid Bot: {bot.symbol || 'Unknown'}
                                                        </Typography>
                                                        <Chip
                                                            label={bot.exchange || 'N/A'}
                                                            size="small"
                                                            color={getOperationColor('bot', bot.status)}
                                                            variant="outlined"
                                                        />
                                                        {bot.status && bot.status !== 'active' && (
                                                            <Chip
                                                                label={bot.status}
                                                                size="small"
                                                                color={getOperationColor('bot', bot.status)}
                                                                variant="filled"
                                                            />
                                                        )}
                                                    </Box>
                                                }
                                                secondary={
                                                    <Typography variant="caption" color="text.secondary">
                                                        Profit: {formatCurrency(bot.profit || 0)} |
                                                        Trades: {bot.trades || 0} |
                                                        Started: {formatTimestamp(bot.startTime)}
                                                    </Typography>
                                                }
                                            />
                                        </ListItem>
                                    </motion.div>
                                ))}

                                {/* Active Signals */}
                                {status.trading.activeSignals > 0 && (
                                    <motion.div
                                        key="active-signals"
                                        initial={{opacity: 0, y: -10}}
                                        animate={{opacity: 1, y: 0}}
                                        exit={{opacity: 0, x: -20}}
                                        transition={{duration: 0.2}}
                                    >
                                        <ListItem>
                                            <ListItemIcon>
                                                {getOperationIcon('signal')}
                                            </ListItemIcon>
                                            <ListItemText
                                                primary={
                                                    <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                                        <Typography variant="body2" fontWeight="bold">
                                                            Active Trading Signals
                                                        </Typography>
                                                        <Chip
                                                            label={`${status.trading.activeSignals} signals`}
                                                            size="small"
                                                            color="success"
                                                            variant="outlined"
                                                        />
                                                    </Box>
                                                }
                                                secondary={
                                                    <Typography variant="caption" color="text.secondary">
                                                        Monitoring market conditions for trading opportunities
                                                    </Typography>
                                                }
                                            />
                                        </ListItem>
                                    </motion.div>
                                )}

                                {/* Pending Trades */}
                                {status.trading.pendingTrades > 0 && (
                                    <motion.div
                                        key="pending-trades"
                                        initial={{opacity: 0, y: -10}}
                                        animate={{opacity: 1, y: 0}}
                                        exit={{opacity: 0, x: -20}}
                                        transition={{duration: 0.2}}
                                    >
                                        <ListItem>
                                            <ListItemIcon>
                                                <Pause color="warning"/>
                                            </ListItemIcon>
                                            <ListItemText
                                                primary={
                                                    <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                                        <Typography variant="body2" fontWeight="bold">
                                                            Pending Trades
                                                        </Typography>
                                                        <Chip
                                                            label={`${status.trading.pendingTrades} pending`}
                                                            size="small"
                                                            color="warning"
                                                            variant="outlined"
                                                        />
                                                    </Box>
                                                }
                                                secondary={
                                                    <Typography variant="caption" color="text.secondary">
                                                        Trades waiting for execution or confirmation
                                                    </Typography>
                                                }
                                            />
                                        </ListItem>
                                    </motion.div>
                                )}

                                {/* Whale Signals */}
                                {status.operations.whaleSignals.slice(0, 3).map((signal, index) => (
                                    <motion.div
                                        key={`whale-${signal.id || index}`}
                                        initial={{opacity: 0, y: -10}}
                                        animate={{opacity: 1, y: 0}}
                                        exit={{opacity: 0, x: -20}}
                                        transition={{duration: 0.2}}
                                    >
                                        <ListItem>
                                            <ListItemIcon>
                                                {getOperationIcon('whale')}
                                            </ListItemIcon>
                                            <ListItemText
                                                primary={
                                                    <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                                        <Typography variant="body2" fontWeight="bold">
                                                            Whale Signal: {signal.symbol || 'Unknown'}
                                                        </Typography>
                                                        <Chip
                                                            label={signal.type || 'Signal'}
                                                            size="small"
                                                            color={getOperationColor('whale')}
                                                            variant="outlined"
                                                        />
                                                    </Box>
                                                }
                                                secondary={
                                                    <Typography variant="caption" color="text.secondary">
                                                        Value: {formatCurrency(signal.amount) || 'N/A'} |
                                                        Time: {formatTimestamp(signal.timestamp)}
                                                    </Typography>
                                                }
                                            />
                                        </ListItem>
                                    </motion.div>
                                ))}

                                {/* Meme Coin Opportunities */}
                                {status.operations.memeOpportunities.slice(0, 3).map((opportunity, index) => (
                                    <motion.div
                                        key={`meme-${opportunity.id || index}`}
                                        initial={{opacity: 0, y: -10}}
                                        animate={{opacity: 1, y: 0}}
                                        exit={{opacity: 0, x: -20}}
                                        transition={{duration: 0.2}}
                                    >
                                        <ListItem>
                                            <ListItemIcon>
                                                {getOperationIcon('meme')}
                                            </ListItemIcon>
                                            <ListItemText
                                                primary={
                                                    <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                                        <Typography variant="body2" fontWeight="bold">
                                                            Meme Opportunity: {opportunity.symbol || 'Unknown'}
                                                        </Typography>
                                                        <Chip
                                                            label={`Score: ${opportunity.score || 'N/A'}`}
                                                            size="small"
                                                            color={getOperationColor('meme')}
                                                            variant="outlined"
                                                        />
                                                    </Box>
                                                }
                                                secondary={
                                                    <Typography variant="caption" color="text.secondary">
                                                        {opportunity.reason || 'No details available'}
                                                    </Typography>
                                                }
                                            />
                                        </ListItem>
                                    </motion.div>
                                ))}

                                {/* Show more indicators */}
                                {status.operations.whaleSignals.length > 3 && (
                                    <ListItem>
                                        <ListItemText
                                            primary={
                                                <Typography variant="body2" color="text.secondary" align="center">
                                                    +{status.operations.whaleSignals.length - 3} more whale signals
                                                </Typography>
                                            }
                                        />
                                    </ListItem>
                                )}

                                {status.operations.memeOpportunities.length > 3 && (
                                    <ListItem>
                                        <ListItemText
                                            primary={
                                                <Typography variant="body2" color="text.secondary" align="center">
                                                    +{status.operations.memeOpportunities.length - 3} more meme
                                                    opportunities
                                                </Typography>
                                            }
                                        />
                                    </ListItem>
                                )}
                            </AnimatePresence>

                            {/* System Activity Summary */}
                            {status.operations.systemMetrics && (
                                <>
                                    <Divider sx={{my: 1}}/>
                                    <ListItem>
                                        <ListItemIcon>
                                            {getOperationIcon('system')}
                                        </ListItemIcon>
                                        <ListItemText
                                            primary={
                                                <Typography variant="body2" fontWeight="bold">
                                                    System Activity Summary
                                                </Typography>
                                            }
                                            secondary={
                                                <Typography variant="caption" color="text.secondary">
                                                    Uptime: {formatUptime(status.operations.systemMetrics.uptime)} |
                                                    Last
                                                    Update: {formatTimestamp(status.operations.systemMetrics.lastUpdate)}
                                                </Typography>
                                            }
                                        />
                                    </ListItem>
                                </>
                            )}
                        </List>
                    )}
                </Collapse>
            </CardContent>
        </Card>
    );

    function formatUptime(uptime) {
        if (!uptime) return 'N/A';
        const seconds = Math.floor(uptime / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}d ${hours % 24}h`;
        } else if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else if (minutes > 0) {
            return `${minutes}m`;
        } else {
            return `${seconds}s`;
        }
    }
};

RunningOperationsMonitor.propTypes = {
    refreshInterval: PropTypes.number
};

export default RunningOperationsMonitor;
