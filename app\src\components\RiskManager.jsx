// Import logger for consistent logging
import logger from '../utils/logger';

import React, {useCallback, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {
  Avatar,
  Box,
  Chip,
  CircularProgress,
  Grid,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography
} from '@mui/material';
import {CheckCircle, Security, TrendingDown, Warning} from '@mui/icons-material';
import HolographicCard from './HolographicCard';

const customWindow = window;
const electronAPI = customWindow.electronAPI || {
    getRiskMetrics: () => Promise.resolve({success: true, data: {}}),
    getSystemAlerts: () => Promise.resolve({success: true, data: []})
};

const RiskManager = () => {
    const [riskMetrics, setRiskMetrics] = useState({
        portfolioRisk: 'Low',
        portfolioScore: 25,
        marketRisk: 'Medium',
        marketScore: 45,
        liquidityRisk: 'Low',
        liquidityScore: 20,
        volatilityRisk: 'Medium',
        volatilityScore: 55,
        maxDrawdown: 0,
        currentDrawdown: 0,
        recoveryTime: 'N/A',
        valueAtRisk: 0
    });
    const [alerts, setAlerts] = useState([]);
    const [loading, setLoading] = useState(true);

    const fetchData = useCallback(async () => {
        try {
            const [metrics, alertData] = await Promise.all([
                electronAPI.getRiskMetrics?.() || Promise.resolve({success: true, data: {}}),
                electronAPI.getSystemAlerts?.() || Promise.resolve({success: true, data: []})]);

            if (metrics?.success) setRiskMetrics(prev => ({...prev, ...metrics.data}));
            if (alertData?.success) setAlerts(alertData.data || []);
        } catch (error) {
            logger.error('Failed to fetch risk data:', error);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 20000); // Refresh every 20 seconds
        return () => clearInterval(interval);
    }, [fetchData]);

    const getRiskColor = (level) => {
        switch (level?.toLowerCase()) {
            case 'high':
                return '#f44336';
            case 'medium':
                return '#ff9800';
            case 'low':
                return '#4caf50';
            default:
                return '#888';
        }
    };

    const getRiskIcon = (level) => {
        switch (level?.toLowerCase()) {
            case 'high':
                return <Warning sx={{color: '#f44336'}}/>;
            case 'medium':
                return <Warning sx={{color: '#ff9800'}}/>;
            case 'low':
                return <CheckCircle sx={{color: '#4caf50'}}/>;
            default:
                return <Security sx={{color: '#888'}}/>;
        }
    };

    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        }).format(value || 0);
    };

    if (loading) {
        return (
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh'}}>
                <CircularProgress/>
            </Box>
        );
    }

    return (
        <Box sx={{p: 3}}>
            <Typography variant="h4" sx={{color: '#f44336', fontWeight: 800, mb: 3}}>
                <Security sx={{mr: 2}}/>
                Risk Manager
            </Typography>

            <Grid container spacing={3}>
                {/* Risk Overview */}
                <Grid item xs={12} md={6}>
                    <HolographicCard variant="error" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#f44336', mb: 2}}>
                            Risk Overview
                        </Typography>
                        <Grid container spacing={2}>
                            {[
                                {
                                    label: 'Portfolio Risk',
                                    value: riskMetrics?.portfolioRisk || 'Low',
                                    score: riskMetrics?.portfolioScore || 25
                                },
                                {
                                    label: 'Market Risk',
                                    value: riskMetrics?.marketRisk || 'Medium',
                                    score: riskMetrics?.marketScore || 45
                                },
                                {
                                    label: 'Liquidity Risk',
                                    value: riskMetrics?.liquidityRisk || 'Low',
                                    score: riskMetrics?.liquidityScore || 20
                                },
                                {
                                    label: 'Volatility Risk',
                                    value: riskMetrics?.volatilityRisk || 'Medium',
                                    score: riskMetrics?.volatilityScore || 55
                                }].map((item) => (
                                <Grid item xs={12} key={item.label}>
                                    <Box sx={{display: 'flex', justifyContent: 'space-between', mb: 1}}>
                                        <Typography variant="body2" sx={{color: '#888'}}>
                                            {item.label}
                                        </Typography>
                                        <Chip
                                            label={item.value}
                                            size="small"
                                            sx={{
                                                backgroundColor: `${getRiskColor(item.value)}20`,
                                                color: getRiskColor(item.value)
                                            }}
                                        />
                                    </Box>
                                    <LinearProgress
                                        variant="determinate"
                                        value={item.score}
                                        sx={{
                                            height: 6,
                                            borderRadius: 3,
                                            backgroundColor: 'rgba(255,255,255,0.1)',
                                            '& .MuiLinearProgress-bar': {
                                                backgroundColor: getRiskColor(item.value),
                                                borderRadius: 3
                                            }
                                        }}
                                    />
                                </Grid>
                            ))}
                        </Grid>
                    </HolographicCard>
                </Grid>

                {/* Risk Alerts */}
                <Grid item xs={12} md={6}>
                    <HolographicCard variant="warning" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#ff9800', mb: 2}}>
                            Risk Alerts ({alerts.length})
                        </Typography>
                        <TableContainer>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell sx={{color: '#ff9800'}}>Alert</TableCell>
                                        <TableCell sx={{color: '#ff9800'}}>Risk</TableCell>
                                        <TableCell sx={{color: '#ff9800'}}>Time</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {alerts.map((alert, index) => (
                                        <TableRow key={index}>
                                            <TableCell sx={{color: '#fff'}}>
                                                <Box sx={{display: 'flex', alignItems: 'center'}}>
                                                    <Avatar sx={{mr: 1, bgcolor: getRiskColor(alert?.riskLevel)}}>
                                                        {getRiskIcon(alert?.riskLevel)}
                                                    </Avatar>
                                                    <Typography>{alert?.message || 'Risk Alert'}</Typography>
                                                </Box>
                                            </TableCell>
                                            <TableCell>
                                                <Chip
                                                    label={alert?.riskLevel || 'Unknown'}
                                                    size="small"
                                                    sx={{
                                                        backgroundColor: `${getRiskColor(alert?.riskLevel)}20`,
                                                        color: getRiskColor(alert?.riskLevel)
                                                    }}
                                                />
                                            </TableCell>
                                            <TableCell sx={{color: '#888'}}>
                                                {new Date(alert?.timestamp || Date.now()).toLocaleTimeString()}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </HolographicCard>
                </Grid>

                {/* Drawdown Analysis */}
                <Grid item xs={12}>
                    <HolographicCard variant="secondary" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#a259ff', mb: 2}}>
                            <TrendingDown sx={{mr: 1}}/>
                            Drawdown Analysis
                        </Typography>
                        <Grid container spacing={3}>
                            <Grid item xs={12} md={3}>
                                <Typography variant="h4" sx={{color: '#f44336', fontWeight: 800}}>
                                    {riskMetrics?.maxDrawdown || 0}%
                                </Typography>
                                <Typography variant="body2" sx={{color: '#888'}}>
                                    Max Drawdown
                                </Typography>
                            </Grid>
                            <Grid item xs={12} md={3}>
                                <Typography variant="h4" sx={{color: '#ff9800', fontWeight: 800}}>
                                    {riskMetrics?.currentDrawdown || 0}%
                                </Typography>
                                <Typography variant="body2" sx={{color: '#888'}}>
                                    Current Drawdown
                                </Typography>
                            </Grid>
                            <Grid item xs={12} md={3}>
                                <Typography variant="h4" sx={{color: '#4caf50', fontWeight: 800}}>
                                    {riskMetrics?.recoveryTime || 'N/A'}
                                </Typography>
                                <Typography variant="body2" sx={{color: '#888'}}>
                                    Recovery Time
                                </Typography>
                            </Grid>
                            <Grid item xs={12} md={3}>
                                <Typography variant="h4" sx={{color: '#00eaff', fontWeight: 800}}>
                                    {formatCurrency(riskMetrics?.valueAtRisk || 0)}
                                </Typography>
                                <Typography variant="body2" sx={{color: '#888'}}>
                                    Value at Risk (VaR)
                                </Typography>
                            </Grid>
                        </Grid>
                    </HolographicCard>
                </Grid>
            </Grid>
        </Box>
    );
};

RiskManager.propTypes = {
    riskMetrics: PropTypes.object,
    alerts: PropTypes.array
};

export default RiskManager;