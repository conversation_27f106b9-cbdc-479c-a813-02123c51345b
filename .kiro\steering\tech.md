# Technology Stack & Build System

## Core Technologies

### Frontend Stack

- **React 18.3.1** - Main UI framework with hooks and functional components
- **Material-UI (MUI) 6.1.3** - Component library with @emotion styling
- **React Router DOM 6.26.2** - Client-side routing
- **Framer Motion 11.11.8** - Animations and transitions
- **Recharts 3.1.0** - Data visualization and charting

### Backend/Trading Engine

- **Node.js 18+** - Runtime environment
- **CCXT 4.4.17** - Cryptocurrency exchange integration
- **Better SQLite3 11.3.0** / **MySQL2 3.11.3** - Database layers
- **Pino 9.4.0** - High-performance logging
- **Decimal.js 10.4.3** - Precise financial calculations
- **Technical Indicators 3.1.0** - Trading analysis

### Desktop Framework

- **Electron 31.2.1** - Cross-platform desktop application
- **Electron Builder 25.0.0** - Application packaging and distribution

### Build System

- **Webpack 5.93.0** - Module bundling with custom configuration
- **Babel 7.25.2** - JavaScript transpilation
- **PostCSS 8.4.40** + **Tailwind CSS 3.4.7** - Styling pipeline
- **React App Rewired 2.2.1** - Custom build configuration

### Testing Framework

- **Jest 29.7.0** - Unit and integration testing
- **React Testing Library 16.0.0** - Component testing
- **JSDoc 4.0.3** - Documentation generation

### Development Tools

- **ESLint 8.57.0** - Code linting with React configuration
- **Prettier 3.3.2** - Code formatting
- **Cross-env 7.0.3** - Environment variable management
- **Concurrently 8.2.2** - Parallel script execution

## Common Commands

### Development

```bash
# Start development environment
npm run dev                    # Start both React dev server and Electron
npm run start                  # Start React development server only
npm run electron-dev           # Start Electron in development mode

# Trading system
cd app/trading
npm run dev                    # Start trading system in development
npm run autonomous             # Start autonomous trading mode
```

### Building

```bash
# Frontend builds
npm run build                  # Production React build
npm run build:webpack          # Webpack production build
npm run build:production       # Optimized production build

# Electron packaging
npm run dist                   # Package for current platform
npm run dist-all               # Package for all platforms (Windows, Mac, Linux)
npm run package:win            # Windows-specific package
```

### Testing

```bash
# Frontend tests
npm run test                   # Run React tests in watch mode
npm run test:unit              # Unit tests with coverage
npm run test:e2e               # End-to-end tests
npm run test:all               # All test suites

# Trading system tests
cd app/trading
npm test                       # Jest test suite
npm run test:integration       # Integration tests
```

### Database Management

```bash
cd app/trading
npm run init-db                # Initialize database
npm run init-sqlite            # SQLite setup
npm run init-mysql             # MySQL setup
npm run apply-schema           # Apply database schema
```

### Maintenance

```bash
npm run lint                   # Lint all code
npm run lint-fix               # Auto-fix linting issues
npm run rebuild                # Rebuild native dependencies
npm run performance:analyze    # Performance analysis
npm run health-check           # System health check
```

## Build Configuration Notes

- Uses npm workspaces for monorepo structure
- Webpack configured for Electron renderer process with Node.js polyfills
- Bundle splitting optimized for trading, dashboard, and portfolio components
- TypeScript definitions included for Electron APIs
- Development server runs on port 7291 (React) with Electron integration