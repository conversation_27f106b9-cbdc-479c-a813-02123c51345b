/**
 * Enhanced Performance Monitor
 * 
 * Comprehensive performance monitoring with advanced optimizations
 * for React components, API calls, memory usage, and bundle loading.
 */

class EnhancedPerformanceMonitor {
    constructor() {
        // Performance tracking data
        this.startTime = Date.now();
        this.componentRenderTimes = new Map();
        this.apiCallTimes = new Map();
        this.memoryUsage = [];
        this.bundleLoadTimes = new Map();
        this.userInteractions = [];
        this.resourceLoadTimes = new Map();
        
        // Performance thresholds
        this.thresholds = {
            componentRender: 16, // 60fps
            apiCall: 5000, // 5 seconds
            bundleLoad: 3000, // 3 seconds
            memoryUsage: 100 * 1024 * 1024, // 100MB
            longTask: 50 // 50ms
        };
        
        this.initialized = false;
        this.observers = [];
    }

    /**
     * Initialize performance monitoring
     */
    initialize() {
        if (typeof window === 'undefined') return;
        
        try {
            // Monitor bundle loading
            this.monitorBundleLoading();
            
            // Monitor memory usage
            this.monitorMemoryUsage();
            
            // Monitor navigation timing
            this.monitorNavigationTiming();
            
            // Set up performance observer
            this.setupPerformanceObserver();
            
            // Monitor user interactions
            this.monitorUserInteractions();
            
            // Monitor resource loading
            this.monitorResourceLoading();
            
            // Start periodic reporting
            this.startPeriodicReporting();
            
            this.initialized = true;
            console.log('📊 Enhanced performance monitoring initialized');
            
            return true;
        } catch (error) {
            console.error('Failed to initialize performance monitoring:', error);
            return false;
        }
    }

    /**
     * Monitor bundle loading performance
     */
    monitorBundleLoading() {
        if (!window.performance || !window.PerformanceObserver) return;
        
        try {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'resource' && entry.name.includes('.chunk.js')) {
                        const chunkName = entry.name.split('/').pop();
                        const loadTime = entry.duration;
                        
                        this.bundleLoadTimes.set(chunkName, {
                            loadTime,
                            size: entry.transferSize,
                            timestamp: Date.now(),
                            cached: entry.transferSize === 0
                        });
                        
                        // Warn about slow bundle loading
                        if (loadTime > this.thresholds.bundleLoad) {
                            console.warn(`🐌 Slow bundle load: ${chunkName} took ${loadTime.toFixed(2)}ms`);
                        }
                    }
                }
            });
            
            observer.observe({ entryTypes: ['resource'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('Bundle loading monitoring failed:', error);
        }
    }

    /**
     * Monitor memory usage with leak detection
     */
    monitorMemoryUsage() {
        if (!window.performance || !window.performance.memory) return;
        
        const checkMemory = () => {
            const memory = window.performance.memory;
            const memoryInfo = {
                used: memory.usedJSHeapSize,
                total: memory.totalJSHeapSize,
                limit: memory.jsHeapSizeLimit,
                timestamp: Date.now()
            };
            
            this.memoryUsage.push(memoryInfo);
            
            // Keep only last 100 measurements
            if (this.memoryUsage.length > 100) {
                this.memoryUsage.shift();
            }
            
            // Detect memory leaks
            this.detectMemoryLeaks();
            
            // Warn about high memory usage
            if (memoryInfo.used > this.thresholds.memoryUsage) {
                console.warn(`🧠 High memory usage: ${this.formatBytes(memoryInfo.used)}`);
            }
        };
        
        // Check immediately and then every 30 seconds
        checkMemory();
        setInterval(checkMemory, 30000);
    }

    /**
     * Detect potential memory leaks
     */
    detectMemoryLeaks() {
        if (this.memoryUsage.length < 10) return;
        
        const recent = this.memoryUsage.slice(-10);
        const trend = recent.reduce((acc, curr, index) => {
            if (index === 0) return acc;
            return acc + (curr.used - recent[index - 1].used);
        }, 0);
        
        // If memory consistently increases over 10 measurements
        if (trend > 10 * 1024 * 1024) { // 10MB increase
            console.warn('🚨 Potential memory leak detected - memory usage trending upward');
        }
    }

    /**
     * Monitor navigation timing with Core Web Vitals
     */
    monitorNavigationTiming() {
        if (!window.performance) return;
        
        window.addEventListener('load', () => {
            setTimeout(() => {
                const timing = window.performance.timing;
                const navigationTiming = {
                    domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
                    loadComplete: timing.loadEventEnd - timing.navigationStart,
                    domInteractive: timing.domInteractive - timing.navigationStart,
                    firstPaint: 0,
                    firstContentfulPaint: 0,
                    largestContentfulPaint: 0
                };
                
                // Get paint timings
                this.getPaintTimings(navigationTiming);
                
                console.log('📊 Navigation timing:', navigationTiming);
            }, 1000);
        });
    }

    /**
     * Get paint timing metrics
     */
    getPaintTimings(navigationTiming) {
        if (!window.performance.getEntriesByType) return;
        
        const paintEntries = window.performance.getEntriesByType('paint');
        paintEntries.forEach(entry => {
            if (entry.name === 'first-paint') {
                navigationTiming.firstPaint = entry.startTime;
            } else if (entry.name === 'first-contentful-paint') {
                navigationTiming.firstContentfulPaint = entry.startTime;
            }
        });
    }

    /**
     * Set up performance observer for various metrics
     */
    setupPerformanceObserver() {
        if (!window.PerformanceObserver) return;
        
        try {
            // Observe long tasks
            const longTaskObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.duration > this.thresholds.longTask) {
                        console.warn('🐌 Long task detected:', {
                            duration: entry.duration.toFixed(2) + 'ms',
                            startTime: entry.startTime.toFixed(2) + 'ms',
                            name: entry.name
                        });
                    }
                }
            });
            longTaskObserver.observe({ entryTypes: ['longtask'] });
            this.observers.push(longTaskObserver);
            
            // Observe layout shifts
            let cumulativeLayoutShift = 0;
            const layoutShiftObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (!entry.hadRecentInput) {
                        cumulativeLayoutShift += entry.value;
                        
                        if (entry.value > 0.1) {
                            console.warn('📐 Significant layout shift:', {
                                value: entry.value.toFixed(4),
                                cumulativeScore: cumulativeLayoutShift.toFixed(4)
                            });
                        }
                    }
                }
            });
            layoutShiftObserver.observe({ entryTypes: ['layout-shift'] });
            this.observers.push(layoutShiftObserver);
            
        } catch (error) {
            console.warn('Performance observer setup failed:', error);
        }
    }

    /**
     * Monitor user interactions for responsiveness
     */
    monitorUserInteractions() {
        if (typeof window === 'undefined') return;
        
        const interactionTypes = ['click', 'keydown', 'scroll', 'touchstart'];
        
        interactionTypes.forEach(type => {
            window.addEventListener(type, (event) => {
                const startTime = performance.now();
                
                // Use requestIdleCallback to measure response time
                if (window.requestIdleCallback) {
                    requestIdleCallback(() => {
                        const responseTime = performance.now() - startTime;
                        
                        this.userInteractions.push({
                            type,
                            responseTime,
                            timestamp: Date.now(),
                            target: event.target.tagName
                        });
                        
                        // Keep only last 50 interactions
                        if (this.userInteractions.length > 50) {
                            this.userInteractions.shift();
                        }
                        
                        // Warn about slow interactions
                        if (responseTime > 100) { // 100ms threshold
                            console.warn(`🐌 Slow interaction: ${type} took ${responseTime.toFixed(2)}ms`);
                        }
                    });
                }
            }, { passive: true });
        });
    }

    /**
     * Monitor resource loading performance
     */
    monitorResourceLoading() {
        if (!window.performance || !window.PerformanceObserver) return;
        
        try {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.entryType === 'resource') {
                        const resourceType = this.getResourceType(entry.name);
                        const loadTime = entry.duration;
                        
                        if (!this.resourceLoadTimes.has(resourceType)) {
                            this.resourceLoadTimes.set(resourceType, []);
                        }
                        
                        this.resourceLoadTimes.get(resourceType).push({
                            name: entry.name.split('/').pop(),
                            loadTime,
                            size: entry.transferSize,
                            cached: entry.transferSize === 0,
                            timestamp: Date.now()
                        });
                        
                        // Keep only last 20 resources per type
                        const resources = this.resourceLoadTimes.get(resourceType);
                        if (resources.length > 20) {
                            resources.shift();
                        }
                    }
                }
            });
            
            observer.observe({ entryTypes: ['resource'] });
            this.observers.push(observer);
        } catch (error) {
            console.warn('Resource loading monitoring failed:', error);
        }
    }

    /**
     * Get resource type from URL
     */
    getResourceType(url) {
        if (url.includes('.js')) return 'javascript';
        if (url.includes('.css')) return 'stylesheet';
        if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image';
        if (url.match(/\.(woff|woff2|ttf|eot)$/)) return 'font';
        return 'other';
    }

    /**
     * Start periodic performance reporting
     */
    startPeriodicReporting() {
        // Log performance summary every 5 minutes
        setInterval(() => {
            this.logPerformanceSummary();
        }, 300000);
    }

    /**
     * Track component render time
     */
    trackComponentRender(componentName, renderTime, phase = 'mount') {
        if (!this.componentRenderTimes.has(componentName)) {
            this.componentRenderTimes.set(componentName, {
                renders: [],
                totalRenders: 0,
                averageRenderTime: 0,
                slowRenders: 0
            });
        }
        
        const stats = this.componentRenderTimes.get(componentName);
        stats.renders.push({
            duration: renderTime,
            phase,
            timestamp: Date.now()
        });
        
        stats.totalRenders++;
        
        // Calculate average
        const totalTime = stats.renders.reduce((sum, r) => sum + r.duration, 0);
        stats.averageRenderTime = totalTime / stats.renders.length;
        
        // Track slow renders
        if (renderTime > this.thresholds.componentRender) {
            stats.slowRenders++;
            console.warn(`🐌 Slow render detected for ${componentName}: ${renderTime.toFixed(2)}ms (${phase})`);
        }
        
        // Keep only last 50 measurements
        if (stats.renders.length > 50) {
            stats.renders.shift();
        }
    }

    /**
     * Track API call performance
     */
    trackApiCall(endpoint, duration, success = true, options = {}) {
        if (!this.apiCallTimes.has(endpoint)) {
            this.apiCallTimes.set(endpoint, {
                calls: [],
                totalCalls: 0,
                successfulCalls: 0,
                failedCalls: 0,
                averageDuration: 0,
                p95Duration: 0,
                slowCalls: 0
            });
        }
        
        const stats = this.apiCallTimes.get(endpoint);
        stats.calls.push({
            duration,
            success,
            timestamp: Date.now(),
            cached: options.cached || false,
            retries: options.retries || 0
        });
        
        stats.totalCalls++;
        if (success) {
            stats.successfulCalls++;
        } else {
            stats.failedCalls++;
        }
        
        // Calculate metrics
        const durations = stats.calls.map(c => c.duration).sort((a, b) => a - b);
        const totalDuration = durations.reduce((sum, d) => sum + d, 0);
        stats.averageDuration = totalDuration / durations.length;
        stats.p95Duration = durations[Math.floor(durations.length * 0.95)] || 0;
        
        // Track slow calls
        if (duration > this.thresholds.apiCall) {
            stats.slowCalls++;
            console.warn(`🐌 Slow API call detected for ${endpoint}: ${duration.toFixed(2)}ms`);
        }
        
        // Keep only last 100 calls
        if (stats.calls.length > 100) {
            stats.calls.shift();
        }
    }

    /**
     * Get comprehensive performance report
     */
    getPerformanceReport() {
        return {
            uptime: Date.now() - this.startTime,
            componentRenderTimes: this.getComponentRenderStats(),
            apiCallTimes: this.getApiCallStats(),
            bundleLoadTimes: Object.fromEntries(this.bundleLoadTimes),
            resourceLoadTimes: this.getResourceLoadStats(),
            memoryUsage: this.memoryUsage.slice(-10),
            userInteractions: this.getUserInteractionStats(),
            timestamp: Date.now()
        };
    }

    /**
     * Get component render statistics
     */
    getComponentRenderStats() {
        const stats = {};
        for (const [component, data] of this.componentRenderTimes) {
            stats[component] = {
                totalRenders: data.totalRenders,
                averageRenderTime: data.averageRenderTime.toFixed(2) + 'ms',
                slowRenders: data.slowRenders,
                slowRenderRate: (data.slowRenders / data.totalRenders * 100).toFixed(1) + '%'
            };
        }
        return stats;
    }

    /**
     * Get API call statistics
     */
    getApiCallStats() {
        const stats = {};
        for (const [endpoint, data] of this.apiCallTimes) {
            stats[endpoint] = {
                totalCalls: data.totalCalls,
                successRate: (data.successfulCalls / data.totalCalls * 100).toFixed(1) + '%',
                averageDuration: data.averageDuration.toFixed(2) + 'ms',
                p95Duration: data.p95Duration.toFixed(2) + 'ms',
                slowCallRate: (data.slowCalls / data.totalCalls * 100).toFixed(1) + '%'
            };
        }
        return stats;
    }

    /**
     * Get resource loading statistics
     */
    getResourceLoadStats() {
        const stats = {};
        for (const [type, resources] of this.resourceLoadTimes) {
            const totalTime = resources.reduce((sum, r) => sum + r.loadTime, 0);
            const cachedCount = resources.filter(r => r.cached).length;
            
            stats[type] = {
                totalResources: resources.length,
                averageLoadTime: (totalTime / resources.length).toFixed(2) + 'ms',
                cacheHitRate: (cachedCount / resources.length * 100).toFixed(1) + '%'
            };
        }
        return stats;
    }

    /**
     * Get user interaction statistics
     */
    getUserInteractionStats() {
        if (this.userInteractions.length === 0) return {};
        
        const totalResponseTime = this.userInteractions.reduce((sum, i) => sum + i.responseTime, 0);
        const slowInteractions = this.userInteractions.filter(i => i.responseTime > 100).length;
        
        return {
            totalInteractions: this.userInteractions.length,
            averageResponseTime: (totalResponseTime / this.userInteractions.length).toFixed(2) + 'ms',
            slowInteractionRate: (slowInteractions / this.userInteractions.length * 100).toFixed(1) + '%'
        };
    }

    /**
     * Log performance summary
     */
    logPerformanceSummary() {
        const report = this.getPerformanceReport();
        
        console.group('📊 Enhanced Performance Summary');
        console.log('Uptime:', this.formatDuration(report.uptime));
        
        // Component render performance
        if (Object.keys(report.componentRenderTimes).length > 0) {
            console.group('Component Render Performance');
            for (const [component, stats] of Object.entries(report.componentRenderTimes)) {
                console.log(`${component}: ${stats.averageRenderTime} avg, ${stats.slowRenderRate} slow renders`);
            }
            console.groupEnd();
        }
        
        // API call performance
        if (Object.keys(report.apiCallTimes).length > 0) {
            console.group('API Call Performance');
            for (const [endpoint, stats] of Object.entries(report.apiCallTimes)) {
                console.log(`${endpoint}: ${stats.averageDuration} avg, ${stats.successRate} success, P95: ${stats.p95Duration}`);
            }
            console.groupEnd();
        }
        
        // Memory usage
        if (this.memoryUsage.length > 0) {
            const latestMemory = this.memoryUsage[this.memoryUsage.length - 1];
            console.log('Memory Usage:', {
                used: this.formatBytes(latestMemory.used),
                total: this.formatBytes(latestMemory.total),
                limit: this.formatBytes(latestMemory.limit)
            });
        }
        
        console.groupEnd();
    }

    /**
     * Format duration in human readable format
     */
    formatDuration(ms) {
        if (ms < 1000) return `${ms}ms`;
        if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
        if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
        return `${(ms / 3600000).toFixed(1)}h`;
    }

    /**
     * Format bytes in human readable format
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Cleanup and destroy monitor
     */
    destroy() {
        // Disconnect all observers
        this.observers.forEach(observer => {
            try {
                observer.disconnect();
            } catch (error) {
                console.warn('Failed to disconnect observer:', error);
            }
        });
        
        this.observers = [];
        this.initialized = false;
        
        console.log('📊 Performance monitor destroyed');
    }
}

// Create singleton instance
const enhancedPerformanceMonitor = new EnhancedPerformanceMonitor();

export default enhancedPerformanceMonitor;