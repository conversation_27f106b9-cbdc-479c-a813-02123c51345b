{"tasks": [{"label": "npm: start", "type": "npm", "command": "npm", "args": ["run", "start"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: dev", "type": "npm", "command": "npm", "args": ["run", "dev"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: build", "type": "npm", "command": "npm", "args": ["run", "build"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$tsc"}, {"label": "npm: test", "type": "npm", "command": "npm", "args": ["run", "test"], "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: lint", "type": "npm", "command": "npm", "args": ["run", "lint"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: postinstall", "type": "npm", "command": "npm", "args": ["run", "postinstall"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "npm: rebuild", "type": "npm", "command": "npm", "args": ["run", "rebuild"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "ESLint", "type": "shell", "command": "npx", "args": ["eslint", ".", "--ext", ".js,.jsx,.ts,.tsx"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$eslint-stylish"}], "launch": [{"name": "Debug Node.js with <PERSON><PERSON><PERSON>", "type": "node", "request": "launch", "program": "${workspaceFolder}/app/main.js", "console": "integratedTerminal", "restart": true, "runtimeExecutable": "node", "skipFiles": ["<node_internals>/**", "node_modules/**"], "env": {"NODE_ENV": "development"}, "internalConsoleOptions": "openOnSessionStart", "outputCapture": "std"}, {"name": "Debug Current File", "type": "node", "request": "launch", "program": "${file}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**", "node_modules/**"]}, {"name": "Attach to Process", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}", "remoteRoot": null}], "settings": {"sonarlint.connectedMode.project": {"connectionId": "SonarLint-Visual Studio Code 8", "projectKey": "xphoenix1996_electron-app"}, "python-envs.pythonProjects": [{"path": "", "envManager": "ms-python.python:venv", "packageManager": "ms-python.python:pip"}], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.format.enable": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": true, "files.eol": "\n", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true}}, "extensions": {"recommendations": ["dbaeumer.vscode-eslint", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode-remote.remote-containers", "ms-vscode.vscode-markdown", "christian-kohler.path-intellisense", "formulahendry.auto-rename-tag"]}}