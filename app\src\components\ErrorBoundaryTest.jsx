import React, {useState} from 'react';
import {<PERSON>, But<PERSON>, Card, CardContent, Typography} from '@mui/material';
import {BugReport} from '@mui/icons-material';
import ErrorBoundary from './ErrorBoundary';

// Component that throws an error for testing
const ErrorThrower = () => {
    const [shouldError, setShouldError] = useState(false);

    if (shouldError) {
        throw new Error('This is a test error thrown by the ErrorThrower component');
    }

    return (
        <Card sx={{mb: 3, backgroundColor: 'rgba(255, 255, 255, 0.1)'}}>
            <CardContent>
                <Typography variant="h6" sx={{color: '#00eaff', mb: 2}}>
                    Error Boundary Test
                </Typography>
                <Typography variant="body2" sx={{color: 'rgba(255, 255, 255, 0.7)', mb: 2}}>
                    This component demonstrates error boundary functionality. Click the button below to trigger a test
                    error.
                </Typography>
                <Button
                    variant="contained"
                    startIcon={<BugReport/>}
                    onClick={() => setShouldError(true)}
                    sx={{
                        backgroundColor: '#f44336',
                        color: 'white',
                        '&:hover': {backgroundColor: '#d32f2f'}
                    }}
                >
                    Trigger Test Error
                </Button>
            </CardContent>
        </Card>
    );
};

// Component that demonstrates nested error boundaries
const NestedComponent = () => {
    const [counter, setCounter] = useState(0);

    // This will throw an error when counter reaches 3
    if (counter >= 3) {
        throw new Error('Counter limit exceeded');
    }

    return (
        <Card sx={{mb: 3, backgroundColor: 'rgba(0, 234, 255, 0.1)'}}>
            <CardContent>
                <Typography variant="h6" sx={{color: '#00eaff', mb: 2}}>
                    Nested Error Boundary Test
                </Typography>
                <Typography variant="body1" sx={{color: 'white', mb: 2}}>
                    Counter: {counter}
                </Typography>
                <Typography variant="body2" sx={{color: 'rgba(255, 255, 255, 0.7)', mb: 2}}>
                    Click increment until counter reaches 3 to trigger an error.
                </Typography>
                <Button
                    variant="contained"
                    onClick={() => setCounter(counter + 1)}
                    sx={{
                        backgroundColor: '#00eaff',
                        color: '#0f0f23',
                        '&:hover': {backgroundColor: '#00b8cc'}
                    }}
                >
                    Increment Counter ({counter})
                </Button>
            </CardContent>
        </Card>
    );
};

// Main error boundary test component
const ErrorBoundaryTest = () => {
    const [resetKey, setResetKey] = useState(0);

    const handleReset = () => {
        setResetKey(prev => prev + 1);
    };

    return (
        <Box sx={{p: 3, maxWidth: 800, margin: '0 auto'}}>
            <Typography variant="h4" sx={{color: '#00eaff', mb: 3, textAlign: 'center'}}>
                Error Boundary Testing
            </Typography>

            <Typography variant="body1" sx={{color: 'rgba(255, 255, 255, 0.8)', mb: 4, textAlign: 'center'}}>
                This page demonstrates the error boundary system in action. Each component below is wrapped in an error
                boundary that catches and displays errors gracefully.
            </Typography>

            {/* Test 1: Basic error boundary */}
            <ErrorBoundary componentName="Basic Error Thrower">
                <ErrorThrower key={resetKey}/>
            </ErrorBoundary>

            {/* Test 2: Nested error boundary */}
            <ErrorBoundary componentName="Nested Counter">
                <NestedComponent key={`nested-${resetKey}`}/>
            </ErrorBoundary>

            {/* Test 3: Multiple error boundaries */}
            <Box sx={{display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2, mt: 3}}>
                <ErrorBoundary componentName="Left Component">
                    <Card sx={{backgroundColor: 'rgba(76, 175, 80, 0.1)'}}>
                        <CardContent>
                            <Typography variant="h6" sx={{color: '#4caf50'}}>
                                Left Component
                            </Typography>
                            <Typography variant="body2" sx={{color: 'rgba(255, 255, 255, 0.7)'}}>
                                This component is protected by its own error boundary.
                            </Typography>
                        </CardContent>
                    </Card>
                </ErrorBoundary>

                <ErrorBoundary componentName="Right Component">
                    <Card sx={{backgroundColor: 'rgba(255, 152, 0, 0.1)'}}>
                        <CardContent>
                            <Typography variant="h6" sx={{color: '#ff9800'}}>
                                Right Component
                            </Typography>
                            <Typography variant="body2" sx={{color: 'rgba(255, 255, 255, 0.7)'}}>
                                This component has separate error handling.
                            </Typography>
                        </CardContent>
                    </Card>
                </ErrorBoundary>
            </Box>

            {/* Reset button */}
            <Box sx={{textAlign: 'center', mt: 4}}>
                <Button
                    variant="outlined"
                    onClick={handleReset}
                    sx={{
                        color: '#00eaff',
                        borderColor: '#00eaff',
                        textTransform: 'none',
                        '&:hover': {borderColor: '#00eaff', backgroundColor: 'rgba(0, 234, 255, 0.1)'}
                    }}
                >
                    Reset All Components
                </Button>
            </Box>

            <Typography variant="caption"
                        sx={{color: 'rgba(255, 255, 255, 0.5)', display: 'block', textAlign: 'center', mt: 3}}>
                Note: All errors are caught by error boundaries and logged to the console for debugging.
            </Typography>
        </Box>
    );
};

export default ErrorBoundaryTest;
