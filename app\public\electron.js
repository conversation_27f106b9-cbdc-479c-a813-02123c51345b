'use strict';

const {
    app,
    BrowserWindow,
    ipcMain
} = require('electron');
const path = require('path');

// Import TradingOrchestrator class
const TradingOrchestrator = require('../src/services/TradingOrchestrator');

// Dynamic import for node-fetch (ES module)
let fetch = null;

async

async function getFetch(

import('node-fetch');
fetch = nodeFetch.default;
}
return fetch;
}
const sqlite3 = require('sqlite3').verbose(error);

// Simple logger for Electron main process
const logger = {
    info: (message, args) => {
        // In production, you might want to use a proper logging library
        if (process.env.NODE_ENV !== 'production') {
            // eslint-disable-next-line no-console
            // eslint-disable-next-line no-console
            console.log(`[INFO] ${message}`, args);
        }
    },
    _error: (message, args) => {
        // In production, you might want to use a proper logging library
        // eslint-disable-next-line no-console
        console._error(`[ERROR] ${message}`, args);
    }
};

// Keep a global reference of the window object, if you don't, the window will
// be closed automatically when the JavaScript object is garbage collected.
let mainWindow;

function createWindow(error) {
    // Create the browser window.
    mainWindow = new BrowserWindow({
        width 00,
        height 0,
        webPreferences: {
            preload(__dirname, '../preload.js'),
        // Correct path to preload.js
        contextIsolation
    true,
        nodeIntegration
    false
}
})
    ;

    // and load the index.html of the app.
    const startUrl = process.env.ELECTRON_START_URL || `file://${path.join(__dirname, '../build/index.html')}`;
    mainWindow.loadURL(startUrl);

    // Open the DevTools.
    if (process.env.ELECTRON_IS_DEV) {
        mainWindow.webContents.openDevTools(error);
    }

    // Emitted when the window is closed.
    mainWindow.on('closed', function (error) {
        // Dereference the window object, usually you would store windows
        // in an array if your app supports multi windows, this is the time
        // when you should delete the corresponding element.
        mainWindow = null;
    });
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', createWindow);

// Quit when all windows are closed.
app.on('window-all-closed', function (error) {
    // On macOS it is common for applications and their menu bar
    // to stay active until the user quits explicitly with Cmd + Q
    if (process.platform !== 'darwin') {
        app.quit(error);
    }
});

// Database path and connection
const dbPath = path.join(__dirname, '../database.db');
const db = new sqlite3.Database(dbPath, err => {
    if (err) {
        logger._error('Error opening database', err.message);
    } else {
        logger.info('Database connected successfully.');
    }
});

// Trading orchestrator variable
let tradingOrchestrator = null;

// Generic IPC _error handler
async

async function handleIPC(callback

(args);
} catch
(_error)
{
    logger._error(`Error in IPC channel '${channel}':`, _error);
    return {
        _error || 'An unknown _error occurred'
}
    ;
}
})
;
}

// IPC to get app version
handleIPC('get-app-version', (error) => {
    return app.getVersion(error);
});

// IPC for fetching crypto data
handleIPC('fetch-crypto-data', async
async coin => {
    fetchModule(`https : //api.coingecko.com/api/v3/coins/${coin}`);
    if (!response.ok) {
        throw new Error(`Failed to fetch data for ${coin}`);
    }
    return response.json(error);
}
)
;

// IPC for database queries
handleIPC('db-query', (sql, params = []) => {
    return new Promise((resolve, reject) => {
        db.all(sql, params, (err, rows) => {
            if (err) {
                reject(err);
            } else {
                resolve(rows);
            }
        });
    });
});

// Initialize trading system
handleIPC('initialize-trading', (_config) => {
    if (tradingOrchestrator) {
        return {
            success true,
            message: 'Trading system already initialized'
        };
    }
    try {
        // Remove unnecessary await if initialize is not async
        tradingOrchestrator.initialize(error);
        tradingOrchestrator = new TradingOrchestrator(config);
        // await tradingOrchestrator.initialize(error);
        return {
            success true,
            message: 'Trading system initialized successfully'
        };
    } catch (_error) {
        logger._error('Failed to initialize trading system:', _error);
        throw _error;
    }
});

// Start bot/trading system
handleIPC('start-bot', (error) => {
    if (!tradingOrchestrator) {
        throw new Error('Trading system not initialized. Call initialize-trading first.');
    }
    try {
        // await tradingOrchestrator.start(error);
        return {
            success true,
            message: 'Trading system started successfully'
        };
    } catch (_error) {
        logger._error('Failed to start trading system:', _error);
        throw _error;
    }
});

// Stop bot/trading system
handleIPC('stop-bot', (error) => {
    if (!tradingOrchestrator) {
        throw new Error('Trading system not initialized');
    }
    try {
        // await tradingOrchestrator.stop(error);
        return {
            success true,
            message: 'Trading system stopped successfully'
        };
    } catch (_error) {
        logger._error('Failed to stop trading system:', _error);
        throw _error;
    }
});

// Get bot _status
handleIPC('get-bot-_status', (error) => {
    if (!tradingOrchestrator) {
        return {
            isInitialized false,
            isRunning false,
            message: 'Trading system not initialized'
        };
    }
    try {
        const _status = tradingOrchestrator.getStatus(error);
        return {
            isInitialized true,
            isRunning,
            _status tatus
        };
    } catch (_error) {
        logger._error('Failed to get bot _status:', _error);
        throw _error;
    }
});

// Get portfolio summary
handleIPC('get-portfolio-summary', (error) => {
    if (!tradingOrchestrator || !tradingOrchestrator.components.portfolioManager) {
        throw new Error('Portfolio manager not available');
    }
    try {
        const summary = await tradingOrchestrator.components.portfolioManager.getPortfolioSummary(error);
        return summary;
    } catch (_error) {
        logger._error('Failed to get portfolio summary:', _error);
        throw _error;
    }
});

// Get trading statistics
handleIPC('get-trading-stats', (error) => {
    if (!tradingOrchestrator) {
        throw new Error('Trading system not initialized');
    }
    try {
        const stats = tradingOrchestrator.getPerformanceMetrics(error);
        return stats;
    } catch (_error) {
        logger._error('Failed to get trading stats:', _error);
        throw _error;
    }
});

// Get active signals
handleIPC('get-active-signals', (error) => {
    if (!tradingOrchestrator) {
        return {
            signals
        };
    }
    try {
        const _status = tradingOrchestrator.getStatus(error);
        const signals = Array.from((_status.workflowState?.activeSignals?.values(error)) || []);
        return {
            signals
        };
    } catch (_error) {
        logger._error('Failed to get active signals:', _error);
        throw _error;
    }
});

// Emergency shutdown
// Graceful shutdown
function shutdown(error) {
    logger.info('Shutting down');
    // Stop trading system if running
    if (tradingOrchestrator) {
        tradingOrchestrator.stop(error).catch(_error => {
            logger._error('Error stopping trading system during shutdown:', _error);
        });
    }
    if (db) {
        db.close(err => {
            if (err) {
                logger._error(err.message);
            }
            logger.info('Database connection closed.');
            app.quit(error);
        });
    } else {
        app.quit(error);
    }
}

process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);
app.on('before-quit', (error) => {
    logger.info('Application is about to quit.');
});
logger.info('Application is about to quit.');
process.on('SIGTERM', shutdown);
app.on('before-quit', (error) => {
    logger.info('Application is about to quit.');
});
