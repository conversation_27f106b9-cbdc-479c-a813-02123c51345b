'use strict';

const logger = require('./logger.js');

/**
 * @fileoverview Performance Monitoring Utility
 * @description Provides tools to monitor component load times, memory usage, and bundle sizes.
 * It includes a React hook for easy integration into components and a reporting mechanism.
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-22
 */

// React imports for hook functionality - loaded conditionally
let useEffect, useRef, useState;
try {
    const React = require('react');
    useEffect = React.useEffect;
    useRef = React.useRef;
    useState = React.useState;
} catch (error) {
    // React not available - hooks will be disabled
    logger.warn('React not available - performance monitoring hooks disabled');
}

class PerformanceMonitor {
  constructor() {
    /** @type {Map<string, number>} */
    this.componentLoadTimes = new Map();
    /** @type {Object} */
    this.bundleMetrics = { initial: 0, current: 0 };
  }

  /**
   * Measures the load time of a component and logs it.
   * @param {string} componentName - The name of the component being measured.
   * @param {number} startTime - The timestamp when loading started.
   * @returns {number} The load time in milliseconds.
   */
  measureComponentLoad(componentName, startTime) {
    const loadTime = performance.now() - startTime;
    this.componentLoadTimes.set(componentName, loadTime);

    if (process.env.NODE_ENV === 'development') {
      logger.info(`Component ${componentName} loaded in ${loadTime.toFixed(2)}ms`);
    }
    return loadTime;
  }

  /**
   * Retrieves the load times for all monitored components.
   * @returns {Object<string, {loadTime: number, timestamp: string}>}
   */
  getComponentMetrics() {
    const metrics = {};
    this.componentLoadTimes.forEach((time, component) => {
      metrics[component] = {
        loadTime: time,
        timestamp: new Date().toISOString(),
      };
    });
    return metrics;
  }

  /**
   * Calculates the size reduction between two bundle sizes.
   * @param {number} originalSize - The original bundle size in bytes.
   * @param {number} newSize - The new bundle size in bytes.
   * @returns {{originalSize: string, newSize: string, reduction: string}}
   */
  calculateBundleReduction(originalSize, newSize) {
    const reduction = ((originalSize - newSize) / originalSize) * 100;
    return {
      originalSize: `${(originalSize / 1024 / 1024).toFixed(2)} MB`,
      newSize: `${(newSize / 1024 / 1024).toFixed(2)} MB`,
      reduction: `${reduction.toFixed(2)}%`,
    };
  }

  /**
   * Retrieves the current memory usage from the performance API.
   * @returns {{used: string, total: string, limit: string} | null}
   */
  getMemoryUsage() {
    try {
      // @ts-ignore - Handle browser compatibility
      if (typeof performance !== 'undefined' && performance.memory) {
        // @ts-ignore
        const memory = performance.memory;
        return {
          used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
          limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
        };
      }
    } catch (error) {
      logger.warn('Memory usage monitoring not available:', error);
    }
    return null;
  }

  /**
   * Checks if performance metrics are within predefined budgets.
   * @param {Object} metrics - The performance metrics to check.
   * @param {Object} budgets - The performance budgets to check against.
   * @returns {{isWithinBudget: boolean, violations: string[]}}
   */
  checkPerformanceBudget(metrics, budgets) {
    const effectiveBudgets = {
      initialLoad: 3000, // ms
      componentLoad: 500, // ms
      bundleSize: 1.5 * 1024 * 1024, // 1.5 MB
      memoryUsage: 100, // MB
      ...budgets,
    };
    const violations = [];

    if (metrics.initialLoad > effectiveBudgets.initialLoad) {
      violations.push(`Initial load time: ${metrics.initialLoad}ms > ${effectiveBudgets.initialLoad}ms`);
    }

    Object.entries(metrics.componentLoadTimes || {}).forEach(([component, time]) => {
      if (time > effectiveBudgets.componentLoad) {
        violations.push(`${component} load time: ${time}ms > ${effectiveBudgets.componentLoad}ms`);
      }
    });

    return {
      isWithinBudget: violations.length === 0,
      violations,
    };
  }
}

const performanceMonitorInstance = new PerformanceMonitor();

/**
 * React hook to monitor the performance of a component.
 * @param {string} componentName - The name of the component to monitor.
 * @returns {{loadTime: number | null, renderTime: number | null, memory: Object | null}}
 */
const usePerformanceMonitor = componentName => {
  const [metrics, setMetrics] = useState({
    loadTime: null,
    renderTime: null,
    memory: null,
  });
  const startTimeRef = useRef(null);
  const renderStartRef = useRef(null);

  useEffect(() => {
    if (componentName) {
      startTimeRef.current = performance.now();
      const loadTimer = setTimeout(() => {
        const loadTime = performanceMonitorInstance.measureComponentLoad(componentName, startTimeRef.current);
        setMetrics(prev => ({
          ...prev,
          loadTime,
          memory: performanceMonitorInstance.getMemoryUsage(),
        }));
      }, 100); // Delay to ensure component is fully loaded
      return () => clearTimeout(loadTimer);
    }
  }, [componentName]);

  useEffect(() => {
    renderStartRef.current = performance.now();
    const renderTimer = setTimeout(() => {
      const renderTime = performance.now() - renderStartRef.current;
      setMetrics(prev => ({ ...prev, renderTime }));
    }, 0); // Defer to after the render cycle
    return () => clearTimeout(renderTimer);
  });

  return metrics;
};

/**
 * Utility to analyze the webpack bundle.
 */
const BundleAnalyzer = {
  async analyzeBundle() {
    if (process.env.NODE_ENV === 'development') {
      try {
        const response = await fetch('/bundle-analysis.html');
        if (response.ok) {
          const html = await response.text();
          const blob = new Blob([html], { type: 'text/html' });
          const url = URL.createObjectURL(blob);
          window.open(url, '_blank');
        }
      } catch (error) {
        logger.warn('Bundle analysis not available:', error);
      }
    }
  },
  getBundleStats: () => {
    return {
      totalSize: performanceMonitorInstance.bundleMetrics.current,
      componentLoadTimes: performanceMonitorInstance.getComponentMetrics(),
      memoryUsage: performanceMonitorInstance.getMemoryUsage(),
    };
  },
};

/**
 * Utility to generate and log a performance report.
 */
const PerformanceReporter = {
  report: () => {
    const componentMetrics = performanceMonitorInstance.getComponentMetrics();
    const report = {
      timestamp: new Date().toISOString(),
      componentLoadTimes: componentMetrics,
      memoryUsage: performanceMonitorInstance.getMemoryUsage(),
      bundleMetrics: performanceMonitorInstance.bundleMetrics,
      performanceBudget: performanceMonitorInstance.checkPerformanceBudget({
        componentLoadTimes: componentMetrics,
      }),
    };

    if (process.env.NODE_ENV === 'development') {
      logger.info('--- Performance Report ---');
      logger.info('Component Load Times:', report.componentLoadTimes);
      logger.info('Memory Usage:', report.memoryUsage);
      logger.info('Performance Budget:', report.performanceBudget);
      logger.info('--------------------------');
    }
    return report;
  },
};

module.exports = {
  PerformanceMonitor: performanceMonitorInstance,
  usePerformanceMonitor,
  BundleAnalyzer,
  PerformanceReporter,
};