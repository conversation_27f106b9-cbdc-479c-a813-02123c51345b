/**
 * Start Button Component Integration Test
 * Tests the Start button component with actual TradingOrchestrator integration
 */

import React from 'react';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import StartButton from '../StartButton';
import ipcService from '../../services/ipcService';

// Mock IPC service
jest.mock('../../services/ipcService');

describe('StartButton Integration', () => {
    let mockShowNotification;
    let mockOnStatusChange;

    beforeEach(() => {
        jest.clearAllMocks();
        mockShowNotification = jest.fn();
        mockOnStatusChange = jest.fn();

        // Default mock implementations
        ipcService.getRealTimeStatus.mockResolvedValue({
            success: true,
            data: {isRunning: false, health: 'healthy'}
        });

        ipcService.getSystemHealth.mockResolvedValue({
            success: true,
            data: {status: 'healthy', components: {}}
        });
    });

    describe('Initial State and Status Check', () => {
        test('should check bot status on mount', async () => {
            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            await waitFor(() => {
                expect(ipcService.getRealTimeStatus).toHaveBeenCalled();
                expect(ipcService.getSystemHealth).toHaveBeenCalled();
            });
        });

        test('should display correct initial state when system is stopped', async () => {
            ipcService.getRealTimeStatus.mockResolvedValue({
                success: true,
                data: {isRunning: false, health: 'healthy'}
            });

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            await waitFor(() => {
                expect(screen.getByRole('button')).toBeInTheDocument();
                expect(screen.getByRole('button')).not.toBeDisabled();
            });
        });

        test('should display running state when system is active', async () => {
            ipcService.getRealTimeStatus.mockResolvedValue({
                success: true,
                data: {isRunning: true, health: 'healthy'}
            });

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            await waitFor(() => {
                expect(mockOnStatusChange).toHaveBeenCalledWith({
                    isRunning: true,
                    health: 'healthy'
                });
            });
        });
    });

    describe('Start Button Workflow', () => {
        test('should successfully start trading system', async () => {
            // Mock successful start
            ipcService.startBot.mockResolvedValue({
                success: true,
                data: {running: true, initialized: true}
            });

            ipcService.getRealTimeStatus
                .mockResolvedValueOnce({
                    success: true,
                    data: {isRunning: false, health: 'healthy'}
                })
                .mockResolvedValueOnce({
                    success: true,
                    data: {isRunning: true, health: 'healthy'}
                });

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            const startButton = await screen.findByRole('button');
            fireEvent.click(startButton);

            await waitFor(() => {
                expect(ipcService.startBot).toHaveBeenCalled();
            });

            await waitFor(() => {
                expect(mockShowNotification).toHaveBeenCalledWith(
                    'Trading system started successfully',
                    'success',
                );
            });
        });

        test('should handle startup errors gracefully', async () => {
            const errorMessage = 'Database connection failed';

            ipcService.startBot.mockResolvedValue({
                success: false,
                error: {
                    message: errorMessage,
                    code: 'DATABASE_ERROR'
                }
            });

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            const startButton = await screen.findByRole('button');
            fireEvent.click(startButton);

            await waitFor(() => {
                expect(mockShowNotification).toHaveBeenCalledWith(
                    `Failed to start trading system: ${errorMessage}`,
                    'error',
                );
            });

            // Should display error in UI
            await waitFor(() => {
                expect(screen.getByText(errorMessage)).toBeInTheDocument();
            });
        });

        test('should show startup progress during initialization', async () => {
            // Mock delayed start to simulate startup process
            ipcService.startBot.mockImplementation(() =>
                new Promise(resolve => {
                    setTimeout(() => {
                        resolve({
                            success: true,
                            data: {running: true}
                        });
                    }, 1000);
                }),
            );

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            const startButton = await screen.findByRole('button');
            fireEvent.click(startButton);

            // Should show loading state
            await waitFor(() => {
                expect(screen.getByText('Initializing trading system...')).toBeInTheDocument();
            });

            // Should show progress indicator
            expect(screen.getByRole('progressbar')).toBeInTheDocument();
        });

        test('should disable button during startup process', async () => {
            ipcService.startBot.mockImplementation(() =>
                new Promise(resolve => {
                    setTimeout(() => {
                        resolve({success: true, data: {running: true}});
                    }, 500);
                }),
            );

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            const startButton = await screen.findByRole('button');
            fireEvent.click(startButton);

            // Button should be disabled during startup
            await waitFor(() => {
                expect(startButton).toBeDisabled();
            });
        });
    });

    describe('Stop Button Workflow', () => {
        test('should successfully stop trading system', async () => {
            // Start with running system
            ipcService.getRealTimeStatus.mockResolvedValue({
                success: true,
                data: {isRunning: true, health: 'healthy'}
            });

            ipcService.stopBot.mockResolvedValue({
                success: true,
                data: {running: false}
            });

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            await waitFor(() => {
                expect(mockOnStatusChange).toHaveBeenCalledWith({
                    isRunning: true,
                    health: 'healthy'
                });
            });

            const stopButton = screen.getByRole('button');
            fireEvent.click(stopButton);

            await waitFor(() => {
                expect(ipcService.stopBot).toHaveBeenCalled();
            });

            await waitFor(() => {
                expect(mockShowNotification).toHaveBeenCalledWith(
                    'Trading system stopped successfully',
                    'success',
                );
            });
        });

        test('should handle stop errors gracefully', async () => {
            const errorMessage = 'Failed to stop gracefully';

            // Start with running system
            ipcService.getRealTimeStatus.mockResolvedValue({
                success: true,
                data: {isRunning: true, health: 'healthy'}
            });

            ipcService.stopBot.mockResolvedValue({
                success: false,
                error: {message: errorMessage}
            });

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            await waitFor(() => {
                expect(mockOnStatusChange).toHaveBeenCalled();
            });

            const stopButton = screen.getByRole('button');
            fireEvent.click(stopButton);

            await waitFor(() => {
                expect(mockShowNotification).toHaveBeenCalledWith(
                    `Failed to stop trading system: ${errorMessage}`,
                    'error',
                );
            });
        });
    });

    describe('Real-time Status Updates', () => {
        test('should handle status check failures', async () => {
            ipcService.getRealTimeStatus.mockRejectedValue(
                new Error('IPC communication failed'),
            );

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            await waitFor(() => {
                expect(screen.getByText('Failed to check system status')).toBeInTheDocument();
            });
        });

        test('should update status when onStatusChange is called', async () => {
            const statusData = {
                isRunning: true,
                health: 'healthy',
                components: {
                    database: 'connected',
                    trading: 'active'
                }
            };

            ipcService.getRealTimeStatus.mockResolvedValue({
                success: true,
                data: statusData
            });

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            await waitFor(() => {
                expect(mockOnStatusChange).toHaveBeenCalledWith(statusData);
            });
        });

        test('should handle system health information', async () => {
            const healthData = {
                status: 'healthy',
                components: {
                    database: {status: 'connected', lastCheck: Date.now()},
                    trading: {status: 'active', lastCheck: Date.now()}
                }
            };

            ipcService.getSystemHealth.mockResolvedValue({
                success: true,
                data: healthData
            });

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            await waitFor(() => {
                expect(ipcService.getSystemHealth).toHaveBeenCalled();
            });
        });
    });

    describe('Error Recovery', () => {
        test('should recover from temporary IPC failures', async () => {
            // First call fails, second succeeds
            ipcService.getRealTimeStatus
                .mockRejectedValueOnce(new Error('Temporary failure'))
                .mockResolvedValueOnce({
                    success: true,
                    data: {isRunning: false, health: 'healthy'}
                });

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            // Should show error initially
            await waitFor(() => {
                expect(screen.getByText('Failed to check system status')).toBeInTheDocument();
            });
        });

        test('should clear errors on successful operations', async () => {
            // Start with error state
            ipcService.getRealTimeStatus.mockRejectedValueOnce(
                new Error('Initial failure'),
            );

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            await waitFor(() => {
                expect(screen.getByText('Failed to check system status')).toBeInTheDocument();
            });

            // Mock successful start
            ipcService.startBot.mockResolvedValue({
                success: true,
                data: {running: true}
            });

            ipcService.getRealTimeStatus.mockResolvedValue({
                success: true,
                data: {isRunning: true, health: 'healthy'}
            });

            const startButton = screen.getByRole('button');
            fireEvent.click(startButton);

            await waitFor(() => {
                expect(screen.queryByText('Failed to check system status')).not.toBeInTheDocument();
            });
        });
    });

    describe('Component Integration', () => {
        test('should integrate with system status panel', async () => {
            const statusData = {
                isRunning: true,
                health: 'healthy',
                components: {
                    memeCoinScanner: 'active',
                    whaleTracker: 'monitoring',
                    dataCollector: 'collecting'
                }
            };

            ipcService.getRealTimeStatus.mockResolvedValue({
                success: true,
                data: statusData
            });

            render(
                <StartButton
                    onStatusChange={mockOnStatusChange}
                    showNotification={mockShowNotification}
                />,
            );

            await waitFor(() => {
                expect(mockOnStatusChange).toHaveBeenCalledWith(statusData);
            });

            // Verify that status change callback provides complete data
            expect(mockOnStatusChange).toHaveBeenCalledWith(
                expect.objectContaining({
                    isRunning: true,
                    health: 'healthy',
                    components: expect.any(Object)
                }),
            );
        });
    });
});