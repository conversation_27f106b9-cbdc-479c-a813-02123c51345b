'use strict';

/**
 * @fileoverview Centralized Production-Ready Logger
 * @description Initializes and configures the electron-log library to provide a robust,
 * feature-rich logging solution for the entire Electron application (main, renderer, and workers).
 * It supports multiple transport layers (console, file), log rotation, structured formatting,
 * and global exception handling.
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 2025-07-25
 */

import log from 'electron-log';
import path from 'path';

// --- Configuration ---

// 1. Set log level
// Determines which messages are processed. Levels are: error, warn, info, verbose, debug, silly.
// In production, 'warn' is a good default to reduce noise. In development, 'debug' is useful.
log.level = process.env.NODE_ENV === 'development' ? 'debug' : 'warn';

// 2. Configure File Transport
// Writes logs to the local filesystem. Essential for debugging production issues.
log.transports.file.resolvePath = (variables) => {
  // Store logs in a dedicated 'logs' directory within the app's user data folder.
  // This is the standard location and ensures logs are persisted across updates.
  return path.join(variables.appData, 'logs', variables.fileName);
};

// Set a custom filename for the main log file.
log.transports.file.fileName = 'main.log';

// Configure log rotation to prevent log files from growing indefinitely.
// Archives the log file when it reaches 10MB and keeps up to 5 archives.
log.transports.file.maxSize = 10 * 1024 * 1024; // 10 MB


// 3. Customize Log Format
// Defines the structure of log messages for both console and file outputs.
// A structured format makes logs easier to parse and analyze.
// Example: [2025-07-25T08:00:00.000Z] [info] [main] Log message
log.transports.file.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}] [{processType}] {text}';
log.transports.console.format = '[{h}:{i}:{s}.{ms}] [{level}] [{processType}] {text}';


// 4. Handle Uncaught Exceptions
// Catches any uncaught errors in both main and renderer processes, logging them before the app crashes.
// This is critical for diagnosing fatal errors.
log.catchErrors({
  showDialog: process.env.NODE_ENV === 'development', // Only show error dialogs in development
  onError(error, _versions, _submitIssue) {
    log.error('Uncaught Exception:', error);
    // In a real application, you might want to send this error to a reporting service.
    // For example: _submitIssue('https://github.com/my-org/my-app/issues/new', { ... });
  },
});

// --- Exports ---

// Expose the configured logger instance for use throughout the application.
// Also, attach helper methods for more specific logging contexts if needed.
export default log;