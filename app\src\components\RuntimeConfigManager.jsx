/**
 * @fileoverview Runtime Configuration Manager Component
 * @description React component for managing runtime configuration, feature flags, and backups
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
    Box,
    Card,
    CardContent,
    Typography,
    Switch,
    FormControlLabel,
    Button,
    TextField,
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Grid,
    Chip,
    Alert,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    List,
    ListItem,
    ListItemText,
    ListItemSecondaryAction,
    IconButton,
    Tooltip,
    Snackbar,
    CircularProgress,
    Divider
} from '@mui/material';
import {
    ExpandMore as ExpandMoreIcon,
    Settings as SettingsIcon,
    Backup as BackupIcon,
    Restore as RestoreIcon,
    Delete as DeleteIcon,
    Refresh as RefreshIcon,
    Save as SaveIcon,
    Warning as WarningIcon,
    CheckCircle as CheckCircleIcon,
    Error as ErrorIcon
} from '@mui/icons-material';

const RuntimeConfigManager = () => {
    // State management
    const [featureFlags, setFeatureFlags] = useState({});
    const [configurations, setConfigurations] = useState({});
    const [backups, setBackups] = useState([]);
    const [configHealth, setConfigHealth] = useState({});
    const [runtimeStats, setRuntimeStats] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [success, setSuccess] = useState(null);

    // Dialog states
    const [backupDialogOpen, setBackupDialogOpen] = useState(false);
    const [restoreDialogOpen, setRestoreDialogOpen] = useState(false);
    const [configEditDialogOpen, setConfigEditDialogOpen] = useState(false);
    const [selectedBackup, setSelectedBackup] = useState(null);
    const [selectedConfig, setSelectedConfig] = useState(null);
    const [configEditValue, setConfigEditValue] = useState('');
    const [backupLabel, setBackupLabel] = useState('');

    // Load initial data
    const loadData = useCallback(async () => {
        try {
            setLoading(true);
            
            // Load feature flags
            const flagsResponse = await window.electronAPI.getAllFeatureFlags();
            if (flagsResponse.success) {
                setFeatureFlags(flagsResponse.data);
            }

            // Load configuration health
            const healthResponse = await window.electronAPI.getConfigHealth();
            if (healthResponse.success) {
                setConfigHealth(healthResponse.data);
            }

            // Load runtime stats
            const statsResponse = await window.electronAPI.getRuntimeConfigStats();
            if (statsResponse.success) {
                setRuntimeStats(statsResponse.data);
            }

            // Load backups
            const backupsResponse = await window.electronAPI.listConfigBackups();
            if (backupsResponse.success) {
                setBackups(backupsResponse.data);
            }

            setError(null);
        } catch (err) {
            setError(`Failed to load configuration data: ${err.message}`);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        loadData();
        
        // Set up periodic refresh
        const interval = setInterval(loadData, 30000); // Refresh every 30 seconds
        return () => clearInterval(interval);
    }, [loadData]);

    // Feature flag handlers
    const handleFeatureFlagToggle = async (flagName, enabled) => {
        try {
            const response = await window.electronAPI.setFeatureFlag(flagName, enabled);
            if (response.success) {
                setFeatureFlags(prev => ({
                    ...prev,
                    [flagName]: {
                        ...prev[flagName],
                        enabled,
                        currentValue: enabled
                    }
                }));
                setSuccess(`Feature flag '${flagName}' ${enabled ? 'enabled' : 'disabled'}`);
            } else {
                throw new Error(response.error || 'Failed to update feature flag');
            }
        } catch (err) {
            setError(`Failed to update feature flag: ${err.message}`);
        }
    };

    // Configuration handlers
    const handleConfigEdit = (configName) => {
        setSelectedConfig(configName);
        setConfigEditValue(JSON.stringify(configurations[configName] || {}, null, 2));
        setConfigEditDialogOpen(true);
    };

    const handleConfigSave = async () => {
        try {
            const configData = JSON.parse(configEditValue);
            const response = await window.electronAPI.updateConfig(selectedConfig, configData);
            
            if (response.success) {
                setConfigurations(prev => ({
                    ...prev,
                    [selectedConfig]: configData
                }));
                setSuccess(`Configuration '${selectedConfig}' updated successfully`);
                setConfigEditDialogOpen(false);
            } else {
                throw new Error(response.error || 'Failed to update configuration');
            }
        } catch (err) {
            setError(`Failed to save configuration: ${err.message}`);
        }
    };

    // Backup handlers
    const handleCreateBackup = async () => {
        try {
            const response = await window.electronAPI.createConfigBackup(backupLabel || 'manual');
            if (response.success) {
                setSuccess(`Backup created: ${response.data.backupName}`);
                setBackupDialogOpen(false);
                setBackupLabel('');
                loadData(); // Refresh backup list
            } else {
                throw new Error(response.error || 'Failed to create backup');
            }
        } catch (err) {
            setError(`Failed to create backup: ${err.message}`);
        }
    };

    const handleRestoreBackup = async () => {
        try {
            const response = await window.electronAPI.restoreConfigBackup(selectedBackup.name);
            if (response.success) {
                setSuccess(`Configuration restored from backup: ${selectedBackup.name}`);
                setRestoreDialogOpen(false);
                setSelectedBackup(null);
                loadData(); // Refresh all data
            } else {
                throw new Error(response.error || 'Failed to restore backup');
            }
        } catch (err) {
            setError(`Failed to restore backup: ${err.message}`);
        }
    };

    // Utility functions
    const getHealthStatusIcon = (status) => {
        switch (status) {
            case 'healthy':
                return <CheckCircleIcon color="success" />;
            case 'warning':
                return <WarningIcon color="warning" />;
            case 'error':
                return <ErrorIcon color="error" />;
            default:
                return <WarningIcon color="disabled" />;
        }
    };

    const formatTimestamp = (timestamp) => {
        return new Date(timestamp).toLocaleString();
    };

    if (loading) {
        return (
            <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
                <CircularProgress />
                <Typography variant="h6" sx={{ ml: 2 }}>
                    Loading configuration data...
                </Typography>
            </Box>
        );
    }

    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h4" gutterBottom>
                Runtime Configuration Manager
            </Typography>

            {/* Health Status Card */}
            <Card sx={{ mb: 3 }}>
                <CardContent>
                    <Box display="flex" alignItems="center" mb={2}>
                        {getHealthStatusIcon(configHealth.status)}
                        <Typography variant="h6" sx={{ ml: 1 }}>
                            System Health: {configHealth.status || 'Unknown'}
                        </Typography>
                        <Button
                            startIcon={<RefreshIcon />}
                            onClick={loadData}
                            sx={{ ml: 'auto' }}
                        >
                            Refresh
                        </Button>
                    </Box>
                    
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                            <Typography variant="body2" color="textSecondary">
                                Configurations Loaded: {configHealth.configsLoaded || 0}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                                Feature Flags: {configHealth.featureFlagsLoaded || 0}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                                Active Features: {configHealth.activeFeatures?.length || 0}
                            </Typography>
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Typography variant="body2" color="textSecondary">
                                Updates: {runtimeStats.updateCount || 0}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                                Failed Updates: {runtimeStats.failedUpdates || 0}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                                Last Update: {runtimeStats.lastUpdate ? formatTimestamp(runtimeStats.lastUpdate) : 'Never'}
                            </Typography>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>

            {/* Feature Flags Section */}
            <Accordion defaultExpanded>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">Feature Flags</Typography>
                </AccordionSummary>
                <AccordionDetails>
                    <Grid container spacing={2}>
                        {Object.entries(featureFlags).map(([flagName, flag]) => (
                            <Grid item xs={12} md={6} key={flagName}>
                                <Card variant="outlined">
                                    <CardContent>
                                        <Box display="flex" alignItems="center" justifyContent="space-between">
                                            <Box>
                                                <Typography variant="subtitle1">
                                                    {flagName}
                                                </Typography>
                                                <Typography variant="body2" color="textSecondary">
                                                    {flag.description}
                                                </Typography>
                                            </Box>
                                            <FormControlLabel
                                                control={
                                                    <Switch
                                                        checked={flag.currentValue || false}
                                                        onChange={(e) => handleFeatureFlagToggle(flagName, e.target.checked)}
                                                    />
                                                }
                                                label=""
                                            />
                                        </Box>
                                        {flag.lastUpdated && (
                                            <Typography variant="caption" color="textSecondary">
                                                Last updated: {formatTimestamp(flag.lastUpdated)}
                                            </Typography>
                                        )}
                                    </CardContent>
                                </Card>
                            </Grid>
                        ))}
                    </Grid>
                </AccordionDetails>
            </Accordion>

            {/* Configuration Backup Section */}
            <Accordion sx={{ mt: 2 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="h6">Configuration Backups</Typography>
                </AccordionSummary>
                <AccordionDetails>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                        <Typography variant="body1">
                            Available Backups ({backups.length})
                        </Typography>
                        <Button
                            startIcon={<BackupIcon />}
                            variant="contained"
                            onClick={() => setBackupDialogOpen(true)}
                        >
                            Create Backup
                        </Button>
                    </Box>
                    
                    <List>
                        {backups.map((backup) => (
                            <ListItem key={backup.name} divider>
                                <ListItemText
                                    primary={backup.name}
                                    secondary={
                                        <Box>
                                            <Typography variant="body2">
                                                Label: {backup.label}
                                            </Typography>
                                            <Typography variant="body2">
                                                Created: {formatTimestamp(backup.timestamp)}
                                            </Typography>
                                            <Typography variant="body2">
                                                Configs: {Object.keys(backup.configs || {}).length}
                                            </Typography>
                                        </Box>
                                    }
                                />
                                <ListItemSecondaryAction>
                                    <Tooltip title="Restore Backup">
                                        <IconButton
                                            onClick={() => {
                                                setSelectedBackup(backup);
                                                setRestoreDialogOpen(true);
                                            }}
                                        >
                                            <RestoreIcon />
                                        </IconButton>
                                    </Tooltip>
                                </ListItemSecondaryAction>
                            </ListItem>
                        ))}
                    </List>
                    
                    {backups.length === 0 && (
                        <Typography variant="body2" color="textSecondary" textAlign="center">
                            No backups available
                        </Typography>
                    )}
                </AccordionDetails>
            </Accordion>

            {/* Create Backup Dialog */}
            <Dialog open={backupDialogOpen} onClose={() => setBackupDialogOpen(false)}>
                <DialogTitle>Create Configuration Backup</DialogTitle>
                <DialogContent>
                    <TextField
                        autoFocus
                        margin="dense"
                        label="Backup Label"
                        fullWidth
                        variant="outlined"
                        value={backupLabel}
                        onChange={(e) => setBackupLabel(e.target.value)}
                        placeholder="Enter a label for this backup"
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setBackupDialogOpen(false)}>Cancel</Button>
                    <Button onClick={handleCreateBackup} variant="contained">
                        Create Backup
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Restore Backup Dialog */}
            <Dialog open={restoreDialogOpen} onClose={() => setRestoreDialogOpen(false)}>
                <DialogTitle>Restore Configuration Backup</DialogTitle>
                <DialogContent>
                    <Alert severity="warning" sx={{ mb: 2 }}>
                        This will replace all current configurations with the backup data.
                        A backup of the current state will be created automatically.
                    </Alert>
                    {selectedBackup && (
                        <Box>
                            <Typography variant="h6">{selectedBackup.name}</Typography>
                            <Typography variant="body2">
                                Label: {selectedBackup.label}
                            </Typography>
                            <Typography variant="body2">
                                Created: {formatTimestamp(selectedBackup.timestamp)}
                            </Typography>
                            <Typography variant="body2">
                                Configurations: {Object.keys(selectedBackup.configs || {}).length}
                            </Typography>
                        </Box>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setRestoreDialogOpen(false)}>Cancel</Button>
                    <Button onClick={handleRestoreBackup} variant="contained" color="warning">
                        Restore Backup
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Configuration Edit Dialog */}
            <Dialog 
                open={configEditDialogOpen} 
                onClose={() => setConfigEditDialogOpen(false)}
                maxWidth="md"
                fullWidth
            >
                <DialogTitle>Edit Configuration: {selectedConfig}</DialogTitle>
                <DialogContent>
                    <TextField
                        multiline
                        rows={20}
                        fullWidth
                        variant="outlined"
                        value={configEditValue}
                        onChange={(e) => setConfigEditValue(e.target.value)}
                        sx={{ mt: 1 }}
                        placeholder="Enter JSON configuration"
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setConfigEditDialogOpen(false)}>Cancel</Button>
                    <Button onClick={handleConfigSave} variant="contained" startIcon={<SaveIcon />}>
                        Save Configuration
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Success/Error Snackbars */}
            <Snackbar
                open={!!success}
                autoHideDuration={6000}
                onClose={() => setSuccess(null)}
            >
                <Alert onClose={() => setSuccess(null)} severity="success">
                    {success}
                </Alert>
            </Snackbar>

            <Snackbar
                open={!!error}
                autoHideDuration={6000}
                onClose={() => setError(null)}
            >
                <Alert onClose={() => setError(null)} severity="error">
                    {error}
                </Alert>
            </Snackbar>
        </Box>
    );
};

export default RuntimeConfigManager;