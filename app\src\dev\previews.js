 
// @ts-nocheck
'use strict';

const React = require('react');
const { ComponentPreview, Previews } = require('@react-buddy/ide-toolbox');
const PaletteTree = require('./palette');
const logger = require('../utils/logger');
const Dashboard = require('../components/Dashboard');
const UltimateDashboard = require('../components/UltimateDashboard');
const BotCard = require('../components/BotCard');
const HolographicCard = require('../components/HolographicCard');
const FuturisticButton = require('../components/FuturisticButton');
const VibrantButton = require('../components/VibrantButton');
const ParticleBackground = require('../components/ParticleBackground');

/**
 * ComponentPreviews is a React component that renders a collection of component previews.
 * The previews are grouped into a single `Previews` component, which is wrapped in a `PaletteTree` component.
 * The `PaletteTree` component is used to group the previews by category.
 * The previews are rendered in the order they are declared.
 * Each preview is rendered as a `ComponentPreview` component, which is passed the `path` prop.
 * The `path` prop is used to determine the route for the preview.
 * The `ComponentPreview` component is also passed the `children` prop, which is the component to be rendered.
 * The `children` prop is optional.
 * The `ComponentPreview` component is also passed the `palette` prop, which is the `PaletteTree` component.
 * The `palette` prop is optional.
 * The `ComponentPreviews` component is also passed the `palette` prop, which is the `PaletteTree` component.
 * The `palette` prop is required.
 * The `ComponentPreviews` component is also passed the `children` prop, which is the component to be rendered.
 * The `children` prop is optional.
 */
const ComponentPreviews = () => {
  return /*#__PURE__*/React.createElement(Previews, {
    palette: /*#__PURE__*/React.createElement(PaletteTree, null),
  }, /*#__PURE__*/React.createElement(ComponentPreview, {
    path: '/Dashboard',
  }, /*#__PURE__*/React.createElement(Dashboard, {
    showNotification: (msg, type) => type === 'error' ? logger.error(`${type}: ${msg}`) : logger.info(`${type}: ${msg}`),
  })), /*#__PURE__*/React.createElement(ComponentPreview, {
    path: '/UltimateDashboard',
  }, /*#__PURE__*/React.createElement(UltimateDashboard, {
    showNotification: (msg, type) => type === 'error' ? logger.error(`${type}: ${msg}`) : logger.info(`${type}: ${msg}`),
  })), /*#__PURE__*/React.createElement(ComponentPreview, {
    path: '/BotCard',
  }, /*#__PURE__*/React.createElement(BotCard, {
    bot: {
      id: '1',
      name: 'Sample Bot',
      type: 'grid',
      status: 'active',
      symbol: 'BTC/USDT',
      profit: 1234.56,
      profitPercent: 15.2,
      totalTrades: 42,
      successRate: 85.7,
    },
    onStart: () => {
    },
    onStop: () => {
    },
    onConfigure: () => {
    },
  })), /*#__PURE__*/React.createElement(ComponentPreview, {
    path: '/HolographicCard',
  }, /*#__PURE__*/React.createElement(HolographicCard, {
    variant: 'default',
    elevation: 'medium',
    sx: {},
  }, /*#__PURE__*/React.createElement('div', {
    style: {
      padding: '20px',
    },
  }, 'Sample Holographic Card Content'))), /*#__PURE__*/React.createElement(ComponentPreview, {
    path: '/FuturisticButton',
  }, /*#__PURE__*/React.createElement(FuturisticButton, {
    variant: 'primary',
    onClick: () => logger.info('Clicked!'),
  }, 'Futuristic Button')), /*#__PURE__*/React.createElement(ComponentPreview, {
    path: '/VibrantButton',
  }, /*#__PURE__*/React.createElement(VibrantButton, {
    color: 'primary',
    onClick: () => logger.info('Clicked!'),
  }, 'Vibrant Button')), /*#__PURE__*/React.createElement(ComponentPreview, {
    path: '/ParticleBackground',
  }, /*#__PURE__*/React.createElement('div', {
    style: {
      height: '400px',
      position: 'relative',
    },
  }, /*#__PURE__*/React.createElement(ParticleBackground, {
    tradingActive: true,
    intensity: 'medium',
  }))));
};
module.exports = ComponentPreviews;