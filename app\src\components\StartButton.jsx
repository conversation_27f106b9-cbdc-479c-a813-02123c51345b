// Import logger for consistent logging
import logger from '../utils/logger';

import React, {useCallback, useEffect, useState} from 'react';
import {<PERSON><PERSON>, Box, Button, Chip, Fade, LinearProgress, Typography} from '@mui/material';
import RestartAltIcon from '@mui/icons-material/RestartAlt';
import PowerSettingsNewIcon from '@mui/icons-material/PowerSettingsNew';
import PowerOffIcon from '@mui/icons-material/PowerOff';
import SyncIcon from '@mui/icons-material/Sync';
import ipcService from '../services/ipcService';

const StartButton = ({
                         onStatusChange = (_data) => {
                         }, showNotification = (_message, _type) => {
    }
                     }) => {
    const [isStarting, setIsStarting] = useState(false);
    const [isRunning, setIsRunning] = useState(false);
    const [startupStatus, setStartupStatus] = useState(null);
    const [startupProgress, setStartupProgress] = useState(0);
    const [currentStep, setCurrentStep] = useState(0);
    const [error, setError] = useState(null);
    const [systemInfo, setSystemInfo] = useState(null);

    const checkBotStatus = useCallback(async () => {
        try {
            const response = await ipcService.getRealTimeStatus();
            if (response.success) {
                setIsRunning(response.data.isRunning);
                if (onStatusChange) {
                    onStatusChange(response.data);
                }
            }
        } catch (error) {
            logger.error('Failed to check bot status:', error);
            setError('Failed to check system status');
        }
    }, [onStatusChange]);

    useEffect(() => {
        checkBotStatus();
        getSystemInfo();
    }, [checkBotStatus]);

    const getSystemInfo = async () => {
        try {
            const response = await ipcService.getSystemHealth();
            if (response.success) {
                setSystemInfo(response.data);
            }
        } catch (error) {
            logger.error('Failed to get system info:', error);
        }
    };

    const handleStart = async () => {
        setIsStarting(true);
        setError(null);
        setStartupStatus('Initializing trading system...');
        setStartupProgress(0);
        setCurrentStep(0);

        try {
            const response = await ipcService.startBot();
            if (response.success) {
                await checkBotStatus();
                if (showNotification) {
                    showNotification('Trading system started successfully', 'success');
                }
            } else {
                throw new Error(response.error?.message || response.error?.toString() || 'Failed to start trading system');
            }
        } catch (error) {
            setError(error.message || 'Failed to start trading system');
            if (showNotification) {
                showNotification(`Failed to start trading system: ${error.message}`, 'error');
            }
            setIsStarting(false);
        }
    };

    const handleStop = async () => {
        setIsStarting(true);
        setError(null);
        setStartupStatus('Stopping trading system...');

        try {
            const response = await ipcService.stopBot();
            if (response.success) {
                setIsRunning(false);
                setStartupStatus('Trading system stopped');
                setStartupProgress(0);
                setCurrentStep(0);
                if (showNotification) {
                    showNotification('Trading system stopped successfully', 'success');
                }
                await checkBotStatus();
            } else {
                throw new Error(response.error?.message || response.error?.toString() || 'Failed to stop trading system');
            }
        } catch (error) {
            setError(error.message || 'Failed to stop trading system');
            if (showNotification) {
                showNotification(`Failed to stop trading system: ${error.message}`, 'error');
            }
        } finally {
            setIsStarting(false);
        }
    };

    const handleRestart = async () => {
        setError(null);
        setStartupStatus('Restarting trading system...');

        try {
            await handleStop();
            await new Promise(resolve => setTimeout(resolve, 2000));
            await handleStart();
        } catch (error) {
            setError(error.message || 'Failed to restart trading system');
            if (showNotification) {
                showNotification(`Failed to restart trading system: ${error.message}`, 'error');
            }
        }
    };

    const getButtonColor = () => {
        if (isStarting) return '#ff9800';
        return isRunning ? '#f44336' : '#4caf50';
    };

    const getButtonHoverColor = () => {
        if (isStarting) return '#f57c00';
        return isRunning ? '#d32f2f' : '#45a049';
    };

    const getButtonText = () => {
        if (isStarting) return 'Processing...';
        return isRunning ? 'Stop Trading' : 'Start Trading';
    };

    const getButtonIcon = () => {
        if (isStarting) return <SyncIcon sx={{animation: 'spin 2s linear infinite'}}/>;
        return isRunning ? <PowerOffIcon/> : <PowerSettingsNewIcon/>;
    };

    const getStatusChip = () => {
        if (isStarting) {
            return <Chip label="Starting..." color="warning" size="small"/>;
        }
        if (isRunning) {
            return <Chip label="Running" color="success" size="small"/>;
        }
        return <Chip label="Stopped" color="default" size="small"/>;
    };

    return (
        <Box sx={{display: 'flex', flexDirection: 'column', gap: 3, alignItems: 'center', width: '100%'}}>
            <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2}}>
                <Button
                    variant="contained"
                    size="large"
                    onClick={isRunning ? handleStop : handleStart}
                    disabled={isStarting}
                    startIcon={getButtonIcon()}
                    sx={{
                        minWidth: 250,
                        backgroundColor: getButtonColor(),
                        '&:hover': {
                            backgroundColor: getButtonHoverColor()
                        },
                        fontWeight: 'bold',
                        fontSize: '1.2rem',
                        padding: '14px 28px',
                        borderRadius: '12px',
                        textTransform: 'none',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                        transition: 'all 0.3s ease'
                    }}
                >
                    {getButtonText()}
                </Button>

                <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                    {getStatusChip()}
                    {systemInfo && (
                        <Typography variant="caption" color="textSecondary">
                            v{systemInfo.version}
                        </Typography>
                    )}
                </Box>
            </Box>

            {isStarting && (
                <Fade in={isStarting} timeout={500}>
                    <Box sx={{width: '100%', maxWidth: 400}}>
                        <LinearProgress
                            variant="determinate"
                            value={startupProgress}
                            sx={{height: 8, borderRadius: 4, mb: 1}}
                        />
                        <Typography variant="body2" color="textSecondary" align="center">
                            {startupStatus || 'Initializing...'}
                        </Typography>
                        <Typography variant="caption" color="textSecondary" align="center" display="block">
                            Step {currentStep} of {5}
                        </Typography>
                    </Box>
                </Fade>
            )}

            {isRunning && !isStarting && (
                <Fade in={!isStarting} timeout={500}>
                    <Button
                        variant="outlined"
                        size="medium"
                        onClick={handleRestart}
                        disabled={isStarting}
                        startIcon={<RestartAltIcon/>}
                        sx={{
                            minWidth: 140,
                            borderColor: '#ff9800',
                            color: '#ff9800',
                            '&:hover': {
                                borderColor: '#f57c00',
                                backgroundColor: 'rgba(255, 152, 0, 0.1)'
                            }
                        }}
                    >
                        Restart
                    </Button>
                </Fade>
            )}

            {error && (
                <Fade in={!!error} timeout={300}>
                    <Alert severity="error" sx={{mt: 1, maxWidth: 400}}>
                        {error}
                    </Alert>
                </Fade>
            )}

            {startupStatus && !isStarting && !error && (
                <Fade in={!isStarting && !error} timeout={300}>
                    <Typography variant="body2" color="textSecondary" align="center">
                        {startupStatus}
                    </Typography>
                </Fade>
            )}
        </Box>
    );
};

export default StartButton;
