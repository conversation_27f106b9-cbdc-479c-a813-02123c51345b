import React from 'react';
import {<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Card, CardContent, CircularProgress, Typography} from '@mui/material';
import {ErrorOutline, Home, Info, Refresh, Warning} from '@mui/icons-material';
import {motion} from 'framer-motion';

export const LoadingFallback = ({message = 'Loading trading system...'}) => (
    <Box
        sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '100vh',
            background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)'
        }}
    >
        <motion.div
            initial={{opacity: 0, scale: 0.8}}
            animate={{opacity: 1, scale: 1}}
            transition={{duration: 0.5}}
        >
            <Card sx={{
                background: 'rgba(24, 26, 32, 0.95)',
                border: '1px solid rgba(0, 234, 255, 0.3)',
                borderRadius: 3
            }}>
                <CardContent sx={{textAlign: 'center', p: 4}}>
                    <CircularProgress sx={{color: '#00eaff', mb: 2}}/>
                    <Typography variant="h6" sx={{color: 'rgba(255, 255, 255, 0.9)'}}>
                        {message}
                    </Typography>
                </CardContent>
            </Card>
        </motion.div>
    </Box>
);

export const NetworkErrorFallback = ({error, onRetry}) => (
    <Box
        sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '100vh',
            background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
            p: 3
        }}
    >
        <motion.div
            initial={{opacity: 0, y: 20}}
            animate={{opacity: 1, y: 0}}
            transition={{duration: 0.6}}
            style={{width: '100%', maxWidth: '400px'}}
        >
            <Card sx={{
                background: 'rgba(24, 26, 32, 0.95)',
                border: '1px solid rgba(255, 152, 0, 0.3)',
                borderRadius: 3
            }}>
                <CardContent sx={{textAlign: 'center', p: 4}}>
                    <Warning sx={{fontSize: '3rem', color: '#ff9800', mb: 2}}/>
                    <Typography variant="h5" sx={{color: '#ff9800', mb: 2}}>
                        Connection Error
                    </Typography>
                    <Typography variant="body1" sx={{color: 'rgba(255, 255, 255, 0.8)', mb: 3}}>
                        {error?.message || 'Unable to connect to the trading system. Please check your connection and try again.'}
                    </Typography>
                    <Button
                        variant="contained"
                        startIcon={<Refresh/>}
                        onClick={onRetry}
                        sx={{
                            background: 'linear-gradient(45deg, #ff9800, #f44336)',
                            color: 'white',
                            borderRadius: 2,
                            textTransform: 'none'
                        }}
                    >
                        Retry Connection
                    </Button>
                </CardContent>
            </Card>
        </motion.div>
    </Box>
);

export const ComponentErrorFallback = ({error, errorInfo, onRetry, onGoHome}) => (
    <Box sx={{p: 3}}>
        <Alert
            severity="error"
            sx={{
                backgroundColor: 'rgba(244, 67, 54, 0.1)',
                border: '1px solid rgba(244, 67, 54, 0.3)',
                color: 'white',
                '& .MuiAlert-icon': {
                    color: '#f44336'
                }
            }}
            action={
                <Box sx={{display: 'flex', gap: 1}}>
                    <Button size="small" onClick={onRetry} sx={{color: '#00eaff'}}>
                        Retry
                    </Button>
                    <Button size="small" onClick={onGoHome} sx={{color: '#ff9800'}}>
                        Home
                    </Button>
                </Box>
            }
        >
            <Typography variant="h6" gutterBottom>
                Component Error
            </Typography>
            <Typography variant="body2">
                {error?.message || 'An error occurred while loading this component.'}
            </Typography>
            {process.env.NODE_ENV === 'development' && errorInfo && (
                <Box sx={{mt: 2, fontSize: '0.75rem', fontFamily: 'monospace'}}>
                    <Typography variant="caption" display="block">
                        Stack: {error?.stack}
                    </Typography>
                    <Typography variant="caption" display="block">
                        Component Stack: {errorInfo?.componentStack}
                    </Typography>
                </Box>
            )}
        </Alert>
    </Box>
);

export const EmptyStateFallback = ({
                                       title = 'No Data Available',
                                       message = 'There is no data to display at this time.'
                                   }) => (
    <Box
        sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '200px',
            p: 3
        }}
    >
        <Card sx={{background: 'rgba(24, 26, 32, 0.5)', border: '1px solid rgba(255, 255, 255, 0.1)', borderRadius: 2}}>
            <CardContent sx={{textAlign: 'center', p: 4}}>
                <Info sx={{fontSize: '2rem', color: 'rgba(255, 255, 255, 0.5)', mb: 2}}/>
                <Typography variant="h6" sx={{color: 'rgba(255, 255, 255, 0.7)', mb: 1}}>
                    {title}
                </Typography>
                <Typography variant="body2" sx={{color: 'rgba(255, 255, 255, 0.5)'}}>
                    {message}
                </Typography>
            </CardContent>
        </Card>
    </Box>
);

export const PermissionDeniedFallback = ({onGoHome}) => (
    <Box
        sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '100vh',
            background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
            p: 3
        }}
    >
        <motion.div
            initial={{opacity: 0, y: 20}}
            animate={{opacity: 1, y: 0}}
            transition={{duration: 0.6}}
            style={{width: '100%', maxWidth: '400px'}}
        >
            <Card sx={{
                background: 'rgba(24, 26, 32, 0.95)',
                border: '1px solid rgba(156, 39, 176, 0.3)',
                borderRadius: 3
            }}>
                <CardContent sx={{textAlign: 'center', p: 4}}>
                    <ErrorOutline sx={{fontSize: '3rem', color: '#9c27b0', mb: 2}}/>
                    <Typography variant="h5" sx={{color: '#9c27b0', mb: 2}}>
                        Access Denied
                    </Typography>
                    <Typography variant="body1" sx={{color: 'rgba(255, 255, 255, 0.8)', mb: 3}}>
                        You don&apos;t have permission to access this resource. Please contact your administrator if you
                        believe this is an error.
                    </Typography>
                    <Button
                        variant="contained"
                        startIcon={<Home/>}
                        onClick={onGoHome}
                        sx={{
                            background: 'linear-gradient(45deg, #9c27b0, #673ab7)',
                            color: 'white',
                            borderRadius: 2,
                            textTransform: 'none'
                        }}
                    >
                        Go to Dashboard
                    </Button>
                </CardContent>
            </Card>
        </motion.div>
    </Box>
);
