 
'use strict';

Object.defineProperty(exports, '__esModule', {
    value: true
});
exports.default = MarketAnalysis;
const _react = _interopRequireWildcard(require('react'));
const _propTypes = _interopRequireDefault(require('prop-types'));
const _logger = _interopRequireDefault(require('../utils/logger'));
const _material = require('@mui/material');
const _iconsMaterial = require('@mui/icons-material');
const _recharts = require('recharts');
const _HolographicCard = _interopRequireDefault(require('./HolographicCard'));
const _formatters = require('../utils/formatters');
const _excluded = ['children', 'value', 'index']; // Import logger for consistent logging
function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}

function _interopRequireWildcard(e, t) {
    if ('function' == typeof WeakMap) var r = new WeakMap(),
        n = new WeakMap();
    return (_interopRequireWildcard = function (e, t) {
        if (!t && e && e.__esModule) return e;
        let o,
            i,
            f = {
                __proto__: null,
                default: e
            };
        if (null === e || 'object' != typeof e && 'function' != typeof e) return f;
        if (o = t ? n : r) {
            if (o.has(e)) return o.get(e);
            o.set(e, f);
        }
        for (const t in e) 'default' !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]);
        return f;
    })(e, t);
}

function _extends() {
    return _extends = Object.assign ? Object.assign.bind() : function (n) {
        for (let e = 1; e < arguments.length; e++) {
            const t = arguments[e];
            for (const r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
        }
        return n;
    }, _extends.apply(null, arguments);
}

function _objectWithoutProperties(e, t) {
    if (null == e) return {};
    let o,
        r,
        i = _objectWithoutPropertiesLoose(e, t);
    if (Object.getOwnPropertySymbols) {
        const n = Object.getOwnPropertySymbols(e);
        for (r = 0; r < n.length; r++) o = n[r], -1 === t.indexOf(o) && {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]);
    }
    return i;
}

function _objectWithoutPropertiesLoose(r, e) {
    if (null == r) return {};
    const t = {};
    for (const n in r) if ({}.hasOwnProperty.call(r, n)) {
        if (-1 !== e.indexOf(n)) continue;
        t[n] = r[n];
    }
    return t;
}

// Define safe electronAPI with fallbacks for missing methods
/** @type {any} */
/** @type {Window & { electronAPI?: any }} */
const customWindow = window;
const safeElectronAPI = customWindow.electronAPI || {
    getMarketOverview: () => Promise.resolve({
        success: true,
        data: []
    }),
    getSentimentData: () => Promise.resolve({
        success: true,
        data: {}
    }),
    getTechnicalIndicators: () => Promise.resolve({
        success: true,
        data: {}
    })
};
const api = safeElectronAPI;

// Type definitions for better code clarity
/**
 * @typedef {Object} MarketCoin
 * @property {string} symbol - The coin symbol
 * @property {string} name - The coin name
 * @property {number} price - Current price
 * @property {number} change24h - 24h price change
 * @property {number} volume - 24h volume
 * @property {number} marketCap - Market capitalization
 * @property {number} sentiment - Sentiment score
 * @property {number} technicalScore - Technical analysis score
 */

/**
 * @typedef {Object} SentimentData
 * @property {number} overall - Overall sentiment score
 * @property {number} greed - Greed index
 * @property {number} neutral - Neutral sentiment
 * @property {number} fear - Fear index
 * @property {Array<{topic: string, sentiment: number, mentions: number}>} trending - Trending topics
 */

/**
 * @typedef {Object} TechnicalIndicators
 * @property {number} rsi - RSI indicator
 * @property {string} macd - Moving Average Convergence Divergence indicator
 * @property {string} bollingerBands - Volatility Bands indicator
 * @property {number} stochastic - Stochastic indicator
 * @property {number} support - Support level
 * @property {number} resistance - Resistance level
 * @property {string} trend - Trend direction
 * @property {number} strength - Trend strength
 */

/**
 * Custom tooltip component for scatter chart
 * Accepts all props from Recharts' Tooltip and uses only the relevant ones.
 * @param {{ active?: boolean, payload?: Array<any> }} props - All props passed by Recharts Tooltip
 * @returns {React.ReactElement|null} Tooltip component
 */
function CustomTooltip(props) {
    const {
        active,
        payload
    } = props;
    if (active && payload !== null && payload !== void 0 && payload.length) {
        const data = payload[0].payload;
        return /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                backgroundColor: 'rgba(24,26,32,0.95)',
                border: '1px solid #ff6b6b',
                borderRadius: '8px',
                p: 2
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#fff',
                fontWeight: 600
            }
        }, data.symbol), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#888',
                fontSize: '0.8rem'
            }
        }, 'Sentiment: ', data.y, '%'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#888',
                fontSize: '0.8rem'
            }
        }, 'Volume: ', (0, _formatters.formatCurrency)(data.z)));
    }
    return null;
}

CustomTooltip.propTypes = {
    active: _propTypes.default.bool,
    payload: _propTypes.default.array
};

/**
 * Renders the content of a tab panel.
 *
 * @param {object} props - The component props.
 * @param {React.ReactNode} props.children - The content to render inside the tab panel.
 * @param {number} props.value - The index of the currently active tab.
 * @param {number} props.index - The index of this tab panel.
 * @param {object} [props.other] - Additional props to pass to the containing `div` element.
 *
 * This component is used to render the contents of a tab panel based on the
 * currently selected tab. It will only render the children if the `value`
 * prop matches the `index` prop.
 */
function TabPanel(_ref) {
    let {
            children,
            value,
            index
        } = _ref,
        other = _objectWithoutProperties(_ref, _excluded);
    return /*#__PURE__*/_react.default.createElement('div', _extends({
        role: 'tabpanel',
        hidden: value !== index,
        id: `analysis-tabpanel-${index}`,
        'aria-labelledby': `analysis-tab-${index}`
    }, other), value === index && /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            pt: 3
        }
    }, children));
}

TabPanel.propTypes = {
    children: _propTypes.default.node,
    index: _propTypes.default.number.isRequired,
    value: _propTypes.default.number.isRequired
};

/**
 * Market Analysis component - provides comprehensive market data analysis,
 * sentiment analysis, and technical indicators
 * @returns {React.ReactElement} The market analysis component
 */
function MarketAnalysis() {
    let _sentimentData$overal, _sentimentData$trendi;
    const [activeTab, setActiveTab] = (0, _react.useState)(0);
    /** @type {[any[], Function]} */
    const [marketData, setMarketData] = (0, _react.useState)([]);
    /** @type {[any, Function]} */
    const [sentimentData, setSentimentData] = (0, _react.useState)({});
    /** @type {[any, Function]} */
    const [technicalIndicators, setTechnicalIndicators] = (0, _react.useState)({});
    (0, _react.useEffect)(() => {
        const fetchData = async () => {
            try {
                _logger.default.info('Fetching market analysis data...');
                const [market, sentiment, tech] = await Promise.all([api.getMarketOverview(), api.getSentimentData(), api.getTechnicalIndicators()]);
                if (market !== null && market !== void 0 && market.success) setMarketData(market.data);
                if (sentiment !== null && sentiment !== void 0 && sentiment.success) setSentimentData(sentiment.data);
                if (tech !== null && tech !== void 0 && tech.success) setTechnicalIndicators(tech.data);
            } catch (error) {
                _logger.default.error('Failed to fetch market analysis data:', error);
            }
        };
        fetchData();
        const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds

        return () => clearInterval(interval);
    }, []);

    /**
     * Handles tab changes by setting the active tab index.
     *
     * @param {object} event - The event object from the Material-UI Tabs component.
     * @param {number} newValue - The new active tab index.
     */
    const handleTabChange = (event, newValue) => {
        setActiveTab(newValue);
    };

    /**
     * Formats a given number as a percentage string with a green color for positive values and a red color for negative values, and a '+' sign for positive values.
     * @param {number} value - The number to format as a percentage.
     * @returns {React.ReactElement} - The formatted percentage string.
     */
    const formatPercentage = value => {
        if (typeof value !== 'number' || isNaN(value)) {
            return /*#__PURE__*/_react.default.createElement('span', {
                style: {
                    color: '#888',
                    fontWeight: 600
                }
            }, 'N/A');
        }
        const color = value >= 0 ? '#4caf50' : '#f44336';
        const sign = value >= 0 ? '+' : '';
        return /*#__PURE__*/_react.default.createElement('span', {
            style: {
                color,
                fontWeight: 600
            }
        }, sign, value.toFixed(2), '%');
    };

    /**
     * Gets the radar chart data for market health visualization.
     * @returns {Array<{metric: string, value: number}>} The radar chart data.
     */
    const getRadarData = () => [{
        metric: 'Sentiment',
        value: sentimentData.overall || 0
    }, {
        metric: 'Volume',
        value: 75
    }, {
        metric: 'Volatility',
        value: 60
    }, {
        metric: 'Liquidity',
        value: 85
    }, {
        metric: 'Technical',
        value: technicalIndicators.rsi || 50
    }, {
        metric: 'Social',
        value: 70
    }];

    /**
     * Gets the heatmap chart data for sentiment vs volume visualization.
     * @returns {Array<{x: number, y: number, z: number, symbol: string}>} The heatmap chart data.
     */
    const getHeatmapData = () => marketData.map((coin, index) => ({
        x: index,
        y: coin.sentiment || 0,
        z: coin.volume || 0,
        symbol: coin.symbol || 'Unknown'
    }));

    /**
     * @param {number} sentiment - Sentiment value
     * @returns {string} Sentiment color
     */
    const getSentimentColor = sentiment => {
        if (sentiment >= 70) return '#4caf50';
        if (sentiment >= 40) return '#ffc107';
        return '#f44336';
    };

    /**
     * Gets the RSI status (Overbought, Oversold, Neutral) based on the given RSI value.
     * @param {number} rsi - The RSI value to get the status for.
     * @returns {string} The RSI status as 'Overbought', 'Oversold', or 'Neutral'.
     */
    const getRsiStatus = rsi => {
        if (rsi > 70) return 'Overbought';
        if (rsi < 30) return 'Oversold';
        return 'Neutral';
    };
    return /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 4,
            minHeight: '100vh'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 4
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        sx: {
            background: 'linear-gradient(45deg, #00eaff 30%, #a259ff 70%, #ff6b6b 90%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontWeight: 800,
            mb: 1,
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Analytics, {
        sx: {
            mr: 2,
            color: '#a259ff'
        }
    }), 'Advanced Market Analysis'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#888',
            fontWeight: 300
        }
    }, 'AI-Powered Market Intelligence & Sentiment Analysis')), /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'medium',
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Tabs, {
        value: activeTab,
        onChange: handleTabChange,
        sx: {
            borderBottom: '1px solid rgba(162,89,255,0.3)',
            '& .MuiTab-root': {
                color: '#888',
                fontWeight: 600,
                '&.Mui-selected': {
                    color: '#a259ff'
                }
            },
            '& .MuiTabs-indicator': {
                backgroundColor: '#a259ff'
            }
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Tab, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.ShowChart, null),
        label: 'Market Overview'
    }), /*#__PURE__*/_react.default.createElement(_material.Tab, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Psychology, null),
        label: 'Sentiment Analysis'
    }), /*#__PURE__*/_react.default.createElement(_material.Tab, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Timeline, null),
        label: 'Technical Analysis'
    }), /*#__PURE__*/_react.default.createElement(_material.Tab, {
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.LocalFireDepartment, null),
        label: 'Social Trends'
    })), /*#__PURE__*/_react.default.createElement(TabPanel, {
        value: activeTab,
        index: 0
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 2,
        sx: {
            mb: 3
        }
    }, [{
        label: 'Market Sentiment',
        value: `${((_sentimentData$overal = sentimentData.overall) === null || _sentimentData$overal === void 0 ? void 0 : _sentimentData$overal.toFixed(1)) || 0}%`,
        color: getSentimentColor(sentimentData.overall),
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Psychology, null)
    }, {
        label: 'Fear & Greed',
        value: `${sentimentData.greed || 0}/100`,
        color: '#ffc107',
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Speed, null)
    }, {
        label: 'Active Traders',
        value: '12.4K',
        color: '#00eaff',
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Visibility, null)
    }, {
        label: 'Hot Coins',
        value: marketData.length,
        color: '#ff6b6b',
        icon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.LocalFireDepartment, null)
    }].map(metric => /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6,
        md: 3,
        key: `metric-${metric.label}`
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'medium',
        sx: {
            background: `linear-gradient(135deg, ${(0, _material.alpha)(metric.color, 0.15)} 0%, ${(0, _material.alpha)(metric.color, 0.05)} 100%)`,
            border: `1px solid ${(0, _material.alpha)(metric.color, 0.3)}`,
            p: 2,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'center',
            mb: 1
        }
    }, _react.default.cloneElement(metric.icon, {
        sx: {
            color: metric.color,
            fontSize: '2rem'
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h5',
        sx: {
            color: metric.color,
            fontWeight: 800,
            mb: 0.5
        }
    }, metric.value), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, metric.label)))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'medium',
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#00eaff',
            mb: 2,
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Insights, {
        sx: {
            mr: 1
        }
    }), 'Market Health Radar'), /*#__PURE__*/_react.default.createElement(_recharts.ResponsiveContainer, {
        width: '100%',
        height: 300
    }, /*#__PURE__*/_react.default.createElement(_recharts.RadarChart, {
        data: getRadarData()
    }, /*#__PURE__*/_react.default.createElement(_recharts.PolarGrid, null), /*#__PURE__*/_react.default.createElement(_recharts.PolarAngleAxis, {
        dataKey: 'metric'
    }), /*#__PURE__*/_react.default.createElement(_recharts.PolarRadiusAxis, null), /*#__PURE__*/_react.default.createElement(_recharts.Radar, {
        dataKey: 'value'
    }))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'medium',
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#4caf50',
            mb: 2,
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.TrendingUp, {
        sx: {
            mr: 1
        }
    }), 'Top Performers'), /*#__PURE__*/_react.default.createElement(_material.List, null, marketData.slice().sort((a, b) => (b.change24h || 0) - (a.change24h || 0)).slice(0, 4).map(coin => /*#__PURE__*/_react.default.createElement(_material.ListItem, {
        key: coin.symbol || `coin-${coin.name}`
    }, /*#__PURE__*/_react.default.createElement(_material.ListItemAvatar, null, /*#__PURE__*/_react.default.createElement(_material.Avatar, {
        sx: {
            backgroundColor: getSentimentColor(coin.sentiment || 0),
            width: 40,
            height: 40
        }
    }, (coin.symbol || '??').slice(0, 2))), /*#__PURE__*/_react.default.createElement(_material.ListItemText, {
        primary: /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#fff',
                fontWeight: 600
            }
        }, coin.symbol || 'Unknown'),
        secondary: /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#888',
                fontSize: '0.8rem'
            }
        }, (0, _formatters.formatCurrency)(coin.price || 0)), formatPercentage(coin.change24h || 0))
    })))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(_material.TableContainer, {
        component: _HolographicCard.default,
        variant: 'default',
        elevation: 'medium'
    }, /*#__PURE__*/_react.default.createElement(_material.Table, null, /*#__PURE__*/_react.default.createElement(_material.TableHead, null, /*#__PURE__*/_react.default.createElement(_material.TableRow, null, /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#a259ff',
            fontWeight: 600
        }
    }, 'Asset'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#a259ff',
            fontWeight: 600
        }
    }, 'Price'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#a259ff',
            fontWeight: 600
        }
    }, '24h Change'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#a259ff',
            fontWeight: 600
        }
    }, 'Volume'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#a259ff',
            fontWeight: 600
        }
    }, 'Market Cap'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#a259ff',
            fontWeight: 600
        }
    }, 'Sentiment'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#a259ff',
            fontWeight: 600
        }
    }, 'Score'))), /*#__PURE__*/_react.default.createElement(_material.TableBody, null, marketData.map((coin, index) => /*#__PURE__*/_react.default.createElement(_material.TableRow, {
        key: coin.symbol || `coin-${index}`,
        sx: {
            '&:hover': {
                backgroundColor: 'rgba(162,89,255,0.05)'
            }
        }
    }, /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Avatar, {
        sx: {
            width: 32,
            height: 32,
            mr: 1,
            backgroundColor: getSentimentColor(coin.sentiment || 0),
            fontSize: '0.8rem'
        }
    }, (coin.symbol || '??').slice(0, 2)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        sx: {
            color: '#fff',
            fontWeight: 600
        }
    }, coin.symbol || 'Unknown'))), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#fff'
        }
    }, (0, _formatters.formatCurrency)(coin.price || 0)), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, formatPercentage(coin.change24h || 0)), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#fff'
        }
    }, (0, _formatters.formatCurrency)(coin.volume || 0)), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#fff'
        }
    }, (0, _formatters.formatCurrency)(coin.marketCap || 0)), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Chip, {
        label: `${(coin.sentiment || 0).toFixed(0)}%`,
        size: 'small',
        sx: {
            backgroundColor: `${getSentimentColor(coin.sentiment || 0)}20`,
            color: getSentimentColor(coin.sentiment || 0),
            fontWeight: 600
        }
    })), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        variant: 'determinate',
        value: coin.technicalScore || 0,
        sx: {
            width: 60,
            mr: 1,
            backgroundColor: 'rgba(162,89,255,0.2)',
            '& .MuiLinearProgress-bar': {
                backgroundColor: '#a259ff'
            }
        }
    }), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'caption',
        sx: {
            color: '#888'
        }
    }, (coin.technicalScore || 0).toFixed(0)))))))))))), /*#__PURE__*/_react.default.createElement(TabPanel, {
        value: activeTab,
        index: 1
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'medium',
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#ffc107',
            mb: 2
        }
    }, 'Fear & Greed Index'), /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            mb: 3
        }
    }, [{
        label: 'Extreme Greed',
        value: sentimentData.greed,
        color: '#4caf50'
    }, {
        label: 'Neutral',
        value: sentimentData.neutral,
        color: '#ffc107'
    }, {
        label: 'Fear',
        value: sentimentData.fear,
        color: '#f44336'
    }].map(sentiment => /*#__PURE__*/_react.default.createElement(_material.Box, {
        key: sentiment.label,
        sx: {
            mb: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            justifyContent: 'space-between',
            mb: 0.5
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, sentiment.label), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: sentiment.color,
            fontWeight: 600
        }
    }, sentiment.value, '%')), /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        variant: 'determinate',
        value: sentiment.value,
        sx: {
            height: 8,
            borderRadius: 4,
            backgroundColor: 'rgba(255,255,255,0.1)',
            '& .MuiLinearProgress-bar': {
                backgroundColor: sentiment.color,
                borderRadius: 4
            }
        }
    })))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'medium',
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#00eaff',
            mb: 2
        }
    }, 'Trending Topics'), /*#__PURE__*/_react.default.createElement(_material.List, null, (_sentimentData$trendi = sentimentData.trending) === null || _sentimentData$trendi === void 0 ? void 0 : _sentimentData$trendi.map((/** @type {any} */topic) => /*#__PURE__*/_react.default.createElement(_material.ListItem, {
        key: topic.topic,
        sx: {
            px: 0
        }
    }, /*#__PURE__*/_react.default.createElement(_material.ListItemAvatar, null, /*#__PURE__*/_react.default.createElement(_material.Avatar, {
        sx: {
            backgroundColor: getSentimentColor(topic.sentiment),
            width: 40,
            height: 40
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.LocalFireDepartment, null))), /*#__PURE__*/_react.default.createElement(_material.ListItemText, {
        primary: /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#fff',
                fontWeight: 600
            }
        }, topic.topic),
        secondary: /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            sx: {
                color: '#888',
                fontSize: '0.8rem'
            }
        }, topic.mentions.toLocaleString(), ' mentions'), /*#__PURE__*/_react.default.createElement(_material.Chip, {
            label: `${topic.sentiment}%`,
            size: 'small',
            sx: {
                backgroundColor: `${getSentimentColor(topic.sentiment)}20`,
                color: getSentimentColor(topic.sentiment)
            }
        }))
    })))))))), /*#__PURE__*/_react.default.createElement(TabPanel, {
        value: activeTab,
        index: 2
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'medium',
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#a259ff',
            mb: 2
        }
    }, 'Key Technical Indicators'), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, [{
        label: 'RSI',
        value: technicalIndicators.rsi || 0,
        status: getRsiStatus(technicalIndicators.rsi || 0)
    }, {
        label: 'Convergence Divergence',
        value: technicalIndicators.macd || 'Unknown',
        status: technicalIndicators.macd || 'Unknown'
    }, {
        label: 'Volatility Bands',
        value: technicalIndicators.bollingerBands || 'Unknown',
        status: technicalIndicators.bollingerBands || 'Unknown'
    }, {
        label: 'Stochastic',
        value: technicalIndicators.stochastic || 0,
        status: (technicalIndicators.stochastic || 0) > 80 ? 'Overbought' : 'Normal'
    }, {
        label: 'Support Level',
        value: (0, _formatters.formatCurrency)(technicalIndicators.support || 0),
        status: 'Strong'
    }, {
        label: 'Resistance Level',
        value: (0, _formatters.formatCurrency)(technicalIndicators.resistance || 0),
        status: 'Moderate'
    }, {
        label: 'Trend Direction',
        value: technicalIndicators.trend || 'Unknown',
        status: technicalIndicators.trend || 'Unknown'
    }, {
        label: 'Trend Strength',
        value: `${technicalIndicators.strength || 0}/10`,
        status: 'Moderate'
    }].map(indicator => /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6,
        md: 3,
        key: `indicator-${indicator.label}`
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'low',
        sx: {
            background: 'linear-gradient(135deg, rgba(162,89,255,0.15) 0%, rgba(162,89,255,0.05) 100%)',
            border: '1px solid rgba(162,89,255,0.3)',
            p: 2,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888',
            mb: 1
        }
    }, indicator.label), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#a259ff',
            fontWeight: 800,
            mb: 1
        }
    }, indicator.value), /*#__PURE__*/_react.default.createElement(_material.Chip, {
        label: indicator.status,
        size: 'small',
        sx: {
            backgroundColor: 'rgba(162,89,255,0.2)',
            color: '#a259ff',
            fontWeight: 600
        }
    }))))))))), /*#__PURE__*/_react.default.createElement(TabPanel, {
        value: activeTab,
        index: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'default',
        elevation: 'medium',
        sx: {
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#ff6b6b',
            mb: 2
        }
    }, 'Sentiment vs. Volume'), /*#__PURE__*/_react.default.createElement(_recharts.ResponsiveContainer, {
        width: '100%',
        height: 400
    }, /*#__PURE__*/_react.default.createElement(_recharts.ScatterChart, null, /*#__PURE__*/_react.default.createElement(_recharts.CartesianGrid, null), /*#__PURE__*/_react.default.createElement(_recharts.XAxis, {
        dataKey: 'x',
        type: 'number',
        name: 'coin'
    }), /*#__PURE__*/_react.default.createElement(_recharts.YAxis, {
        dataKey: 'y',
        type: 'number',
        name: 'sentiment',
        unit: '%'
    }), /*#__PURE__*/_react.default.createElement(_recharts.Tooltip, {
        content: props => /*#__PURE__*/_react.default.createElement(CustomTooltip, props)
    }), /*#__PURE__*/_react.default.createElement(_recharts.Scatter, {
        data: getHeatmapData()
    })))))))));
}

MarketAnalysis.propTypes = {};