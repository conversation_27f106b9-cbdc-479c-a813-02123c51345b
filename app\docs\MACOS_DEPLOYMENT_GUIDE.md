# macOS Production Deployment Guide

## Overview

This guide provides detailed instructions for deploying the Meme Coin Trader application on macOS production environments.

## Prerequisites

### System Requirements

- **Operating System**: macOS 10.15 (Catalina) or later
- **Architecture**: x64 (Intel) or arm64 (Apple Silicon)
- **RAM**: Minimum 8GB, Recommended 16GB
- **Storage**: Minimum 10GB free space
- **Network**: Stable internet connection

### Development Tools

```bash
# Install Xcode Command Line Tools
xcode-select --install

# Install Homebrew (if not already installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Node.js
brew install node@18

# Install Python (required for native modules)
brew install python@3.11

# Install Git
brew install git
```

### Alternative Installation (Node Version Manager)

```bash
# Install nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Install and use Node.js 18
nvm install 18
nvm use 18
nvm alias default 18
```

## Build Environment Setup

### 1. Environment Variables

Create a shell script to set up environment variables:

```bash
#!/bin/bash
# setup-env.sh

export NODE_ENV="production"
export ELECTRON_ENV="production"
export npm_config_target_platform="darwin"
export npm_config_runtime="electron"
export npm_config_build_from_source="true"

# Architecture detection
if [[ $(uname -m) == "arm64" ]]; then
    export npm_config_target_arch="arm64"
    export ELECTRON_ARCH="arm64"
else
    export npm_config_target_arch="x64"
    export ELECTRON_ARCH="x64"
fi

# Python configuration
export npm_config_python="python3"

echo "Environment variables set for macOS production build"
echo "Architecture: $npm_config_target_arch"
```

### 2. Native Module Compilation

```bash
# Make script executable
chmod +x setup-env.sh
source setup-env.sh

# Rebuild native modules for Electron
npm rebuild --runtime=electron --target=31.2.1 --disturl=https://electronjs.org/headers --build-from-source

# Specifically rebuild SQLite3 for the correct architecture
npm rebuild better-sqlite3 --runtime=electron --target=31.2.1 --disturl=https://electronjs.org/headers --build-from-source
```

## Code Signing Setup

### 1. Apple Developer Account

- Enroll in Apple Developer Program ($99/year)
- Create App ID in Apple Developer Console
- Generate certificates and provisioning profiles

### 2. Certificate Installation

```bash
# List available certificates
security find-identity -v -p codesigning

# Import certificate (if not already in Keychain)
security import certificate.p12 -k ~/Library/Keychains/login.keychain -P certificate_password
```

### 3. Notarization Setup

```bash
# Create app-specific password for notarization
# Go to appleid.apple.com > Sign-In and Security > App-Specific Passwords

# Store credentials in Keychain
xcrun notarytool store-credentials "notarytool-profile" \
  --apple-id "<EMAIL>" \
  --team-id "YOUR_TEAM_ID" \
  --password "app-specific-password"
```

## Production Build Process

### 1. Clone and Setup

```bash
# Clone repository
git clone <repository-url>
cd electrontrader-monorepo

# Install dependencies
npm install

# Setup app dependencies
cd app
npm install

# Setup trading engine dependencies
cd trading
npm install
cd ..
```

### 2. Build Configuration

Create `electron-builder.config.js` for macOS:

```javascript
module.exports = {
  appId: "com.memetrader.app",
  productName: "Meme Coin Trader",
  directories: {
    output: "dist"
  },
  files: [
    "build/**/*",
    "trading/**/*",
    "main.js",
    "preload.js",
    "package.json",
    "!trading/node_modules/**/*",
    "!**/*.map"
  ],
  mac: {
    target: [
      {
        target: "dmg",
        arch: ["x64", "arm64"]
      },
      {
        target: "zip",
        arch: ["x64", "arm64"]
      },
      {
        target: "pkg",
        arch: ["x64", "arm64"]
      }
    ],
    icon: "assets/icon.icns",
    category: "public.app-category.finance",
    bundleVersion: "1.0.0",
    minimumSystemVersion: "10.15.0",
    darkModeSupport: true,
    hardenedRuntime: true,
    gatekeeperAssess: false,
    entitlements: "entitlements.mac.plist",
    entitlementsInherit: "entitlements.mac.plist",
    notarize: {
      teamId: "YOUR_TEAM_ID"
    }
  },
  dmg: {
    title: "${productName} ${version}",
    icon: "assets/dmg-icon.icns",
    background: "assets/dmg-background.png",
    contents: [
      {
        x: 130,
        y: 220
      },
      {
        x: 410,
        y: 220,
        type: "link",
        path: "/Applications"
      }
    ],
    window: {
      width: 540,
      height: 380
    }
  },
  pkg: {
    license: "LICENSE.txt",
    allowAnywhere: false,
    allowCurrentUserHome: false,
    allowRootDirectory: false
  }
};
```

### 3. Entitlements Configuration

Create `entitlements.mac.plist`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>com.apple.security.cs.allow-jit</key>
    <true/>
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <true/>
    <key>com.apple.security.cs.allow-dyld-environment-variables</key>
    <true/>
    <key>com.apple.security.network.client</key>
    <true/>
    <key>com.apple.security.network.server</key>
    <true/>
    <key>com.apple.security.files.user-selected.read-write</key>
    <true/>
    <key>com.apple.security.files.downloads.read-write</key>
    <true/>
</dict>
</plist>
```

### 4. Build Commands

```bash
# Build React application
npm run build:production

# Package for macOS (unsigned)
npm run package:mac

# Build and sign for macOS
npm run dist:mac

# Build universal binary (Intel + Apple Silicon)
npm run dist:mac:universal
```

### 5. Advanced Build Script

Create `build-macos.sh`:

```bash
#!/bin/bash
# build-macos.sh

VERSION=${1:-"1.0.0"}
SIGN=${2:-false}
NOTARIZE=${3:-false}
UNIVERSAL=${4:-false}

echo "Building Meme Coin Trader for macOS v$VERSION"

# Set environment
export NODE_ENV="production"
export ELECTRON_ENV="production"

# Detect architecture
ARCH=$(uname -m)
if [[ "$ARCH" == "arm64" ]]; then
    echo "Building on Apple Silicon"
    export npm_config_target_arch="arm64"
else
    echo "Building on Intel"
    export npm_config_target_arch="x64"
fi

# Clean previous builds
echo "Cleaning previous builds..."
rm -rf dist build

# Install dependencies
echo "Installing dependencies..."
npm ci --production=false

# Build React app
echo "Building React application..."
npm run build:production

# Rebuild native modules
echo "Rebuilding native modules..."
npm rebuild --runtime=electron --target=31.2.1 --disturl=https://electronjs.org/headers --build-from-source

# Package application
echo "Packaging Electron application..."
if [[ "$UNIVERSAL" == "true" ]]; then
    npm run dist:mac:universal
elif [[ "$SIGN" == "true" ]]; then
    if [[ "$NOTARIZE" == "true" ]]; then
        npm run dist:mac:signed:notarized
    else
        npm run dist:mac:signed
    fi
else
    npm run dist:mac
fi

# Verify build
echo "Verifying build..."
if [[ ! -d "dist" ]]; then
    echo "Error: dist directory not found"
    exit 1
fi

DIST_FILES=$(find dist -name "*.dmg" -o -name "*.zip" -o -name "*.pkg")
if [[ -z "$DIST_FILES" ]]; then
    echo "Error: No distribution files found"
    exit 1
fi

echo "Build completed successfully!"
echo "Output files:"
for file in $DIST_FILES; do
    echo "  - $(basename "$file") ($(du -h "$file" | cut -f1))"
done

# Verify code signing
if [[ "$SIGN" == "true" ]]; then
    echo "Verifying code signature..."
    for file in $DIST_FILES; do
        if [[ "$file" == *.dmg ]]; then
            # Mount DMG and check app signature
            hdiutil attach "$file" -quiet
            APP_PATH="/Volumes/Meme Coin Trader/Meme Coin Trader.app"
            if codesign --verify --deep --strict "$APP_PATH" 2>/dev/null; then
                echo "✓ $(basename "$file") is properly signed"
            else
                echo "✗ $(basename "$file") signature verification failed"
            fi
            hdiutil detach "/Volumes/Meme Coin Trader" -quiet
        fi
    done
fi

echo "Build process completed!"
```

## Code Signing and Notarization

### 1. Signing Process

```bash
# Sign the application
codesign --force --deep --sign "Developer ID Application: Your Name (TEAM_ID)" \
  --options runtime \
  --entitlements entitlements.mac.plist \
  "dist/mac/Meme Coin Trader.app"

# Verify signature
codesign --verify --deep --strict "dist/mac/Meme Coin Trader.app"
spctl --assess --type exec "dist/mac/Meme Coin Trader.app"
```

### 2. Notarization Process

```bash
# Create ZIP for notarization
ditto -c -k --keepParent "dist/mac/Meme Coin Trader.app" "MemeTrader.zip"

# Submit for notarization
xcrun notarytool submit "MemeTrader.zip" \
  --keychain-profile "notarytool-profile" \
  --wait

# Check notarization status
xcrun notarytool info SUBMISSION_ID --keychain-profile "notarytool-profile"

# Staple notarization ticket
xcrun stapler staple "dist/mac/Meme Coin Trader.app"

# Verify stapling
xcrun stapler validate "dist/mac/Meme Coin Trader.app"
```

### 3. Automated Signing Script

Create `sign-and-notarize.sh`:

```bash
#!/bin/bash
# sign-and-notarize.sh

APP_PATH="$1"
DEVELOPER_ID="$2"
KEYCHAIN_PROFILE="$3"

if [[ -z "$APP_PATH" || -z "$DEVELOPER_ID" || -z "$KEYCHAIN_PROFILE" ]]; then
    echo "Usage: $0 <app_path> <developer_id> <keychain_profile>"
    exit 1
fi

echo "Signing and notarizing $APP_PATH..."

# Sign the application
echo "Signing application..."
codesign --force --deep --sign "$DEVELOPER_ID" \
  --options runtime \
  --entitlements entitlements.mac.plist \
  "$APP_PATH"

# Verify signature
echo "Verifying signature..."
if ! codesign --verify --deep --strict "$APP_PATH"; then
    echo "Error: Signature verification failed"
    exit 1
fi

# Create ZIP for notarization
ZIP_PATH="$(dirname "$APP_PATH")/$(basename "$APP_PATH" .app).zip"
echo "Creating ZIP for notarization..."
ditto -c -k --keepParent "$APP_PATH" "$ZIP_PATH"

# Submit for notarization
echo "Submitting for notarization..."
SUBMISSION_ID=$(xcrun notarytool submit "$ZIP_PATH" \
  --keychain-profile "$KEYCHAIN_PROFILE" \
  --wait \
  --output-format json | jq -r '.id')

if [[ "$SUBMISSION_ID" == "null" ]]; then
    echo "Error: Notarization submission failed"
    exit 1
fi

echo "Notarization submitted with ID: $SUBMISSION_ID"

# Check status
echo "Checking notarization status..."
STATUS=$(xcrun notarytool info "$SUBMISSION_ID" \
  --keychain-profile "$KEYCHAIN_PROFILE" \
  --output-format json | jq -r '.status')

if [[ "$STATUS" == "Accepted" ]]; then
    echo "Notarization successful!"
    
    # Staple the ticket
    echo "Stapling notarization ticket..."
    xcrun stapler staple "$APP_PATH"
    
    # Verify stapling
    if xcrun stapler validate "$APP_PATH"; then
        echo "✓ Notarization ticket stapled successfully"
    else
        echo "⚠ Warning: Stapling verification failed"
    fi
else
    echo "Error: Notarization failed with status: $STATUS"
    xcrun notarytool log "$SUBMISSION_ID" --keychain-profile "$KEYCHAIN_PROFILE"
    exit 1
fi

# Cleanup
rm -f "$ZIP_PATH"

echo "Signing and notarization completed successfully!"
```

## Installation and Deployment

### 1. DMG Installation

```bash
# Mount DMG
hdiutil attach MemeTrader-1.0.0.dmg

# Copy to Applications (user installation)
cp -R "/Volumes/Meme Coin Trader/Meme Coin Trader.app" /Applications/

# Unmount DMG
hdiutil detach "/Volumes/Meme Coin Trader"
```

### 2. PKG Installation

```bash
# Install PKG (requires admin privileges)
sudo installer -pkg MemeTrader-1.0.0.pkg -target /

# Install for current user only
installer -pkg MemeTrader-1.0.0.pkg -target CurrentUserHomeDirectory
```

### 3. ZIP Installation

```bash
# Extract ZIP
unzip MemeTrader-1.0.0-mac.zip

# Move to Applications
mv "Meme Coin Trader.app" /Applications/
```

## Launch Daemon Setup

### 1. Create Launch Daemon

Create `com.memetrader.app.plist`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.memetrader.app</string>
    <key>ProgramArguments</key>
    <array>
        <string>/Applications/Meme Coin Trader.app/Contents/MacOS/Meme Coin Trader</string>
        <string>--headless</string>
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
    <key>StandardOutPath</key>
    <string>/usr/local/var/log/memetrader.log</string>
    <key>StandardErrorPath</key>
    <string>/usr/local/var/log/memetrader.error.log</string>
    <key>EnvironmentVariables</key>
    <dict>
        <key>NODE_ENV</key>
        <string>production</string>
        <key>ELECTRON_ENV</key>
        <string>production</string>
    </dict>
</dict>
</plist>
```

### 2. Install Launch Daemon

```bash
# Copy to LaunchDaemons directory
sudo cp com.memetrader.app.plist /Library/LaunchDaemons/

# Set permissions
sudo chown root:wheel /Library/LaunchDaemons/com.memetrader.app.plist
sudo chmod 644 /Library/LaunchDaemons/com.memetrader.app.plist

# Load daemon
sudo launchctl load /Library/LaunchDaemons/com.memetrader.app.plist

# Start daemon
sudo launchctl start com.memetrader.app
```

## Configuration Management

### 1. macOS Preferences

```bash
# Set application preferences
defaults write com.memetrader.app Environment "production"
defaults write com.memetrader.app LogLevel "info"
defaults write com.memetrader.app AutoStart -bool true

# Read preferences
defaults read com.memetrader.app
```

### 2. Configuration File Locations

- **Application**: `/Applications/Meme Coin Trader.app`
- **User Configuration**: `~/Library/Application Support/meme-coin-trader`
- **User Preferences**: `~/Library/Preferences/com.memetrader.app.plist`
- **Logs**: `~/Library/Logs/meme-coin-trader`
- **Cache**: `~/Library/Caches/meme-coin-trader`

### 3. System-wide Configuration

```bash
# Create system configuration directory
sudo mkdir -p /usr/local/etc/memetrader

# Create configuration file
sudo tee /usr/local/etc/memetrader/config.json << EOF
{
  "environment": "production",
  "trading": {
    "mode": "live",
    "paperTrading": false
  },
  "database": {
    "path": "/usr/local/var/memetrader/trading.db"
  },
  "logging": {
    "level": "info",
    "path": "/usr/local/var/log/memetrader"
  }
}
EOF
```

## Performance Optimization

### 1. macOS-Specific Optimizations

```javascript
// macOS performance configuration
const macosConfig = {
  process: {
    priority: 10, // Higher priority
    niceness: -10, // Lower niceness value
  },
  memory: {
    maxOldSpaceSize: 4096, // 4GB heap limit
    maxSemiSpaceSize: 256,  // 256MB semi-space
  },
  io: {
    useKqueue: true, // Use kqueue for file watching
    bufferSize: 65536,
  }
};

// Apply macOS-specific optimizations
if (process.platform === 'darwin') {
  process.env.UV_THREADPOOL_SIZE = '16';
  process.env.NODE_OPTIONS = '--max-old-space-size=4096';
}
```

### 2. System Resource Limits

```bash
# Increase file descriptor limits
echo "kern.maxfiles=65536" | sudo tee -a /etc/sysctl.conf
echo "kern.maxfilesperproc=32768" | sudo tee -a /etc/sysctl.conf

# Apply changes
sudo sysctl -w kern.maxfiles=65536
sudo sysctl -w kern.maxfilesperproc=32768
```

## Monitoring and Logging

### 1. macOS Console Integration

```javascript
// macOS Console logging
const os = require('os');
const { spawn } = require('child_process');

class MacOSLogger {
  constructor(subsystem = 'com.memetrader.app') {
    this.subsystem = subsystem;
  }

  log(level, message, category = 'general') {
    if (process.platform === 'darwin') {
      const logger = spawn('logger', [
        '-t', this.subsystem,
        '-p', `user.${level}`,
        message
      ]);
    }
    
    // Also log to console
    console.log(`[${level.toUpperCase()}] ${message}`);
  }

  info(message, category) {
    this.log('info', message, category);
  }

  warning(message, category) {
    this.log('warning', message, category);
  }

  error(message, category) {
    this.log('error', message, category);
  }
}

module.exports = MacOSLogger;
```

### 2. Activity Monitor Integration

```javascript
// System monitoring for Activity Monitor
const { execSync } = require('child_process');

class MacOSSystemMonitor {
  getProcessInfo() {
    try {
      const pid = process.pid;
      const psOutput = execSync(`ps -p ${pid} -o pid,ppid,pcpu,pmem,vsz,rss,comm`).toString();
      return psOutput.split('\n')[1]; // Skip header
    } catch (error) {
      console.error('Failed to get process info:', error);
      return null;
    }
  }

  getSystemLoad() {
    try {
      const uptime = execSync('uptime').toString().trim();
      const match = uptime.match(/load averages: ([\d.]+) ([\d.]+) ([\d.]+)/);
      if (match) {
        return {
          load1: parseFloat(match[1]),
          load5: parseFloat(match[2]),
          load15: parseFloat(match[3])
        };
      }
    } catch (error) {
      console.error('Failed to get system load:', error);
    }
    return null;
  }
}
```

## Security Configuration

### 1. Gatekeeper and XProtect

```bash
# Check Gatekeeper status
spctl --status

# Assess application
spctl --assess --type exec "/Applications/Meme Coin Trader.app"

# Add to Gatekeeper exceptions (if needed for development)
sudo spctl --add "/Applications/Meme Coin Trader.app"
```

### 2. Keychain Access

```javascript
// macOS Keychain integration
const keychain = require('keychain');

class MacOSKeychainManager {
  async storeCredential(service, account, password) {
    return new Promise((resolve, reject) => {
      keychain.setPassword({
        account: account,
        service: service,
        password: password
      }, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }

  async getCredential(service, account) {
    return new Promise((resolve, reject) => {
      keychain.getPassword({
        account: account,
        service: service
      }, (err, password) => {
        if (err) reject(err);
        else resolve(password);
      });
    });
  }

  async deleteCredential(service, account) {
    return new Promise((resolve, reject) => {
      keychain.deletePassword({
        account: account,
        service: service
      }, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  }
}

module.exports = MacOSKeychainManager;
```

## Troubleshooting

### Common Issues

#### 1. Code Signing Errors

```bash
# Clear code signing cache
sudo rm -rf /var/db/DetachedSignatures

# Reset Gatekeeper
sudo spctl --master-disable
sudo spctl --master-enable

# Check certificate validity
security find-identity -v -p codesigning
```

#### 2. Notarization Issues

```bash
# Check notarization logs
xcrun notarytool log SUBMISSION_ID --keychain-profile "notarytool-profile"

# Common fixes:
# - Ensure hardened runtime is enabled
# - Check entitlements are correct
# - Verify all binaries are signed
# - Check for unsigned dylibs
```

#### 3. Native Module Issues

```bash
# Clear npm cache
npm cache clean --force

# Rebuild for correct architecture
npm rebuild --arch=arm64  # For Apple Silicon
npm rebuild --arch=x64    # For Intel

# Check architecture
file node_modules/better-sqlite3/build/Release/better_sqlite3.node
```

### Diagnostic Tools

#### 1. System Information Script

```bash
#!/bin/bash
# system-info-macos.sh

echo "=== macOS System Information ==="
echo "OS Version: $(sw_vers -productVersion)"
echo "Build: $(sw_vers -buildVersion)"
echo "Architecture: $(uname -m)"
echo "Kernel: $(uname -r)"

echo -e "\n=== Hardware Information ==="
echo "Model: $(sysctl -n hw.model)"
echo "CPU: $(sysctl -n machdep.cpu.brand_string)"
echo "Cores: $(sysctl -n hw.ncpu)"
echo "Memory: $(echo "$(sysctl -n hw.memsize) / 1024 / 1024 / 1024" | bc) GB"

echo -e "\n=== Development Tools ==="
echo "Xcode: $(xcode-select -p 2>/dev/null || echo "Not installed")"
echo "Node.js: $(node --version 2>/dev/null || echo "Not installed")"
echo "npm: $(npm --version 2>/dev/null || echo "Not installed")"
echo "Python: $(python3 --version 2>/dev/null || echo "Not installed")"

echo -e "\n=== Code Signing ==="
security find-identity -v -p codesigning | head -5

echo -e "\n=== Environment Variables ==="
env | grep -E "(NODE|npm|ELECTRON)" | sort
```

#### 2. Build Verification Script

```bash
#!/bin/bash
# verify-build-macos.sh

BUILD_PATH=${1:-"dist"}

echo "Verifying macOS build in $BUILD_PATH..."

# Check if build directory exists
if [[ ! -d "$BUILD_PATH" ]]; then
    echo "❌ Build directory not found!"
    exit 1
fi

# Find distribution files
DMG_FILES=$(find "$BUILD_PATH" -name "*.dmg")
ZIP_FILES=$(find "$BUILD_PATH" -name "*.zip")
PKG_FILES=$(find "$BUILD_PATH" -name "*.pkg")

echo "Found distribution files:"
for file in $DMG_FILES $ZIP_FILES $PKG_FILES; do
    if [[ -f "$file" ]]; then
        SIZE=$(du -h "$file" | cut -f1)
        echo "  ✓ $(basename "$file") ($SIZE)"
    fi
done

# Verify code signing for DMG files
for dmg in $DMG_FILES; do
    if [[ -f "$dmg" ]]; then
        echo "Verifying code signature for $(basename "$dmg")..."
        
        # Mount DMG
        MOUNT_POINT=$(hdiutil attach "$dmg" -nobrowse -quiet | grep "/Volumes" | cut -f3)
        
        if [[ -n "$MOUNT_POINT" ]]; then
            APP_PATH="$MOUNT_POINT/Meme Coin Trader.app"
            
            if [[ -d "$APP_PATH" ]]; then
                # Check code signature
                if codesign --verify --deep --strict "$APP_PATH" 2>/dev/null; then
                    echo "  ✅ Code signature valid"
                else
                    echo "  ❌ Code signature invalid or missing"
                fi
                
                # Check notarization
                if xcrun stapler validate "$APP_PATH" 2>/dev/null; then
                    echo "  ✅ Notarization ticket present"
                else
                    echo "  ⚠️  Notarization ticket missing"
                fi
                
                # Check Gatekeeper assessment
                if spctl --assess --type exec "$APP_PATH" 2>/dev/null; then
                    echo "  ✅ Gatekeeper assessment passed"
                else
                    echo "  ❌ Gatekeeper assessment failed"
                fi
            fi
            
            # Unmount DMG
            hdiutil detach "$MOUNT_POINT" -quiet
        fi
    fi
done

echo "Build verification completed!"
```

## Automated Deployment

### 1. Deployment Script

```bash
#!/bin/bash
# deploy-macos.sh

VERSION="$1"
TARGET_PATH="/Applications"
CREATE_DAEMON="$2"

if [[ -z "$VERSION" ]]; then
    echo "Usage: $0 <version> [create_daemon]"
    exit 1
fi

echo "Deploying Meme Coin Trader v$VERSION to macOS"

# Download DMG
DMG_URL="https://releases.example.com/MemeTrader-$VERSION.dmg"
DMG_PATH="/tmp/MemeTrader-$VERSION.dmg"

echo "Downloading installer..."
if ! curl -L "$DMG_URL" -o "$DMG_PATH"; then
    echo "❌ Failed to download installer"
    exit 1
fi

# Verify download
if [[ ! -f "$DMG_PATH" ]]; then
    echo "❌ Installer not found after download"
    exit 1
fi

# Mount DMG
echo "Mounting installer..."
MOUNT_POINT=$(hdiutil attach "$DMG_PATH" -nobrowse -quiet | grep "/Volumes" | cut -f3)

if [[ -z "$MOUNT_POINT" ]]; then
    echo "❌ Failed to mount installer"
    exit 1
fi

# Stop existing application
echo "Stopping existing application..."
pkill -f "Meme Coin Trader" || true

# Install application
echo "Installing application..."
if [[ -d "$TARGET_PATH/Meme Coin Trader.app" ]]; then
    rm -rf "$TARGET_PATH/Meme Coin Trader.app"
fi

cp -R "$MOUNT_POINT/Meme Coin Trader.app" "$TARGET_PATH/"

# Unmount DMG
hdiutil detach "$MOUNT_POINT" -quiet

# Verify installation
if [[ ! -d "$TARGET_PATH/Meme Coin Trader.app" ]]; then
    echo "❌ Installation failed"
    exit 1
fi

# Create launch daemon if requested
if [[ "$CREATE_DAEMON" == "true" ]]; then
    echo "Creating launch daemon..."
    # Launch daemon creation logic here
fi

# Cleanup
rm -f "$DMG_PATH"

echo "✅ Deployment completed successfully!"
echo "Application installed at: $TARGET_PATH/Meme Coin Trader.app"
```

### 2. Update Script

```bash
#!/bin/bash
# update-macos.sh

NEW_VERSION="$1"

if [[ -z "$NEW_VERSION" ]]; then
    echo "Usage: $0 <new_version>"
    exit 1
fi

echo "Updating Meme Coin Trader to version $NEW_VERSION"

# Backup current installation
BACKUP_PATH="$HOME/Desktop/MemeTrader-Backup-$(date +%Y%m%d-%H%M%S)"
if [[ -d "/Applications/Meme Coin Trader.app" ]]; then
    echo "Creating backup..."
    cp -R "/Applications/Meme Coin Trader.app" "$BACKUP_PATH"
fi

# Download and install update
./deploy-macos.sh "$NEW_VERSION"

echo "✅ Update completed successfully!"
echo "Backup created at: $BACKUP_PATH"
```

### 3. Health Check Script

```bash
#!/bin/bash
# health-check-macos.sh

echo "Performing macOS health check..."

# Check if application is installed
if [[ -d "/Applications/Meme Coin Trader.app" ]]; then
    echo "✅ Application is installed"
else
    echo "❌ Application is not installed"
    exit 1
fi

# Check if application is running
if pgrep -f "Meme Coin Trader" > /dev/null; then
    PID=$(pgrep -f "Meme Coin Trader")
    echo "✅ Application is running (PID: $PID)"
else
    echo "⚠️  Application is not running"
fi

# Check launch daemon
if [[ -f "/Library/LaunchDaemons/com.memetrader.app.plist" ]]; then
    STATUS=$(sudo launchctl list | grep com.memetrader.app || echo "not loaded")
    echo "✅ Launch daemon status: $STATUS"
else
    echo "⚠️  Launch daemon not installed"
fi

# Check API endpoint
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    echo "✅ API endpoint responding"
else
    echo "❌ API endpoint not responding"
fi

# Check log files
LOG_PATH="$HOME/Library/Logs/meme-coin-trader"
if [[ -d "$LOG_PATH" ]]; then
    LOG_COUNT=$(find "$LOG_PATH" -name "*.log" | wc -l)
    echo "✅ Found $LOG_COUNT log files"
else
    echo "⚠️  Log directory not found"
fi

# Check system resources
MEMORY_USAGE=$(ps -o pid,ppid,pmem,comm -p $(pgrep -f "Meme Coin Trader") 2>/dev/null | tail -1 | awk '{print $3}')
if [[ -n "$MEMORY_USAGE" ]]; then
    echo "✅ Memory usage: ${MEMORY_USAGE}%"
fi

echo "Health check completed!"
```

---

*This macOS deployment guide is part of the comprehensive production deployment documentation. For other platforms, see the Windows and Linux deployment guides.*