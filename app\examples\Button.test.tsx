import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => {
  const mockReact = jest.requireActual('react') as typeof React;
  return {
    motion: {
      button: mockReact.forwardRef<HTMLButtonElement, any>((props: any, ref: any) => 
        mockReact.createElement('button', { ...props, ref, type: 'button' })
      ),
    },
  };
});

describe('Button Component', () => {
  const mockOnClick = jest.fn();

  beforeEach(() => {
    mockOnClick.mockClear();
  });

  it('should render button with children', () => {
    render(
      <Button onClick={mockOnClick} variant="primary" size="medium">
        Click me
      </Button>
    );

    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument();
  });

  it('should call onClick when clicked', () => {
    render(
      <Button onClick={mockOnClick} variant="primary" size="medium">
        Click me
      </Button>
    );

    fireEvent.click(screen.getByRole('button'));
    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('should not call onClick when disabled', () => {
    render(
      <Button onClick={mockOnClick} variant="primary" size="medium" disabled>
        Click me
      </Button>
    );

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    
    fireEvent.click(button);
    expect(mockOnClick).not.toHaveBeenCalled();
  });

  it('should not call onClick when loading', () => {
    render(
      <Button onClick={mockOnClick} variant="primary" size="medium" loading>
        Click me
      </Button>
    );

    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    
    fireEvent.click(button);
    expect(mockOnClick).not.toHaveBeenCalled();
  });

  it('should show loading spinner when loading', () => {
    render(
      <Button onClick={mockOnClick} variant="primary" size="medium" loading>
        Click me
      </Button>
    );

    // Check for loading spinner SVG
    const spinner = screen.getByRole('button').querySelector('svg');
    expect(spinner).toBeInTheDocument();
    expect(spinner).toHaveClass('animate-spin');
  });

  it('should render icon when provided', () => {
    const TestIcon = () => <span data-testid="test-icon">🚀</span>;
    
    render(
      <Button 
        onClick={mockOnClick} 
        variant="primary" 
        size="medium"
        icon={<TestIcon />}
      >
        Click me
      </Button>
    );

    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  });

  it('should not render icon when loading', () => {
    const TestIcon = () => <span data-testid="test-icon">🚀</span>;
    
    render(
      <Button 
        onClick={mockOnClick} 
        variant="primary" 
        size="medium"
        icon={<TestIcon />}
        loading
      >
        Click me
      </Button>
    );

    expect(screen.queryByTestId('test-icon')).not.toBeInTheDocument();
  });

  it('should apply correct variant classes', () => {
    const { rerender } = render(
      <Button onClick={mockOnClick} variant="primary" size="medium">
        Primary
      </Button>
    );

    let button = screen.getByRole('button');
    expect(button).toHaveClass('bg-blue-600');

    rerender(
      <Button onClick={mockOnClick} variant="secondary" size="medium">
        Secondary
      </Button>
    );

    button = screen.getByRole('button');
    expect(button).toHaveClass('bg-gray-200');

    rerender(
      <Button onClick={mockOnClick} variant="danger" size="medium">
        Danger
      </Button>
    );

    button = screen.getByRole('button');
    expect(button).toHaveClass('bg-red-600');
  });

  it('should apply correct size classes', () => {
    const { rerender } = render(
      <Button onClick={mockOnClick} variant="primary" size="small">
        Small
      </Button>
    );

    let button = screen.getByRole('button');
    expect(button).toHaveClass('px-3', 'py-1.5', 'text-sm');

    rerender(
      <Button onClick={mockOnClick} variant="primary" size="medium">
        Medium
      </Button>
    );

    button = screen.getByRole('button');
    expect(button).toHaveClass('px-4', 'py-2', 'text-base');

    rerender(
      <Button onClick={mockOnClick} variant="primary" size="large">
        Large
      </Button>
    );

    button = screen.getByRole('button');
    expect(button).toHaveClass('px-6', 'py-3', 'text-lg');
  });

  it('should apply disabled styles when disabled or loading', () => {
    const { rerender } = render(
      <Button onClick={mockOnClick} variant="primary" size="medium" disabled>
        Disabled
      </Button>
    );

    let button = screen.getByRole('button');
    expect(button).toHaveClass('opacity-50', 'cursor-not-allowed');

    rerender(
      <Button onClick={mockOnClick} variant="primary" size="medium" loading>
        Loading
      </Button>
    );

    button = screen.getByRole('button');
    expect(button).toHaveClass('opacity-50', 'cursor-not-allowed');
  });

  it('should have proper accessibility attributes', () => {
    render(
      <Button onClick={mockOnClick} variant="primary" size="medium">
        Accessible Button
      </Button>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('type', 'button');
    expect(button).not.toHaveAttribute('aria-disabled');
  });

  it('should handle keyboard interactions', () => {
    render(
      <Button onClick={mockOnClick} variant="primary" size="medium">
        Click me
      </Button>
    );

    const button = screen.getByRole('button');
    
    // Focus the button first
    button.focus();
    
    // Test Enter key
    fireEvent.keyDown(button, { key: 'Enter', code: 'Enter' });
    // Note: onClick is not called on keyDown by default for buttons
    
    // Test Space key
    fireEvent.keyDown(button, { key: ' ', code: 'Space' });
    // Note: onClick is not called on keyDown by default for buttons
    
    // The button should have focus after keyboard interactions
    expect(button).toHaveFocus();
  });
});