import React from 'react';
import {<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, CardContent, Typography} from '@mui/material';
import {BugReport, Home, Refresh} from '@mui/icons-material';
import {motion} from 'framer-motion';
import {ErrorReporter} from '../services/ErrorReporter.js';
// Import logger for consistent logging
import logger from '../utils/logger';


const errorReporter = new ErrorReporter();

class ErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            errorCount: 0,
            lastErrorTime: null
        };
    }

    static getDerivedStateFromError(error) {
        return {
            hasError: true,
            error,
            errorCount: 1,
            lastErrorTime: Date.now()
        };
    }

    componentDidCatch(error, errorInfo) {
        const _errorData = {
            error: error?.message || 'Unknown error',
            stack: error?.stack,
            componentStack: errorInfo?.componentStack,
            timestamp: new Date().toISOString(),
            component: this.props.componentName || 'Unknown',
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // Report the error
        errorReporter.report(_errorData);

        // Track error metrics
        this.trackErrorMetrics(_errorData);
    }

    trackErrorMetrics(_errorData) {
        // Track error frequency and patterns
        const now = Date.now();
        const timeSinceLastError = this.state.lastErrorTime ? now - this.state.lastErrorTime : null;

        if (timeSinceLastError && timeSinceLastError < 5000) {
            // Rapid error sequence - might be a loop
            logger.warn('Rapid error sequence detected', {errorCount: this.state.errorCount});
        }
    }

    handleRetry = () => {
        const {onRetry} = this.props;

        if (onRetry) {
            onRetry();
        } else {
            // Reset error state
            this.setState({
                hasError: false,
                error: null,
                errorInfo: null
            });
        }
    };

    handleGoHome = () => {
        const {onGoHome} = this.props;

        if (onGoHome) {
            onGoHome();
        } else {
            window.location.href = '/';
        }
    };

    handleReportBug = () => {
        const {error, errorInfo} = this.state;

        const bugReport = {
            error: error?.message,
            stack: error?.stack,
            componentStack: errorInfo?.componentStack,
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent
        };

        // Send bug report
        errorReporter.report({
            ...bugReport,
            type: 'user_reported_bug'
        });

        // Show confirmation
        this.setState({bugReportSent: true});
    };

    render() {
        const {hasError, error, errorInfo, errorCount, bugReportSent} = this.state;
        const {children, fallbackUI, showDetails = true} = this.props;

        if (!hasError) {
            return children;
        }

        // Use custom fallback if provided
        if (fallbackUI) {
            return React.cloneElement(fallbackUI, {
                error,
                errorInfo,
                onRetry: this.handleRetry,
                onGoHome: this.handleGoHome
            });
        }

        return (
            <Box
                sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: '100vh',
                    background: 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
                    p: 3
                }}
            >
                <motion.div
                    initial={{opacity: 0, y: 20}}
                    animate={{opacity: 1, y: 0}}
                    transition={{duration: 0.6}}
                    style={{width: '100%', maxWidth: '500px'}}
                >
                    <Card sx={{
                        background: 'rgba(24, 26, 32, 0.95)',
                        border: '1px solid rgba(244, 67, 54, 0.3)',
                        borderRadius: 3
                    }}>
                        <CardContent sx={{p: 4}}>
                            <Typography variant="h5" sx={{color: '#f44336', mb: 2, textAlign: 'center'}}>
                                Something went wrong
                            </Typography>

                            <Alert
                                severity="error"
                                sx={{
                                    mb: 3,
                                    backgroundColor: 'rgba(244, 67, 54, 0.1)',
                                    border: '1px solid rgba(244, 67, 54, 0.3)',
                                    color: 'white',
                                    '& .MuiAlert-icon': {color: '#f44336'}
                                }}
                            >
                                {error?.message || 'An unexpected error occurred'}
                            </Alert>

                            {showDetails && process.env.NODE_ENV === 'development' && (
                                <Box sx={{
                                    backgroundColor: 'rgba(0, 0, 0, 0.3)',
                                    borderRadius: 1,
                                    p: 2,
                                    fontFamily: 'monospace',
                                    fontSize: '0.8rem',
                                    overflow: 'auto',
                                    maxHeight: '200px'
                                }}>
                                    <Typography variant="caption" sx={{color: 'rgba(255, 255, 255, 0.7)'}}>
                                        Stack Trace:
                                    </Typography>
                                    <Typography variant="body2"
                                                sx={{color: 'rgba(255, 255, 255, 0.5)', whiteSpace: 'pre-wrap'}}>
                                        {error?.stack || 'No stack trace available'}
                                    </Typography>
                                </Box>
                            )}

                            <Box sx={{display: 'flex', gap: 2, flexDirection: 'column'}}>
                                <Button
                                    fullWidth
                                    variant="contained"
                                    startIcon={<Refresh/>}
                                    onClick={this.handleRetry}
                                    sx={{
                                        background: 'linear-gradient(45deg, #00eaff, #0088cc)',
                                        color: 'white',
                                        textTransform: 'none',
                                        borderRadius: 2
                                    }}
                                >
                                    Try Again
                                </Button>

                                <Button
                                    fullWidth
                                    variant="outlined"
                                    startIcon={<Home/>}
                                    onClick={this.handleGoHome}
                                    sx={{
                                        color: '#ff9800',
                                        borderColor: '#ff9800',
                                        textTransform: 'none',
                                        borderRadius: 2,
                                        '&:hover': {borderColor: '#ff9800', backgroundColor: 'rgba(255, 152, 0, 0.1)'}
                                    }}
                                >
                                    Go to Dashboard
                                </Button>

                                {!bugReportSent && (
                                    <Button
                                        fullWidth
                                        variant="text"
                                        startIcon={<BugReport/>}
                                        onClick={this.handleReportBug}
                                        sx={{
                                            color: 'rgba(255, 255, 255, 0.7)',
                                            textTransform: 'none',
                                            borderRadius: 2
                                        }}
                                    >
                                        Report Bug
                                    </Button>
                                )}

                                {bugReportSent && (
                                    <Typography variant="body2" sx={{color: '#4caf50', textAlign: 'center'}}>
                                        Bug report sent successfully!
                                    </Typography>
                                )}
                            </Box>

                            <Typography variant="caption" sx={{
                                color: 'rgba(255, 255, 255, 0.5)',
                                display: 'block',
                                textAlign: 'center',
                                mt: 2
                            }}>
                                Error Count: {errorCount}
                            </Typography>
                        </CardContent>
                    </Card>
                </motion.div>
            </Box>
        );
    }
}

// Component-specific error boundaries
export const DashboardErrorBoundary = ({children}) => (
    <ErrorBoundary componentName="Dashboard" fallbackUI={
        <Box sx={{p: 3}}>
            <Typography variant="h6" sx={{color: '#f44336'}}>
                Dashboard Error
            </Typography>
            <Typography variant="body2" sx={{color: 'rgba(255, 255, 255, 0.7)'}}>
                Unable to load dashboard. Please refresh or contact support.
            </Typography>
        </Box>
    }>
        {children}
    </ErrorBoundary>
);

export const TradingViewErrorBoundary = ({children}) => (
    <ErrorBoundary componentName="TradingView" fallbackUI={
        <Box sx={{p: 3, textAlign: 'center'}}>
            <Typography variant="h6" sx={{color: '#f44336', mb: 2}}>
                Trading View Error
            </Typography>
            <Typography variant="body2" sx={{color: 'rgba(255, 255, 255, 0.7)', mb: 3}}>
                Unable to load trading view. This might be due to connection issues.
            </Typography>
            <Button
                variant="contained"
                onClick={() => window.location.reload()}
                sx={{
                    background: 'linear-gradient(45deg, #00eaff, #0088cc)',
                    color: 'white',
                    textTransform: 'none'
                }}
            >
                Reload Page
            </Button>
        </Box>
    }>
        {children}
    </ErrorBoundary>
);

// Hook for error boundary context
export const useErrorBoundary = () => {
    const resetErrorBoundary = () => {
        // This would be used with React Error Boundaries context
        // Implementation depends on your state management
    };

    return {resetErrorBoundary};
};

export default ErrorBoundary;
