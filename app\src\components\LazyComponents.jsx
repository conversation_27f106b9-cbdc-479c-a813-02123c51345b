import React, {lazy, Suspense} from 'react';
import {Box, CircularProgress} from '@mui/material';
// Import logger for consistent logging
import logger from '../utils/logger';


// Enhanced lazy loading with intelligent preloading strategies
const createLazyComponent = (importFunc, options = {}) => {
    const {
        preloadDelay = 5000,
        retryAttempts = 3,
        retryDelay = 1000,
        timeout = 30000,
        priority = 'normal', // 'high', 'normal', 'low'
        preloadCondition = null, // Function to determine if preloading should occur
        chunkName = null, // Custom chunk name for webpack
    } = options;

    // Create lazy component with enhanced error handling
    const LazyComponent = lazy(() =>
        new Promise((resolve, reject) => {
            let attempts = 0;
            const startTime = performance.now();

            const loadComponent = async () => {
                try {
                    const module = await importFunc();

                    // Track loading performance
                    const loadTime = performance.now() - startTime;
                    if (process.env.NODE_ENV === 'development') {
                        logger.info(`Lazy component loaded in ${loadTime.toFixed(2)}ms (attempt ${attempts + 1})`);
                    }

                    resolve(module);
                } catch (error) {
                    attempts += 1;
                    if (attempts < retryAttempts) {
                        const delay = retryDelay * Math.pow(2, attempts - 1); // Exponential backoff
                        setTimeout(loadComponent, delay);
                    } else {
                        logger.error(`Failed to load component after ${attempts} attempts:`, error);
                        reject(error);
                    }
                }
            };

            // Set timeout for loading
            const timeoutId = setTimeout(() => {
                reject(new Error(`Component loading timeout after ${timeout}ms`));
            }, timeout);

            loadComponent().finally(() => clearTimeout(timeoutId));
        }),
    );

    // Intelligent preloading functionality
    const preload = () => {
        // Check preload condition if provided
        if (preloadCondition && !preloadCondition()) {
            return Promise.resolve();
        }

        // Adjust delay based on priority
        const adjustedDelay = priority === 'high' ? preloadDelay / 2 :
            priority === 'low' ? preloadDelay * 2 : preloadDelay;

        return new Promise((resolve) => {
            setTimeout(() => {
                importFunc()
                    .then(resolve)
                    .catch((error) => {
                        logger.warn('Preload failed:', error);
                        resolve();
                    });
            }, adjustedDelay);
        });
    };

    // Add chunk name for webpack if provided
    if (chunkName) {
        // @ts-ignore - displayName property exists on lazy components
        LazyComponent.displayName = chunkName;
    }

    return [LazyComponent, preload];
};

// Create loading fallback component
const LoadingFallback = ({size = 40, message = 'Loading...'}) => (
    <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        minHeight="200px"
        p={3}
    >
        <CircularProgress size={size}/>
        <Box mt={2} color="text.secondary">
            {message}
        </Box>
    </Box>
);

// Split component imports by feature/usage priority with intelligent loading

// Core dashboard components (high priority - load immediately after initial render)
export const [Dashboard, preloadDashboard] = createLazyComponent(
    () => import(/* webpackChunkName: "dashboard-core" */ './Dashboard'),
    {
        preloadDelay: 500,
        priority: 'high',
        chunkName: 'Dashboard',
        preloadCondition: () => {
            // Check if Network Information API is supported
            // @ts-ignore - Network Information API is not in standard TypeScript types
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            return connection && connection.effectiveType !== 'slow-2g';
        }
    },
);

export const [AutonomousDashboard, preloadAutonomous] = createLazyComponent(
    () => import(/* webpackChunkName: "dashboard-autonomous" */ './AutonomousDashboard'),
    {
        preloadDelay: 1000,
        priority: 'high',
        chunkName: 'AutonomousDashboard'
    },
);

export const [UltimateDashboard, preloadUltimate] = createLazyComponent(
    () => import(/* webpackChunkName: "dashboard-ultimate" */ './UltimateDashboard'),
    {
        preloadDelay: 1500,
        priority: 'normal',
        chunkName: 'UltimateDashboard'
    },
);

// Trading components (medium priority - load when user shows trading interest)
export const [TradingDashboard, preloadTrading] = createLazyComponent(
    () => import(/* webpackChunkName: "trading-dashboard" */ './TradingDashboard'),
    {
        preloadDelay: 2000,
        priority: 'normal',
        chunkName: 'TradingDashboard',
        preloadCondition: () => localStorage.getItem('user-trading-active') === 'true'
    },
);

export const [PositionManager, preloadPosition] = createLazyComponent(
    () => import(/* webpackChunkName: "trading-positions" */ './PositionManager'),
    {
        preloadDelay: 3000,
        priority: 'normal',
        chunkName: 'PositionManager'
    },
);

export const [TradeHistory, preloadTradeHistory] = createLazyComponent(
    () => import(/* webpackChunkName: "trading-history" */ './TradeHistory'),
    {
        preloadDelay: 3500,
        priority: 'normal',
        chunkName: 'TradeHistory'
    },
);

// Bot management components (medium priority)
export const [BotDashboard, preloadBotDashboard] = createLazyComponent(
    () => import(/* webpackChunkName: "bot-dashboard" */ './BotDashboard'),
    {
        preloadDelay: 2500,
        priority: 'normal',
        chunkName: 'BotDashboard'
    },
);

// Portfolio components (low priority - load when portfolio is accessed)
export const [PortfolioSummary, preloadPortfolio] = createLazyComponent(
    () => import(/* webpackChunkName: "portfolio-summary" */ './PortfolioSummary'),
    {
        preloadDelay: 4000,
        priority: 'low',
        chunkName: 'PortfolioSummary',
        preloadCondition: () => localStorage.getItem('user-has-portfolio') === 'true'
    },
);

// Authentication components (load on demand)
export const [Login, preloadLogin] = createLazyComponent(
    () => import(/* webpackChunkName: "auth-login" */ './Login'),
    {
        preloadDelay: 5000,
        priority: 'low',
        chunkName: 'Login'
    },
);

// Development/testing components (lowest priority)
export const [ErrorBoundaryTest, preloadErrorTest] = createLazyComponent(
    () => import(/* webpackChunkName: "dev-error-test" */ './ErrorBoundaryTest'),
    {
        preloadDelay: 8000,
        priority: 'low',
        chunkName: 'ErrorBoundaryTest',
        preloadCondition: () => process.env.NODE_ENV === 'development'
    },
);

// Advanced trading components (conditional loading based on user level)
export const [MarketAnalysis, preloadMarketAnalysis] = createLazyComponent(
    () => import(/* webpackChunkName: "analysis-market" */ './MarketAnalysis'),
    {
        preloadDelay: 6000,
        priority: 'low',
        retryAttempts: 2,
        chunkName: 'MarketAnalysis',
        preloadCondition: () => localStorage.getItem('user-level') === 'advanced'
    },
);

export const [ArbitrageOpportunityPanel, preloadArbitrage] = createLazyComponent(
    () => import(/* webpackChunkName: "analysis-arbitrage" */ './ArbitrageOpportunityPanel'),
    {
        preloadDelay: 7000,
        priority: 'low',
        chunkName: 'ArbitrageOpportunityPanel',
        preloadCondition: () => localStorage.getItem('user-level') === 'advanced'
    },
);

export const [WhaleTracker, preloadWhaleTracker] = createLazyComponent(
    () => import(/* webpackChunkName: "analysis-whale" */ './WhaleTracker'),
    {
        preloadDelay: 7500,
        priority: 'low',
        chunkName: 'WhaleTracker',
        preloadCondition: () => localStorage.getItem('user-level') === 'advanced'
    },
);

// Enhanced intelligent preloading strategy with performance optimization
export const preloadAllComponents = () => {
    // Check network conditions and device capabilities
    // @ts-ignore - connection APIs are not in standard TypeScript types
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
    const isSlowConnection = connection && (
        connection.effectiveType === 'slow-2g' ||
        connection.effectiveType === '2g' ||
        connection.saveData === true
    );

    // Check device memory and CPU capabilities
    // @ts-ignore - deviceMemory is not in standard TypeScript types
    const deviceMemory = navigator.deviceMemory || 4; // Default to 4GB if not available
    const isLowEndDevice = deviceMemory < 4;

    // Check if user prefers reduced motion (accessibility)
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    // Define preload groups by priority with enhanced categorization
    const preloadGroups = {
        critical: [preloadDashboard, preloadAutonomous], // Must load immediately
        important: [preloadTrading, preloadBotDashboard], // Load within 1-2 seconds
        normal: [preloadUltimate, preloadPosition, preloadTradeHistory], // Load within 3-5 seconds
        optional: [preloadPortfolio, preloadLogin], // Load when idle
        advanced: [preloadMarketAnalysis, preloadArbitrage, preloadWhaleTracker], // Load for advanced users
        development: [preloadErrorTest], // Development only
    };

    // Adaptive loading strategy based on device and network conditions
    const getLoadingStrategy = () => {
        if (isSlowConnection || isLowEndDevice) {
            return {
                critical: {delay: 0, interval: 2000},
                important: {delay: 5000, interval: 3000},
                normal: {delay: 15000, interval: 5000},
                optional: {delay: 30000, interval: 10000},
                advanced: {delay: 60000, interval: 15000},
                development: {delay: 120000, interval: 30000}
            };
        } else if (prefersReducedMotion) {
            return {
                critical: {delay: 0, interval: 500},
                important: {delay: 1000, interval: 1000},
                normal: {delay: 3000, interval: 1500},
                optional: {delay: 8000, interval: 2000},
                advanced: {delay: 15000, interval: 3000},
                development: {delay: 30000, interval: 5000}
            };
        } else {
            return {
                critical: {delay: 0, interval: 300},
                important: {delay: 500, interval: 500},
                normal: {delay: 1500, interval: 800},
                optional: {delay: 4000, interval: 1000},
                advanced: {delay: 8000, interval: 2000},
                development: {delay: 15000, interval: 3000}
            };
        }
    };

    const strategy = getLoadingStrategy();

    // Enhanced preloading with adaptive timing and error handling
    const schedulePreloading = (group, preloads, config) => {
        if (preloads.length === 0) return;

        setTimeout(() => {
            preloads.forEach((preload, index) => {
                const delay = index * config.interval;
                setTimeout(async () => {
                    try {
                        await preload();
                        if (process.env.NODE_ENV === 'development') {
                            logger.info(`✅ Preloaded ${group} component ${index + 1}/${preloads.length}`);
                        }
                    } catch (error) {
                        logger.warn(`⚠️ Failed to preload ${group} component:`, error);
                    }
                }, delay);
            });
        }, config.delay);
    };

    // Schedule preloading for each group
    schedulePreloading('critical', preloadGroups.critical, strategy.critical);
    schedulePreloading('important', preloadGroups.important, strategy.important);
    schedulePreloading('normal', preloadGroups.normal, strategy.normal);

    // Use requestIdleCallback for optional components when available
    const scheduleOptionalPreloading = () => {
        if ('requestIdleCallback' in window && !isSlowConnection) {
            requestIdleCallback(() => {
                schedulePreloading('optional', preloadGroups.optional, {delay: 0, interval: 1000});
            }, {timeout: 10000});
        } else {
            schedulePreloading('optional', preloadGroups.optional, strategy.optional);
        }
    };

    scheduleOptionalPreloading();

    // Load advanced components only if user is advanced
    if (localStorage.getItem('user-level') === 'advanced') {
        schedulePreloading('advanced', preloadGroups.advanced, strategy.advanced);
    }

    // Load development components only in development mode
    if (process.env.NODE_ENV === 'development') {
        schedulePreloading('development', preloadGroups.development, strategy.development);
    }

    // Performance monitoring
    if (process.env.NODE_ENV === 'development') {
        logger.info('🚀 Preloading strategy:', {
            networkType: connection?.effectiveType || 'unknown',
            deviceMemory: deviceMemory + 'GB',
            isSlowConnection,
            isLowEndDevice,
            prefersReducedMotion,
            strategy
        });
    }
};

// Smart preloading based on user interaction patterns
export const preloadBasedOnRoute = (currentRoute) => {
    const routePreloadMap = {
        '/dashboard': [preloadDashboard, preloadAutonomous],
        '/trading-dashboard': [preloadTrading, preloadPosition, preloadTradeHistory],
        '/autonomous-dashboard': [preloadAutonomous, preloadBotDashboard],
        '/bot-dashboard': [preloadBotDashboard, preloadTrading],
        '/portfolio-summary': [preloadPortfolio, preloadPosition],
        '/position-manager': [preloadPosition, preloadPortfolio],
        '/trade-history': [preloadTradeHistory, preloadTrading]
    };

    const preloadsForRoute = routePreloadMap[currentRoute] || [];
    preloadsForRoute.forEach((preload, index) => {
        setTimeout(preload, index * 200);
    });
};

// Preload components based on user hover/focus events
export const preloadOnInteraction = (componentName) => {
    const preloadMap = {
        'dashboard': preloadDashboard,
        'autonomous': preloadAutonomous,
        'trading': preloadTrading,
        'portfolio': preloadPortfolio,
        'positions': preloadPosition,
        'history': preloadTradeHistory,
        'bots': preloadBotDashboard,
        'analysis': preloadMarketAnalysis,
        'arbitrage': preloadArbitrage,
        'whale': preloadWhaleTracker
    };

    const preloadFunc = preloadMap[componentName];
    if (preloadFunc) {
        preloadFunc();
    }
};

// Export Suspense wrapper for consistent loading experience
export const withSuspense = (Component, fallback = <LoadingFallback/>) => {
    const WrappedComponent = (props) => (
        <Suspense fallback={fallback}>
            <Component {...props} />
        </Suspense>
    );
    WrappedComponent.displayName = `withSuspense(${Component.displayName || Component.name || 'Component'})`;
    return WrappedComponent;
};

// Additional non-critical components for enhanced lazy loading
// Note: These components would be created as needed for the application

// Placeholder for future components
const createPlaceholderComponent = (name) => createLazyComponent(
    () => Promise.resolve({
        default: () => React.createElement('div', null, `Component ${name} not implemented`)
    }),
    {
        preloadDelay: 10000,
        priority: 'low',
        chunkName: name
    },
);

export const [Settings, preloadSettings] = createPlaceholderComponent('Settings');
export const [HelpCenter, preloadHelpCenter] = createPlaceholderComponent('HelpCenter');
export const [NotificationCenter, preloadNotificationCenter] = createPlaceholderComponent('NotificationCenter');
export const [BacktestingPanel, preloadBacktesting] = createPlaceholderComponent('BacktestingPanel');

// Performance optimization utilities
export const LazyLoadingOptimizer = {
    // Track component usage patterns
    usagePatterns: new Map(),

    // Record component access
    recordAccess: (componentName) => {
        const current = LazyLoadingOptimizer.usagePatterns.get(componentName) || {count: 0, lastAccess: 0};
        LazyLoadingOptimizer.usagePatterns.set(componentName, {
            count: current.count + 1,
            lastAccess: Date.now()
        });
    },

    // Get preload priority based on usage
    getPreloadPriority: (componentName) => {
        const usage = LazyLoadingOptimizer.usagePatterns.get(componentName);
        if (!usage) return 'low';

        const daysSinceLastAccess = (Date.now() - usage.lastAccess) / (1000 * 60 * 60 * 24);

        if (usage.count > 10 && daysSinceLastAccess < 7) return 'high';
        if (usage.count > 5 && daysSinceLastAccess < 14) return 'normal';
        return 'low';
    },

    // Optimize preloading based on usage patterns
    optimizePreloading: () => {
        const components = [
            {name: 'Dashboard', preload: preloadDashboard},
            {name: 'AutonomousDashboard', preload: preloadAutonomous},
            {name: 'TradingDashboard', preload: preloadTrading},
            {name: 'PortfolioSummary', preload: preloadPortfolio},
            {name: 'MarketAnalysis', preload: preloadMarketAnalysis},
            {name: 'Settings', preload: preloadSettings},
            {name: 'NotificationCenter', preload: preloadNotificationCenter}];

        components.forEach(({name, preload}) => {
            const priority = LazyLoadingOptimizer.getPreloadPriority(name);
            const delay = priority === 'high' ? 500 : priority === 'normal' ? 2000 : 5000;

            setTimeout(preload, delay);
        });
    }
};

// Enhanced preloading with usage-based optimization
export const preloadBasedOnUsage = () => {
    // Load usage patterns from localStorage
    const storedPatterns = localStorage.getItem('component-usage-patterns');
    if (storedPatterns) {
        try {
            const patterns = JSON.parse(storedPatterns);
            Object.entries(patterns).forEach(([component, data]) => {
                LazyLoadingOptimizer.usagePatterns.set(component, data);
            });
        } catch (error) {
            logger.warn('Failed to load usage patterns:', error);
        }
    }

    // Optimize preloading based on usage
    LazyLoadingOptimizer.optimizePreloading();

    // Save usage patterns periodically
    setInterval(() => {
        const patterns = Object.fromEntries(LazyLoadingOptimizer.usagePatterns);
        localStorage.setItem('component-usage-patterns', JSON.stringify(patterns));
    }, 60000); // Save every minute
};

// Export loading components and utilities
export {LoadingFallback};
