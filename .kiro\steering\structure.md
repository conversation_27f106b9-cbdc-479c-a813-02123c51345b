# Project Structure & Organization

## Repository Layout

```
electrontrader-monorepo/
├── package.json                 # Root workspace configuration
├── ipc-validation-test.js      # Root-level IPC validation test
├── test-ipc-simple.js          # Root-level simple IPC test
├── test-ipc-handlers.js        # Root-level IPC handlers test
├── app/                        # Main Electron application
│   ├── run-autonomous.js      # Autonomous runner script
│   ├── package.json           # App dependencies and scripts
│   ├── main.js                # Electron main process
│   ├── preload.js             # Electron preload script
│   ├── src/                   # React frontend source
│   ├── trading/               # Trading engine (separate workspace)
│   ├── public/                # Static assets
│   │   └── electron.js        # Public electron file
│   ├── build/                 # Production build output
│   ├── scripts/               # Build and utility scripts
│   │   └── performance-test.js
│   ├── webpack.config.js      # Main webpack configuration
│   ├── webpack.config.performance.js # Performance webpack config
│   ├── electron-builder.config.js # Electron builder config
│   ├── jest.config.js         # Jest test configuration
│   ├── test-ipc.js           # IPC test file
│   ├── test-ipc.html         # IPC test HTML
│   ├── test-ipc-simple.js    # Simple IPC test
│   ├── test-ipc-handlers.js  # IPC handlers test
│   ├── test-ipc-integration.js # IPC integration test
│   ├── ipc-validation-test.js # IPC validation test
│   └── __tests__/             # Frontend tests
└── .kiro/                     # Kiro AI assistant configuration
    ├── specs/                 # Feature specifications
    │   ├── application-integration/
    │   └── backend-cleanup/
    └── steering/              # AI steering documents
        ├── product.md
        ├── tech.md
        └── structure.md
```

## Frontend Structure (`app/src/`)

```
src/
├── components/                # React components
│   ├── AdvancedChart.jsx     # Advanced charting component
│   ├── ApplicationErrorBoundary.jsx # App-level error boundary
│   ├── ArbitrageOpportunityPanel.jsx # Arbitrage opportunities
│   ├── AutonomousDashboard.jsx # AI trading dashboard
│   ├── BotCard.jsx          # Bot card component
│   ├── BotDashboard.jsx      # Bot management
│   ├── CoinManager.jsx      # Coin management
│   ├── ConnectionStatus.jsx  # Connection status
│   ├── CriticalWorkflowErrorBoundary.jsx # Critical error boundary
│   ├── CrossExchangePortfolio.jsx # Cross-exchange portfolio
│   ├── Dashboard.jsx         # Main dashboard
│   ├── DexScreenerChart.jsx  # DEX screener chart
│   ├── EnhancedErrorBoundary.jsx # Enhanced error boundary
│   ├── EnhancedStartButton.jsx # Enhanced start button
│   ├── ErrorBoundary.jsx     # Error handling
│   ├── ErrorBoundaryTest.jsx # Error boundary testing
│   ├── FallbackComponents.jsx # Fallback UI components
│   ├── FuturisticButton.jsx  # Futuristic button component
│   ├── GridTradingPanel.jsx  # Grid trading panel
│   ├── HolographicCard.jsx   # Holographic card component
│   ├── IPCExample.jsx        # IPC usage examples
│   ├── IPCExampleClean.jsx   # Clean IPC examples
│   ├── LazyComponents.jsx    # Code splitting utilities
│   ├── LiveSystemMonitor.jsx # Real-time monitoring
│   ├── Login.js             # Login component
│   ├── MarketAnalysis.jsx    # Market analysis
│   ├── OrderForm.js         # Order form
│   ├── ParticleBackground.jsx # Particle background
│   ├── PortfolioSummary.js   # Portfolio management
│   ├── PortfolioTracker.jsx  # Portfolio tracking
│   ├── PositionManager.js    # Position management
│   ├── RunningOperationsMonitor.jsx # Operations monitor
│   ├── SettingsModal.jsx     # Settings modal
│   ├── Sidebar.jsx          # Navigation sidebar
│   ├── StartButton.jsx       # Trading start button
│   ├── StartupProgressPanel.jsx # Startup progress
│   ├── SystemDiagnostics.jsx # System diagnostics
│   ├── SystemStatusPanel.jsx # System status display
│   ├── TradeHistory.js       # Trade history view
│   ├── TradingDashboard.js   # Trading interface
│   ├── TradingStatusIndicator.jsx # Trading status
│   ├── UltimateDashboard.jsx # Ultimate dashboard view
│   ├── VibrantButton.jsx     # Vibrant button component
│   ├── WhaleTracker.jsx      # Whale tracking component
│   └── __tests__/           # Component tests
│       ├── Dashboard.test.jsx
│       ├── RealTimeStatusIntegration.test.js
│       └── StartButton.test.jsx
├── hooks/                    # Custom React hooks
│   ├── useIPC.js            # Electron IPC communication
│   └── useParticleAnimation.js
├── services/                 # Business logic services
│   ├── ipcService.js        # IPC abstraction layer
│   ├── realTimeStatusService.js # Real-time status
│   ├── startupService.js    # Startup management
│   └── ErrorReporter.js     # Error reporting
├── utils/                    # Utility functions
│   ├── PerformanceMonitor.js # Performance monitoring
│   ├── GlobalErrorHandler.js # Global error handling
│   ├── StartupOptimizer.js  # Startup optimization
│   ├── BuildOptimizationMonitor.js # Build optimization
│   ├── validateRealTimeStatus.js # Status validation
│   ├── formatters.js        # Data formatters
│   ├── AnimationOptimizer.js # Animation optimization
│   ├── ComponentRecoveryManager.js # Component recovery
│   └── ElectronAPITester.js # Electron API testing
├── api/                      # API integration
│   ├── trading.js           # Trading API
│   ├── backend.js           # Backend API
│   └── server.js            # Server API
├── auth/                     # Authentication context
│   └── AuthContext.js       # Auth provider
├── config/                   # Configuration
│   └── environment.js       # Environment config
├── constants/                # Constants
│   ├── ui-constants.js      # UI constants
│   └── exchangeColors.js    # Exchange color mapping
├── types/                    # TypeScript definitions
│   ├── electron-api.d.ts    # Electron API types
│   └── electron.d.ts        # Electron types
├── docs/                     # Documentation
│   └── IPC_USAGE.md         # IPC usage guide
├── styles/                   # Styling
│   └── animations.css       # Animation styles
├── theme/                    # Theme configuration
│   └── theme.js             # MUI theme
├── dev/                      # Development tools
│   ├── palette.jsx          # Component palette
│   └── previews.jsx         # Component previews
└── __tests__/               # Test suites
    ├── components/          # Component tests
    │   └── Dashboard.test.jsx
    ├── e2e/                # End-to-end tests
    │   ├── complete-application-workflow.test.js
    │   └── simple-workflow.test.js
    ├── ipc/                # IPC tests
    │   ├── ipc-communication-test.js
    │   ├── ipc-enhanced-communication-test.js
    │   ├── ipc-integration.test.js
    │   ├── ipc-integration-test.js
    │   ├── ipc-protocol-validation.js
    │   ├── ipc-test-runner.js
    │   └── README.md
    ├── integration/        # Integration tests
    ├── utils/              # Utility tests
    ├── error-handling/     # Error handling tests
    ├── __mocks__/          # Test mocks
    │   └── framer-motion.js
    ├── jest-dom.d.ts       # Jest DOM types
    ├── jest-globals.d.ts   # Jest globals types
    ├── jest.d.ts           # Jest types
    └── setupTests.js       # Test setup
```

## Trading Engine Structure (`app/trading/`)

```
trading/
├── engines/                  # Core trading components
│   ├── trading/             # Trading execution
│   │   ├── orchestration/   # TradingOrchestrator (main coordinator)
│   │   │   ├── TradingOrchestrator.js
│   │   │   └── enhanced-component-initializer.js
│   │   ├── PortfolioManager.js
│   │   ├── TradingExecutor.js
│   │   ├── ProductionTradingExecutor.js
│   │   ├── MemeCoinScanner.js
│   │   └── whaletrader/     # Whale tracking
│   │       └── Elite.WhaleTracker.js
│   ├── data-collection/     # Market data
│   │   ├── DataCollector.js
│   │   └── backtesting.js
│   ├── exchange/            # Exchange connectors
│   │   ├── ConnectionPool.js
│   │   └── ProductionExchangeConnector.js
│   ├── ccxt/               # CCXT integration
│   │   └── engines/
│   │       ├── CCXT-Connector.js
│   │       └── CCXT-Exchange-Manager.js
│   ├── shared/             # Shared utilities
│   │   ├── security/       # Security & risk management
│   │   │   ├── health-monitor.js
│   │   │   ├── error-handling/
│   │   │   │   ├── ErrorHandler.js
│   │   │   │   └── index.js
│   │   │   ├── safety/
│   │   │   │   └── circuit-breakers.js
│   │   │   ├── risk/
│   │   │   │   └── UnifiedRiskManager.js
│   │   │   └── recovery/
│   │   │       ├── EnhancedRecoveryManager.js
│   │   │       └── PositionRecoveryManager.js
│   │   └── utils/          # Common utilities
│   │       ├── ErrorHandlingUtils.js
│   │       ├── ErrorBoundary.js
│   │       └── ValidationUtils.js
│   ├── config/             # Configuration engines
│   │   ├── configuration-loader.js
│   │   └── EnhancedConfigManager.js
│   ├── logging/            # Logging system
│   │   ├── logger.js
│   │   └── EnhancedLogger.js
│   ├── context/            # Context management
│   │   ├── ContextEngine.js
│   │   └── index.js
│   └── optimization/       # Performance optimization
│       └── performance-monitor.js
├── ai/                      # AI/ML components
│   ├── AutonomousTrader.js # Main AI trader
│   ├── AutonomousTrader.test.js
│   ├── CryptoDiscoveryEngine.js
│   ├── StrategyOptimizer.js
│   ├── cli.js              # AI CLI interface
│   └── llm-coordinator.js  # LLM coordination
├── analysis/               # Market analysis
│   ├── SentimentAnalyzer.js
│   ├── PerformanceTracker.js
│   ├── PerformanceTracker.old.js # Legacy performance tracker
│   ├── MemeCoinAnalyzer.js
│   ├── TechnicalAnalyzer.js
│   └── RuleBasedSentiment.js
├── config/                 # Configuration management
│   ├── index.js            # Main config manager
│   ├── enhanced-config-manager.js
│   ├── database-config.js
│   ├── startup-config-loader.js
│   ├── error-handling.config.js
│   ├── config-cli.js       # CLI configuration tool
│   ├── config-test-utils.js
│   ├── monitoring.json     # Monitoring configuration
│   ├── security.json       # Security configuration
│   ├── risk-management.json # Risk management config
│   ├── schemas/            # Configuration schemas
│   │   └── config-schemas.json
│   ├── migrations/         # Config migrations
│   │   └── config-migrator.js
│   ├── exchanges/          # Exchange configurations
│   ├── strategies/         # Trading strategies
│   ├── package.json        # Config module dependencies
│   ├── README.md           # Config documentation
│   └── *.json             # Environment configs
├── data/                   # Database layer
│   ├── DatabaseManager.js
│   ├── UnifiedDatabaseManager.js
│   ├── UnifiedDatabaseInitializer.js
│   ├── transaction-manager.js
│   ├── databases/          # Database files
│   ├── analytics/          # Analytics data
│   └── cache/              # Data caching
├── monitoring/             # Health monitoring
│   ├── health-check.js
│   ├── enhanced-health-monitor.js
│   ├── health-dashboard.js
│   ├── health-cli.js
│   ├── health-monitoring-integration.js
│   ├── EnhancedLogger.js
│   ├── ErrorReporter.js
│   ├── status-reporter.js
│   ├── metrics-server.js
│   ├── package.json        # Monitoring dependencies
│   └── README.md
├── api/                    # API endpoints
│   └── health-endpoints.js
├── scripts/                # Utility scripts
│   └── performance-optimizer.js
├── docs/                   # Documentation
│   ├── STARTUP_WORKFLOW_IMPLEMENTATION.md
│   ├── COMPREHENSIVE_ERROR_HANDLING.md
│   ├── DATABASE_INITIALIZATION.md
│   ├── DATABASE_SETUP.md
│   └── COMPONENT_INTEGRATION_SUMMARY.md
├── tests/                  # Legacy test files
│   ├── test-trading-system.js
│   ├── test-database-initialization.js
│   ├── test-database-connections.js
│   └── test-refactored-structure.js
├── __tests__/             # Jest test suites
│   ├── unit/              # Unit tests
│   │   ├── trading-orchestrator.test.js
│   │   ├── simple-ipc-communication.test.js
│   │   ├── database-operations.test.js
│   │   ├── ipc-communication.test.js
│   │   └── test-runner.js
│   ├── integration/       # Integration tests
│   │   ├── start-button-workflow.test.js
│   │   └── error-handling-integration.test.js
│   ├── e2e/               # End-to-end tests
│   │   └── application-workflow.test.js
│   ├── comprehensive-error-handling.test.js
│   └── test-backend.js
├── logs/                   # Log files
│   ├── trading.log
│   ├── errors.log
│   ├── cleanup-report.json
│   └── errors/            # Error logs
├── migrations/             # Database migrations
│   ├── 001_initial_trading_schema.sql
│   ├── 002_n8n_workflow_schema.sql
│   └── 003_credentials_security_schema.sql
├── mocks/                  # Test mocks
│   └── database-manager.mock.js
├── examples/               # Code examples
│   ├── ccxt-decimal-example.js
│   └── precision-trading-example.js
├── .keys/                  # Security keys
├── .claude/                # Claude AI settings
├── dependencies.js         # Dependency management
├── package.json            # Trading engine dependencies
├── .env                    # Environment variables
├── .env.example           # Environment template
├── README.md
├── MYSQL_SETUP.md         # MySQL setup guide
├── workflow-config.json   # Workflow configuration
├── index.js               # Main entry point
├── launchTrading.js       # Legacy launch script
├── start-trading-system.js # Main startup script
├── start-autonomous-trading.js # Autonomous mode startup
├── autonomous-startup.js  # Autonomous startup
├── autonomous-trader.js   # Autonomous trader entry
├── autonomous-trader-service.js # Autonomous service
├── startup.js             # General startup
├── start-autonomous-mock.js # Mock autonomous start
├── automatic-failure-recovery.js # Failure recovery
├── check-database-schema.js # Schema validation
├── .eslintignore          # ESLint ignore rules
└── .eslintrc.cjs          # ESLint configuration
```

## Key Architecture Patterns

### Component Organization

- **Feature-based grouping**: Components grouped by trading functionality
- **Lazy loading**: Performance optimization with React.lazy()
- **Error boundaries**: Isolated error handling per feature area
- **Shared utilities**: Common functionality in utils/ directories

### IPC Communication Pattern

- **Main Process** (`main.js`): Electron main process with IPC handlers
- **Preload Script** (`preload.js`): Secure API exposure to renderer
- **Frontend Services** (`services/ipcService.js`): IPC abstraction layer
- **Trading Engine**: Backend business logic with IPC integration

### Database Architecture

- **Unified Manager**: Single interface for SQLite/MySQL
- **Migration System**: Schema versioning in migrations/
- **Transaction Management**: ACID compliance for trading operations

### Configuration Management

- **Environment-based**: Development, staging, production configs
- **Modular Structure**: Separate configs for trading, security, monitoring
- **Schema Validation**: JSON schema validation for configuration

### Testing Structure

- **Unit Tests**: Component and function-level testing
- **Integration Tests**: Cross-component interaction testing
- **E2E Tests**: Full application workflow testing
- **IPC Tests**: Communication layer validation

### Build Artifacts

- **Development**: Hot-reloaded React + Electron dev environment
- **Production**: Optimized bundles with code splitting
- **Distribution**: Platform-specific installers (Windows, Mac, Linux)

## File Naming Conventions

- **React Components**: PascalCase with .jsx extension
- **Utilities/Services**: camelCase with .js extension
- **Configuration**: kebab-case with .json/.js extension
- **Tests**: Same name as source file with .test.js suffix
- **Documentation**: UPPERCASE with .md extension