 
'use strict';

Object.defineProperty(exports, '__esModule', {
    value: true
});
exports.default = void 0;
const _react = _interopRequireWildcard(require('react'));
const _propTypes = _interopRequireDefault(require('prop-types'));
const _logger = _interopRequireDefault(require('../utils/logger'));
const _material = require('@mui/material');
const _iconsMaterial = require('@mui/icons-material');
const _framerMotion = require('framer-motion');
const _HolographicCard = _interopRequireDefault(require('./HolographicCard'));
const _FuturisticButton = _interopRequireDefault(require('./FuturisticButton'));
const _exchangeColors = require('../constants/exchangeColors');

function _interopRequireDefault(e) {
    return e && e.__esModule ? e : {
        default: e
    };
}

function _interopRequireWildcard(e, t) {
    if ('function' == typeof WeakMap) var r = new WeakMap(),
        n = new WeakMap();
    return (_interopRequireWildcard = function (e, t) {
        if (!t && e && e.__esModule) return e;
        let o,
            i,
            f = {
                __proto__: null,
                default: e
            };
        if (null === e || 'object' != typeof e && 'function' != typeof e) return f;
        if (o = t ? n : r) {
            if (o.has(e)) return o.get(e);
            o.set(e, f);
        }
        for (const t in e) 'default' !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]);
        return f;
    })(e, t);
}

function ownKeys(e, r) {
    const t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        let o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function (r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}

function _objectSpread(e) {
    for (let r = 1; r < arguments.length; r++) {
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}

function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: !0,
        configurable: !0,
        writable: !0
    }) : e[r] = t, e;
}

function _toPropertyKey(t) {
    const i = _toPrimitive(t, 'string');
    return 'symbol' == typeof i ? i : i + '';
}

function _toPrimitive(t, r) {
    if ('object' != typeof t || !t) return t;
    const e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        const i = e.call(t, r || 'default');
        if ('object' != typeof i) return i;
        throw new TypeError('@@toPrimitive must return a primitive value.');
    }
    return ('string' === r ? String : Number)(t);
} // Import logger for consistent logging
// Color codes for opportunity profit levels
const OPPORTUNITY_COLORS = {
    high: '#4caf50',
    // Green for high profit
    good: '#2196f3',
    // Blue for good profit
    moderate: '#ff9800',
    // Orange for moderate profit
    low: '#f44336', // Red for low profit
};

/**
 * @typedef {Object} ElectronAPI
 * @property {() => Promise<{success: boolean, data?: any, error?: string}>} [getArbitrageOpportunities]
 * @property {() => Promise<{success: boolean, data?: any, error?: string}>} [getArbitragePositions]
 * @property {() => Promise<{success: boolean, data?: any, error?: string}>} [getArbitrageStats]
 * @property {() => Promise<{success: boolean, isActive?: boolean, error?: string}>} [getArbitrageStatus]
 * @property {() => Promise<{success: boolean, error?: string}>} [startArbitrageEngine]
 * @property {() => Promise<{success: boolean, error?: string}>} [stopArbitrageEngine]
 * @property {(opportunity: any) => Promise<{success: boolean, error?: string}>} [executeArbitrage]
 * @property {(config: any) => Promise<{success: boolean, error?: string}>} [updateArbitrageConfig]
 * @property {(callback: Function) => Function} [onArbitrageOpportunity]
 * @property {(callback: Function) => Function} [onArbitrageExecuted]
 */

/**
 * @type {Window & { electronAPI?: ElectronAPI }}
 */
const customWindow = window;
const safeElectronAPI = customWindow.electronAPI || {
    getArbitrageOpportunities: () => Promise.resolve({
        success: true,
        data: []
    }),
    getArbitragePositions: () => Promise.resolve({
        success: true,
        data: []
    }),
    getArbitrageStats: () => Promise.resolve({
        success: true,
        data: {
            totalOpportunities: 0,
            successfulTrades: 0,
            totalProfit: 0,
            successRate: 0,
            avgLatency: 0
        }
    }),
    getArbitrageStatus: () => Promise.resolve({
        success: true,
        isActive: false
    }),
    startArbitrageEngine: () => Promise.resolve({
        success: true
    }),
    stopArbitrageEngine: () => Promise.resolve({
        success: true
    }),
    executeArbitrage: () => Promise.resolve({
        success: true
    }),
    updateArbitrageConfig: () => Promise.resolve({
        success: true
    }),
    onArbitrageOpportunity: () => () => {
    },
    onArbitrageExecuted: () => () => {
    }
};
const MotionTableRow = (0, _framerMotion.motion)(_material.TableRow);
const ArbitrageOpportunityPanel = ({
                                       showNotification
                                   }) => {
    const [isActive, setIsActive] = (0, _react.useState)(false);
    const [isLoading, setIsLoading] = (0, _react.useState)(false);
    const [opportunities, setOpportunities] = (0, _react.useState)([]);
    const [activePositions, setActivePositions] = (0, _react.useState)([]);
    const [stats, setStats] = (0, _react.useState)({
        totalOpportunities: 0,
        successfulTrades: 0,
        totalProfit: 0,
        successRate: 0,
        avgLatency: 0
    });
    const [expandedOpportunity, setExpandedOpportunity] = (0, _react.useState)(null);
    const [selectedFilter, setSelectedFilter] = (0, _react.useState)('all');
    const [autoExecuteEnabled, setAutoExecuteEnabled] = (0, _react.useState)(false);
    const [settingsOpen, setSettingsOpen] = (0, _react.useState)(false);
    const [executionDialogOpen, setExecutionDialogOpen] = (0, _react.useState)(false);
    const [selectedOpportunity, setSelectedOpportunity] = (0, _react.useState)(null);
    const [config, setConfig] = (0, _react.useState)({
        minProfitThreshold: 0.5,
        maxPositionSize: 1000,
        maxSlippage: 0.3,
        autoExecuteThreshold: 1.0,
        maxConcurrentTrades: 5,
        enabledExchanges: ['binance', 'bybit', 'okx', 'kucoin']
    });
    const [draftConfig, setDraftConfig] = (0, _react.useState)(config);
    const handleNewOpportunity = (0, _react.useCallback)(opportunity => {
        setOpportunities(prev => {
            const updated = prev.filter(opp => opp.id !== opportunity.id);
            return [opportunity, ...updated].slice(0, 50); // Keep top 50
        });
    }, []);
    const fetchArbitrageData = (0, _react.useCallback)(async () => {
        try {
            setIsLoading(true);
            const [opportunitiesRes, positionsRes, statsRes, statusRes] = await Promise.all([safeElectronAPI.getArbitrageOpportunities(), safeElectronAPI.getArbitragePositions(), safeElectronAPI.getArbitrageStats(), safeElectronAPI.getArbitrageStatus()]);
            if (opportunitiesRes !== null && opportunitiesRes !== void 0 && opportunitiesRes.success) {
                setOpportunities(opportunitiesRes.data || []);
            }
            if (positionsRes !== null && positionsRes !== void 0 && positionsRes.success) {
                setActivePositions(positionsRes.data || []);
            }
            if (statsRes !== null && statsRes !== void 0 && statsRes.success) {
                setStats(() => statsRes.data || {
                    totalOpportunities: 0,
                    successfulTrades: 0,
                    totalProfit: 0,
                    successRate: 0,
                    avgLatency: 0
                });
            }
            if (statusRes !== null && statusRes !== void 0 && statusRes.success) {
                setIsActive(statusRes.isActive || false);
            }
        } catch (error) {
            _logger.default.error('Failed to fetch arbitrage data:', error);
            showNotification('Failed to fetch arbitrage data', 'error');
        } finally {
            setIsLoading(false);
        }
    }, [showNotification]);
    const handleArbitrageExecuted = (0, _react.useCallback)(result => {
        let _result$profit;
        showNotification(`Arbitrage executed: ${/** @type any */(_result$profit = result.profit) === null || _result$profit === void 0 ? void 0 : _result$profit.toFixed(2)} USDT profit`, /** @type any */result.profit > 0 ? 'success' : 'error');
        fetchArbitrageData(); // Refresh data
    }, [showNotification, fetchArbitrageData]);
    (0, _react.useEffect)(() => {
        fetchArbitrageData();
        const cleanupOpportunity = safeElectronAPI.onArbitrageOpportunity(handleNewOpportunity);
        const cleanupExecuted = safeElectronAPI.onArbitrageExecuted(handleArbitrageExecuted);
        if (settingsOpen) {
            setDraftConfig(config);
        }
        return () => {
            if (typeof cleanupOpportunity === 'function') {
                cleanupOpportunity();
            }
            if (typeof cleanupExecuted === 'function') {
                cleanupExecuted();
            }
        };
    }, [fetchArbitrageData, settingsOpen, config, handleNewOpportunity, handleArbitrageExecuted]);
    const handleToggleEngine = async () => {
        try {
            setIsLoading(true);
            const result = isActive ? await safeElectronAPI.stopArbitrageEngine() : await safeElectronAPI.startArbitrageEngine();
            if (result !== null && result !== void 0 && result.success) {
                setIsActive(!isActive);
                showNotification(`Arbitrage engine ${isActive ? 'stopped' : 'started'}`, 'success');
            } else {
                showNotification(/** @type {{error?: string}} */(result === null || result === void 0 ? void 0 : result.error) || 'Operation failed', 'error');
            }
        } catch (error) {
            _logger.default.error('Failed to toggle arbitrage engine:', error);
            showNotification('Failed to toggle arbitrage engine', 'error');
        } finally {
            setIsLoading(false);
        }
    };
    const handleExecuteOpportunity = async opportunity => {
        try {
            setIsLoading(true);
            const result = await safeElectronAPI.executeArbitrage(opportunity);
            if (result !== null && result !== void 0 && result.success) {
                showNotification(`Arbitrage execution initiated for ${opportunity.symbol}`, 'success');
                setExecutionDialogOpen(false);
                fetchArbitrageData(); // Refresh data
            } else {
                showNotification(/** @type {{error?: string}} */(result === null || result === void 0 ? void 0 : result.error) || 'Execution failed', 'error');
            }
        } catch (error) {
            _logger.default.error('Failed to execute arbitrage:', error);
            showNotification('Failed to execute arbitrage', 'error');
        } finally {
            setIsLoading(false);
        }
    };
    const handleConfigUpdate = async newConfig => {
        try {
            const result = await safeElectronAPI.updateArbitrageConfig(newConfig);
            if (result !== null && result !== void 0 && result.success) {
                setConfig(newConfig);
                setDraftConfig(newConfig);
                showNotification('Configuration updated', 'success');
                setSettingsOpen(false);
            } else {
                showNotification(/** @type {{error?: string}} */(result === null || result === void 0 ? void 0 : result.error) || 'Failed to update configuration', 'error');
            }
        } catch (error) {
            _logger.default.error('Failed to update configuration:', error);
            showNotification('Failed to update configuration', 'error');
        }
    };
    const getOpportunityColor = profitPercent => {
        if (profitPercent >= 2.0) return OPPORTUNITY_COLORS.high;
        if (profitPercent >= 1.0) return OPPORTUNITY_COLORS.good;
        if (profitPercent >= 0.5) return OPPORTUNITY_COLORS.moderate;
        return OPPORTUNITY_COLORS.low;
    };
    const formatCurrency = value => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(value);
    };
    const formatPercentage = value => {
        if (value === null || value === undefined || isNaN(value)) {
            return '0.000%';
        }
        return `${Number(value).toFixed(3)}%`;
    };
    const filteredOpportunities = opportunities.filter(opp => {
        switch (selectedFilter) {
            case 'high-profit':
                return opp.profitPercent >= 1.0;
            case 'executable':
                return opp.profitPercent >= config.minProfitThreshold;
            case 'btc':
                return opp.symbol.includes('BTC');
            case 'eth':
                return opp.symbol.includes('ETH');
            default:
                return true;
        }
    });
    return /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            p: 3,
            maxWidth: '100%',
            overflow: 'hidden'
        }
    }, /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        initial: {
            opacity: 0,
            y: -20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.6
        }
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'quantum',
        elevation: 'high',
        glowColor: '#00eaff',
        className: 'energy-field',
        sx: {
            mb: 3,
            p: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3,
        alignItems: 'center'
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h4',
        sx: {
            color: '#00eaff',
            fontWeight: 900,
            display: 'flex',
            alignItems: 'center',
            gap: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        animate: {
            rotate: 360,
            scale: [1, 1.1, 1]
        },
        transition: {
            duration: 3,
            repeat: Infinity
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.CompareArrows, {
        sx: {
            fontSize: '2rem'
        }
    })), '\u26A1 QUANTUM ARBITRAGE MATRIX'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'subtitle1',
        sx: {
            color: '#888',
            mt: 1
        }
    }, 'Cross-exchange arbitrage opportunities with AI-enhanced profit optimization')), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        md: 6
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            gap: 2,
            justifyContent: 'flex-end',
            flexWrap: 'wrap'
        }
    }, /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        whileHover: {
            scale: 1.05
        },
        whileTap: {
            scale: 0.95
        }
    }, /*#__PURE__*/_react.default.createElement(_FuturisticButton.default, {
        onClick: handleToggleEngine,
        disabled: isLoading,
        variant: isActive ? 'error' : 'success',
        startIcon: isActive ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.Stop, null) : /*#__PURE__*/_react.default.createElement(_iconsMaterial.PlayArrow, null),
        sx: {
            minWidth: '160px'
        }
    }, isActive ? '🛑 STOP ENGINE' : '🚀 START ENGINE')), /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        whileHover: {
            scale: 1.05
        },
        whileTap: {
            scale: 0.95
        }
    }, /*#__PURE__*/_react.default.createElement(_FuturisticButton.default, {
        onClick: () => setSettingsOpen(true),
        variant: 'ghost',
        startIcon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Settings, null)
    }, '\u2699\uFE0F CONFIG')), /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        whileHover: {
            scale: 1.05
        },
        whileTap: {
            scale: 0.95
        }
    }, /*#__PURE__*/_react.default.createElement(_FuturisticButton.default, {
        onClick: fetchArbitrageData,
        disabled: isLoading,
        variant: 'ghost',
        startIcon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Refresh, null)
    }, '\uFFFD\uFFFD REFRESH'))))))), /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.8,
            delay: 0.2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3,
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'success',
        elevation: 'medium',
        sx: {
            p: 2,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h3',
        sx: {
            color: '#4caf50',
            fontWeight: 900
        }
    }, stats.totalOpportunities || 0), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, '\uD83D\uDCCA Total Opportunities'))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'premium',
        elevation: 'medium',
        sx: {
            p: 2,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h3',
        sx: {
            color: '#a259ff',
            fontWeight: 900
        }
    }, formatPercentage(stats.successRate)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, '\uD83C\uDFAF Success Rate'))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'quantum',
        elevation: 'medium',
        sx: {
            p: 2,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h3',
        sx: {
            color: '#00eaff',
            fontWeight: 900
        }
    }, formatCurrency(stats.totalProfit)), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, '\uD83D\uDCB0 Total Profit'))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6,
        md: 3
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'warning',
        elevation: 'medium',
        sx: {
            p: 2,
            textAlign: 'center'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h3',
        sx: {
            color: '#ffc107',
            fontWeight: 900
        }
    }, stats.avgLatency || 0, 'ms'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, '\u26A1 Avg Latency'))))), /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        initial: {
            opacity: 0
        },
        animate: {
            opacity: 1
        },
        transition: {
            duration: 0.8,
            delay: 0.4
        }
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'glass',
        sx: {
            mb: 3,
            p: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 2,
        alignItems: 'center'
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 4
    }, /*#__PURE__*/_react.default.createElement(_material.FormControl, {
        fullWidth: true,
        size: 'small'
    }, /*#__PURE__*/_react.default.createElement(_material.InputLabel, {
        sx: {
            color: '#888'
        }
    }, 'Filter Opportunities'), /*#__PURE__*/_react.default.createElement(_material.Select, {
        value: selectedFilter,
        onChange: e => setSelectedFilter(e.target.value),
        sx: {
            color: '#fff'
        }
    }, /*#__PURE__*/_react.default.createElement(_material.MenuItem, {
        value: 'all'
    }, '\uD83C\uDF1F All Opportunities'), /*#__PURE__*/_react.default.createElement(_material.MenuItem, {
        value: 'high-profit'
    }, '\uD83D\uDC8E High Profit (\u22651%)'), /*#__PURE__*/_react.default.createElement(_material.MenuItem, {
        value: 'executable'
    }, '\u26A1 Executable'), /*#__PURE__*/_react.default.createElement(_material.MenuItem, {
        value: 'btc'
    }, '\u20BF Bitcoin Pairs'), /*#__PURE__*/_react.default.createElement(_material.MenuItem, {
        value: 'eth'
    }, '\u039E Ethereum Pairs')))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 4
    }, /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            display: 'flex',
            alignItems: 'center',
            gap: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, '\uD83E\uDD16 Auto Execute:'), /*#__PURE__*/_react.default.createElement(_material.Switch, {
        checked: autoExecuteEnabled,
        onChange: e => setAutoExecuteEnabled(e.target.checked),
        color: 'primary'
    }), /*#__PURE__*/_react.default.createElement(_material.Tooltip, {
        title: 'Automatically execute profitable opportunities'
    }, /*#__PURE__*/_react.default.createElement(_material.IconButton, {
        size: 'small'
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Analytics, {
        sx: {
            color: '#888',
            fontSize: '1rem'
        }
    }))))), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 4
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, '\uD83D\uDCC8 Active Opportunities: ', filteredOpportunities.length, ' | \uD83D\uDD04 Positions: ', activePositions.length))))), /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.8,
            delay: 0.6
        }
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'quantum',
        elevation: 'high',
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#00eaff',
            p: 2,
            borderBottom: '1px solid rgba(0,234,255,0.2)',
            fontWeight: 800,
            display: 'flex',
            alignItems: 'center',
            gap: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.ShowChart, null), '\uD83C\uDFAF LIVE ARBITRAGE OPPORTUNITIES', /*#__PURE__*/_react.default.createElement(_material.Chip, {
        label: `${filteredOpportunities.length} Active`,
        size: 'small',
        sx: {
            backgroundColor: 'rgba(0,234,255,0.2)',
            color: '#00eaff',
            fontWeight: 600
        }
    })), /*#__PURE__*/_react.default.createElement(_material.TableContainer, {
        sx: {
            maxHeight: 600
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Table, {
        stickyHeader: true
    }, /*#__PURE__*/_react.default.createElement(_material.TableHead, null, /*#__PURE__*/_react.default.createElement(_material.TableRow, null, /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#888',
            fontWeight: 700
        }
    }, 'Symbol'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#888',
            fontWeight: 700
        }
    }, 'Exchanges'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#888',
            fontWeight: 700
        }
    }, 'Profit %'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#888',
            fontWeight: 700
        }
    }, 'Profit USDT'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#888',
            fontWeight: 700
        }
    }, 'Volume'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#888',
            fontWeight: 700
        }
    }, 'Age'), /*#__PURE__*/_react.default.createElement(_material.TableCell, {
        sx: {
            color: '#888',
            fontWeight: 700
        }
    }, 'Actions'))), /*#__PURE__*/_react.default.createElement(_material.TableBody, null, /*#__PURE__*/_react.default.createElement(_framerMotion.AnimatePresence, null, filteredOpportunities.map((opportunity, index) => {
        let _opportunity$buyExcha, _opportunity$sellExch, _opportunity$volume;
        return /*#__PURE__*/_react.default.createElement(MotionTableRow, {
            key: opportunity.id,
            initial: {
                opacity: 0,
                x: -20
            },
            animate: {
                opacity: 1,
                x: 0
            },
            exit: {
                opacity: 0,
                x: 20
            },
            transition: {
                duration: 0.3,
                delay: index * 0.05
            },
            sx: {
                cursor: 'pointer',
                '&:hover': {
                    backgroundColor: 'rgba(0,234,255,0.05)',
                    transform: 'scale(1.01)',
                    transition: 'all 0.2s ease'
                }
            }
        }, /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                alignItems: 'center',
                gap: 1
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            sx: {
                color: '#fff',
                fontWeight: 700
            }
        }, opportunity.symbol))), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                gap: 1
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Tooltip, {
            title: `Buy from ${opportunity.buyExchange}`
        }, /*#__PURE__*/_react.default.createElement(_material.Chip, {
            avatar: /*#__PURE__*/_react.default.createElement(_material.Avatar, {
                sx: {
                    bgcolor: (0, _exchangeColors.getExchangeColor)(opportunity.buyExchange),
                    width: 20,
                    height: 20,
                    fontSize: '0.7rem'
                }
            }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.TrendingUp, {
                sx: {
                    fontSize: '0.8rem'
                }
            })),
            label: (_opportunity$buyExcha = opportunity.buyExchange) === null || _opportunity$buyExcha === void 0 ? void 0 : _opportunity$buyExcha.toUpperCase(),
            size: 'small',
            sx: {
                backgroundColor: 'rgba(76,175,80,0.2)',
                color: '#4caf50',
                fontWeight: 600
            }
        })), /*#__PURE__*/_react.default.createElement(_iconsMaterial.CompareArrows, {
            sx: {
                color: '#888',
                fontSize: '1rem'
            }
        }), /*#__PURE__*/_react.default.createElement(_material.Tooltip, {
            title: `Sell to ${opportunity.sellExchange}`
        }, /*#__PURE__*/_react.default.createElement(_material.Chip, {
            avatar: /*#__PURE__*/_react.default.createElement(_material.Avatar, {
                sx: {
                    bgcolor: (0, _exchangeColors.getExchangeColor)(opportunity.sellExchange),
                    width: 20,
                    height: 20,
                    fontSize: '0.7rem'
                }
            }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.TrendingDown, {
                sx: {
                    fontSize: '0.8rem'
                }
            })),
            label: (_opportunity$sellExch = opportunity.sellExchange) === null || _opportunity$sellExch === void 0 ? void 0 : _opportunity$sellExch.toUpperCase(),
            size: 'small',
            sx: {
                backgroundColor: 'rgba(244,67,54,0.2)',
                color: '#f44336',
                fontWeight: 600
            }
        })))), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            sx: {
                color: getOpportunityColor(opportunity.profitPercent),
                fontWeight: 900,
                fontSize: '1.1rem'
            }
        }, '+', formatPercentage(opportunity.profitPercent))), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            sx: {
                color: '#00eaff',
                fontWeight: 700
            }
        }, formatCurrency(opportunity.profitUsdt))), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            sx: {
                color: '#888'
            }
        }, (_opportunity$volume = opportunity.volume) === null || _opportunity$volume === void 0 ? void 0 : _opportunity$volume.toFixed(4))), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            sx: {
                color: '#888'
            }
        }, opportunity.detectedAt ? `${Math.floor((Date.now() - opportunity.detectedAt) / 1000)}s` : 'N/A')), /*#__PURE__*/_react.default.createElement(_material.TableCell, null, /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                gap: 1
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Tooltip, {
            title: 'Execute Arbitrage'
        }, /*#__PURE__*/_react.default.createElement(_material.IconButton, {
            size: 'small',
            onClick: () => {
                setSelectedOpportunity(opportunity);
                setExecutionDialogOpen(true);
            },
            sx: {
                color: '#4caf50',
                '&:hover': {
                    backgroundColor: 'rgba(76,175,80,0.1)'
                }
            }
        }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.PlayArrow, null))), /*#__PURE__*/_react.default.createElement(_material.Tooltip, {
            title: 'View Details'
        }, /*#__PURE__*/_react.default.createElement(_material.IconButton, {
            size: 'small',
            onClick: () => {
                setExpandedOpportunity(expandedOpportunity === opportunity.id ? null : opportunity.id);
            },
            sx: {
                color: '#00eaff',
                '&:hover': {
                    backgroundColor: 'rgba(0,234,255,0.1)'
                }
            }
        }, expandedOpportunity === opportunity.id ? /*#__PURE__*/_react.default.createElement(_iconsMaterial.ExpandLess, null) : /*#__PURE__*/_react.default.createElement(_iconsMaterial.ExpandMore, null))))));
    }))))), filteredOpportunities.length === 0 && /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            textAlign: 'center',
            py: 6
        }
    }, /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        animate: {
            rotate: 360,
            scale: [1, 1.1, 1]
        },
        transition: {
            duration: 4,
            repeat: Infinity
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.Analytics, {
        sx: {
            fontSize: 80,
            opacity: 0.3,
            mb: 2,
            color: '#00eaff'
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        sx: {
            color: '#888',
            fontWeight: 600
        }
    }, isActive ? '🔍 Scanning for arbitrage opportunities...' : '⏸️ Engine stopped - Start to scan for opportunities')))), activePositions.length > 0 && /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        initial: {
            opacity: 0,
            y: 20
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.8,
            delay: 0.8
        }
    }, /*#__PURE__*/_react.default.createElement(_HolographicCard.default, {
        variant: 'premium',
        elevation: 'high',
        sx: {
            mb: 3
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#a259ff',
            p: 2,
            borderBottom: '1px solid rgba(162,89,255,0.2)',
            fontWeight: 800,
            display: 'flex',
            alignItems: 'center',
            gap: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_iconsMaterial.AccountBalanceWallet, null), '\uD83D\uDCBC ACTIVE ARBITRAGE POSITIONS', /*#__PURE__*/_react.default.createElement(_material.Chip, {
        label: `${activePositions.length} Open`,
        size: 'small',
        sx: {
            backgroundColor: 'rgba(162,89,255,0.2)',
            color: '#a259ff',
            fontWeight: 600
        }
    })), /*#__PURE__*/_react.default.createElement(_material.List, {
        sx: {
            p: 0
        }
    }, activePositions.map((position, index) => /*#__PURE__*/_react.default.createElement(_framerMotion.motion.div, {
        key: position.id,
        initial: {
            opacity: 0,
            x: -20
        },
        animate: {
            opacity: 1,
            x: 0
        },
        transition: {
            duration: 0.3,
            delay: index * 0.1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.ListItem, {
        sx: {
            borderBottom: '1px solid rgba(162,89,255,0.1)',
            '&:hover': {
                backgroundColor: 'rgba(162,89,255,0.05)'
            }
        }
    }, /*#__PURE__*/_react.default.createElement(_material.ListItemText, {
        primary: /*#__PURE__*/_react.default.createElement(_material.Box, {
            sx: {
                display: 'flex',
                alignItems: 'center',
                gap: 2
            }
        }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body1',
            sx: {
                color: '#fff',
                fontWeight: 700
            }
        }, position.symbol), /*#__PURE__*/_react.default.createElement(_material.Chip, {
            label: position.status,
            size: 'small',
            color: position.status === 'completed' ? 'success' : 'warning'
        })),
        secondary: /*#__PURE__*/_react.default.createElement(_material.Typography, {
            variant: 'body2',
            sx: {
                color: '#888'
            }
        }, position.buyExchange, ' \u2192 ', position.sellExchange, ' | Size: ', position.positionSize, ' | P&L: ', formatCurrency(position.profitLoss || 0))
    }))))))), /*#__PURE__*/_react.default.createElement(_material.Dialog, {
        open: executionDialogOpen,
        onClose: () => setExecutionDialogOpen(false),
        maxWidth: 'sm',
        fullWidth: true,
        PaperProps: {
            sx: {
                backgroundColor: '#181a20',
                color: '#fff',
                border: '1px solid #00eaff'
            }
        }
    }, /*#__PURE__*/_react.default.createElement(_material.DialogTitle, {
        sx: {
            color: '#00eaff',
            fontWeight: 800
        }
    }, '\u26A1 Execute Arbitrage Opportunity'), /*#__PURE__*/_react.default.createElement(_material.DialogContent, null, selectedOpportunity && /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            py: 2
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 2
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 6
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, 'Symbol:'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#fff'
        }
    }, selectedOpportunity.symbol)), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 6
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, 'Profit:'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'h6',
        sx: {
            color: '#4caf50'
        }
    }, formatPercentage(selectedOpportunity.profitPercent), '(', formatCurrency(selectedOpportunity.profitUsdt), ')')), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 6
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, 'Buy Exchange:'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body1',
        sx: {
            color: '#4caf50'
        }
    }, selectedOpportunity.buyExchange, ' @ ', selectedOpportunity.buyPrice)), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 6
    }, /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body2',
        sx: {
            color: '#888'
        }
    }, 'Sell Exchange:'), /*#__PURE__*/_react.default.createElement(_material.Typography, {
        variant: 'body1',
        sx: {
            color: '#f44336'
        }
    }, selectedOpportunity.sellExchange, ' @ ', selectedOpportunity.sellPrice))), /*#__PURE__*/_react.default.createElement(_material.Alert, {
        severity: 'warning',
        sx: {
            mt: 2,
            backgroundColor: 'rgba(255,193,7,0.1)'
        }
    }, '\u26A0\uFE0F This will execute real trades on both exchanges. Ensure sufficient balance.'))), /*#__PURE__*/_react.default.createElement(_material.DialogActions, null, /*#__PURE__*/_react.default.createElement(_material.Button, {
        onClick: () => setExecutionDialogOpen(false),
        sx: {
            color: '#888'
        }
    }, 'Cancel'), /*#__PURE__*/_react.default.createElement(_FuturisticButton.default, {
        onClick: () => handleExecuteOpportunity(selectedOpportunity),
        variant: 'success',
        startIcon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.FlashOn, null),
        disabled: isLoading
    }, '\u26A1 EXECUTE NOW'))), /*#__PURE__*/_react.default.createElement(_material.Dialog, {
        open: settingsOpen,
        onClose: () => setSettingsOpen(false),
        maxWidth: 'md',
        fullWidth: true,
        PaperProps: {
            sx: {
                backgroundColor: '#181a20',
                color: '#fff',
                border: '1px solid #00eaff'
            }
        }
    }, /*#__PURE__*/_react.default.createElement(_material.DialogTitle, {
        sx: {
            color: '#00eaff',
            fontWeight: 800
        }
    }, '\u2699\uFE0F Arbitrage Engine Configuration'), /*#__PURE__*/_react.default.createElement(_material.DialogContent, null, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        container: true,
        spacing: 3,
        sx: {
            mt: 1
        }
    }, /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6
    }, /*#__PURE__*/_react.default.createElement(_material.TextField, {
        fullWidth: true,
        label: 'Min Profit Threshold (%)',
        type: 'number',
        value: draftConfig.minProfitThreshold,
        onChange: e => setDraftConfig(prev => _objectSpread(_objectSpread({}, prev), {}, {
            minProfitThreshold: parseFloat(e.target.value)
        })),
        inputProps: {
            step: 0.1,
            min: 0.1,
            max: 5.0
        },
        sx: {
            '& .MuiInputLabel-root': {
                color: '#888'
            }
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6
    }, /*#__PURE__*/_react.default.createElement(_material.TextField, {
        fullWidth: true,
        label: 'Max Position Size (USDT)',
        type: 'number',
        value: draftConfig.maxPositionSize,
        onChange: e => setDraftConfig(prev => _objectSpread(_objectSpread({}, prev), {}, {
            maxPositionSize: parseFloat(e.target.value)
        })),
        inputProps: {
            step: 100,
            min: 100,
            max: 10000
        },
        sx: {
            '& .MuiInputLabel-root': {
                color: '#888'
            }
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6
    }, /*#__PURE__*/_react.default.createElement(_material.TextField, {
        fullWidth: true,
        label: 'Max Slippage (%)',
        type: 'number',
        value: draftConfig.maxSlippage,
        onChange: e => setDraftConfig(prev => _objectSpread(_objectSpread({}, prev), {}, {
            maxSlippage: parseFloat(e.target.value)
        })),
        inputProps: {
            step: 0.1,
            min: 0.1,
            max: 2.0
        },
        sx: {
            '& .MuiInputLabel-root': {
                color: '#888'
            }
        }
    })), /*#__PURE__*/_react.default.createElement(_material.Grid, {
        item: true,
        xs: 12,
        sm: 6
    }, /*#__PURE__*/_react.default.createElement(_material.TextField, {
        fullWidth: true,
        label: 'Max Concurrent Trades',
        type: 'number',
        value: draftConfig.maxConcurrentTrades,
        onChange: e => setDraftConfig(prev => _objectSpread(_objectSpread({}, prev), {}, {
            maxConcurrentTrades: parseInt(e.target.value)
        })),
        inputProps: {
            step: 1,
            min: 1,
            max: 20
        },
        sx: {
            '& .MuiInputLabel-root': {
                color: '#888'
            }
        }
    })))), /*#__PURE__*/_react.default.createElement(_material.DialogActions, null, /*#__PURE__*/_react.default.createElement(_material.Button, {
        onClick: () => {
            setSettingsOpen(false);
            setDraftConfig(config);
        },
        sx: {
            color: '#888'
        }
    }, 'Cancel'), /*#__PURE__*/_react.default.createElement(_FuturisticButton.default, {
        onClick: () => handleConfigUpdate(draftConfig),
        variant: 'primary',
        startIcon: /*#__PURE__*/_react.default.createElement(_iconsMaterial.Security, null)
    }, '\uD83D\uDCBE SAVE CONFIG'))), isLoading && /*#__PURE__*/_react.default.createElement(_material.Box, {
        sx: {
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            zIndex: 9999
        }
    }, /*#__PURE__*/_react.default.createElement(_material.LinearProgress, {
        sx: {
            backgroundColor: 'rgba(0,234,255,0.1)',
            '& .MuiLinearProgress-bar': {
                backgroundColor: '#00eaff'
            }
        }
    })));
};
ArbitrageOpportunityPanel.propTypes = {
    showNotification: _propTypes.default.func
};
ArbitrageOpportunityPanel.defaultProps = {
    showNotification: (message, severity) => {
        _logger.default.info(`${(severity === null || severity === void 0 ? void 0 : severity.toUpperCase()) || 'INFO'}: ${message}`);
    }
};
const _default = exports.default = ArbitrageOpportunityPanel;