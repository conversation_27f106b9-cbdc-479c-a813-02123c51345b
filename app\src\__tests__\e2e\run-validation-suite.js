#!/usr/bin/env node

/**
 * End-to-End Validation Suite Runner
 *
 * This script runs all end-to-end validation tests to ensure
 * the complete application workflow is working correctly.
 */

const {spawn} = require('child_process');
const path = require('path');
const fs = require('fs').promises;

class ValidationSuiteRunner {
    constructor() {
        // this.results = {
            tests: [],
            summary: {
                total: 0,
                passed: 0,
                failed: 0,
                duration: 0
            }
        };
        // this.startTime = Date.now();
    }

    async runTest(testFile, description) {
        // console.log(`\n🧪 Running: ${description}`);
        // console.log(`📁 File: ${testFile}`);
        // console.log('─'.repeat(60));

        const testStartTime = Date.now();

        try {
            // Check if test file exists
            const testPath = path.join(__dirname, testFile);
            await fs.access(testPath);

            // Run the test using Jest
            const result = await this.executeJestTest(testPath);

            const duration = Date.now() - testStartTime;

            // this.results.tests.push({
                file: testFile,
                description,
                success: result.success,
                duration,
                output: result.output,
                errors: result.errors
            });

            if (result.success) {
                console.log(`✅ PASSED: ${description} (${duration}ms)`);
                // this.results.summary.passed++;
            } else {
                console.log(`❌ FAILED: ${description} (${duration}ms)`);
                console.log(`Error: ${result.errors.join(', ')}`);
                // this.results.summary.failed++;
            }

        } catch (error) {
            const duration = Date.now() - testStartTime;

            // this.results.tests.push({
                file: testFile,
                description,
                success: false,
                duration,
                output: '',
                errors: [error.message]
            });

            console.log(`❌ FAILED: ${description} (${duration}ms)`);
            console.log(`Error: ${error.message}`);
            // this.results.summary.failed++;
        }

        // this.results.summary.total++;
    }

    executeJestTest(testPath) {
        return new Promise((resolve) => {
            const jest = spawn('npx', ['jest', testPath, '--verbose'], {
                cwd: path.join(__dirname, '../../../'),
                stdio: 'pipe'
            });

            let output = '';
            const errors = [];

            jest.stdout.on('data', (data) => {
                output += data.toString();
            });

            jest.stderr.on('data', (data) => {
                const errorText = data.toString();
                if (!errorText.includes('console.log') && !errorText.includes('console.warn')) {
                    errors.push(errorText);
                }
            });

            jest.on('close', (code) => {
                resolve({
                    success: code === 0,
                    output,
                    errors
                });
            });

            jest.on('error', (error) => {
                resolve({
                    success: false,
                    output,
                    errors: [error.message]
                });
            });
        });
    }

    async runValidationSuite() {
        // console.log('🚀 Starting End-to-End Application Validation Suite');
        // console.log('='.repeat(60));

        const testSuite = [
            {
                file: 'startup-sequence-validation.test.js',
                description: 'Application Startup Sequence Validation'
            },
            {
                file: 'real-application-integration.test.js',
                description: 'Real Application Integration Validation'
            },
            {
                file: 'complete-end-to-end-validation.test.js',
                description: 'Complete End-to-End Workflow Validation'
            },
            {
                file: 'ipc-load-test.js',
                description: 'IPC Communication Load Testing'
            }];

        // Run each test
        for (const test of testSuite) {
            await this.runTest(test.file, test.description);
        }

        // Calculate final results
        // this.results.summary.duration = Date.now() - this.startTime;

        // Generate and display report
        // this.generateFinalReport();

        return this.results;
    }

    generateFinalReport() {
        // console.log('\n' + '='.repeat(60));
        // console.log('📊 VALIDATION SUITE RESULTS');
        // console.log('='.repeat(60));

        // console.log(`📈 Total Tests: ${this.results.summary.total}`);
        // console.log(`✅ Passed: ${this.results.summary.passed}`);
        // console.log(`❌ Failed: ${this.results.summary.failed}`);
        // console.log(`⏱️  Total Duration: ${this.results.summary.duration}ms`);

        const _successRate = (this.results.summary.passed / this.results.summary.total) * 100;
        // console.log(`📊 Success Rate: ${successRate.toFixed(2)}%`);

        // console.log('\n📋 DETAILED RESULTS:');
        // console.log('-'.repeat(60));

        // this.results.tests.forEach((test, index) => {
            const status = test.success ? '✅' : '❌';
            console.log(`${index + 1}. ${status} ${test.description}`);
            console.log(`   📁 ${test.file}`);
            // console.log(`   ⏱️  ${test.duration}ms`);

            if (!test.success && test.errors.length > 0) {
                console.log('   🚨 Errors:');
                test.errors.forEach(error => {
                    console.log(`      - ${error.trim()}`);
                });
            }
            console.log('');
        });

        // Generate recommendations
        // console.log('💡 RECOMMENDATIONS:');
        // console.log('-'.repeat(60));

        if (this.results.summary.failed === 0) {
            // console.log('🎉 All tests passed! The application is ready for end-to-end usage.');
            // console.log('✨ The Start button should successfully launch the complete trading system.');
            // console.log('🔧 All IPC channels are working correctly under load.');
            // console.log('🛡️  Error handling and recovery mechanisms are in place.');
        } else {
            // console.log('⚠️  Some tests failed. Please address the following issues:');

            const failedTests = this.results.tests.filter(test => !test.success);
            failedTests.forEach(test => {
                console.log(`\n🔍 ${test.description}:`);
                test.errors.forEach(error => {
                    console.log(`   - ${error.trim()}`);
                });
            });

            // console.log('\n🔧 Suggested Actions:');
            // console.log('   1. Review and fix the failed test cases');
            // console.log('   2. Ensure all required files and components exist');
            // console.log('   3. Verify IPC channels are properly configured');
            // console.log('   4. Test the Start button workflow manually');
            // console.log('   5. Re-run this validation suite after fixes');
        }

        // console.log('\n' + '='.repeat(60));

        // Save results to file
        // this.saveResultsToFile();
    }

    async saveResultsToFile() {
        try {
            const resultsPath = path.join(__dirname, 'validation-results.json');
            await fs.writeFile(resultsPath, JSON.stringify(this.results, null, 2));
            console.log(`📄 Results saved to: ${resultsPath}`);
        } catch (error) {
            console.warn('⚠️  Could not save results to file:', error.message);
        }
    }

    async checkPrerequisites() {
        // console.log('🔍 Checking prerequisites...');

        const prerequisites = [
            {
                name: 'Node.js',
                check: () => process.version,
                required: true
            },
            {
                name: 'Jest',
                check: () => {
                    try {
                        const {spawn} = require('child_process');
                        return new Promise((resolve) => {
                            const jest = spawn('npx', ['jest', '--version'], {stdio: 'pipe'});
                            jest.on('close', (code) => resolve(code === 0));
                            jest.on('error', () => resolve(false));
                        });
                    } catch {
                        return false;
                    }
                },
                required: true
            },
            {
                name: 'Application Files',
                check: async () => {
                    const criticalFiles = [
                        '../../../main.js',
                        '../../../preload.js',
                        '../../../package.json',
                        '../../index.jsx'];

                    try {
                        for (const file of criticalFiles) {
                            await fs.access(path.join(__dirname, file));
                        }
                        return true;
                    } catch {
                        return false;
                    }
                },
                required: true
            }];

        let allPrerequisitesMet = true;

        for (const prereq of prerequisites) {
            try {
                const result = await prereq.check();
                if (result) {
                    // console.log(`✅ ${prereq.name}ailable`);
                } else {
                    // console.log(`❌ ${prereq.name}t available`);
                    if (prereq.required) {
                        allPrerequisitesMet = false;
                    }
                }
            } catch (error) {
                // console.log(`❌ ${prereq.name}ror checking (${error.message})`);
                if (prereq.required) {
                    allPrerequisitesMet = false;
                }
            }
        }

        if (!allPrerequisitesMet) {
            // console.log('\n⚠️  Some required prerequisites are missing.');
            // console.log('Please ensure all requirements are met before running the validation suite.');
            return false;
        }

        // console.log('✅ All prerequisites met!\n');
        return true;
    }
}

// Main execution
async function main() {
    const runner = new ValidationSuiteRunner();

    try {
        // Check prerequisites first
        const prerequisitesMet = await runner.checkPrerequisites();
        if (!prerequisitesMet) {
            process.exit(1);
        }

        // Run the validation suite
        const results = await runner.runValidationSuite();

        // Exit with appropriate code
        const exitCode = results.summary.failed > 0 ? 1 : 0;
        process.exit(exitCode);

    } catch (error) {
        // console.error('💥 Fatal error running validation suite:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = ValidationSuiteRunner;