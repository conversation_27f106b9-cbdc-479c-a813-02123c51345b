{"definitions": [{"name": "IPCResponse", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 6, "column": 0}, "definition": "export interface IPCResponse<T = any> {\r\n    success: boolean;\r\n    data?: T;\r\n    error?: string;\r\n}", "exports": true, "dependencies": ["T"], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 68, "column": 28}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 69, "column": 27}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 70, "column": 32}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 75, "column": 37}, "context": "IPCResponse<RealTimeStatusData>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 76, "column": 35}, "context": "IPCResponse<SystemHealthData>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 77, "column": 59}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 78, "column": 36}, "context": "IPCResponse<SystemMetricsData>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 79, "column": 33}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 80, "column": 35}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 85, "column": 41}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 86, "column": 40}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 87, "column": 74}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 88, "column": 43}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 89, "column": 56}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 90, "column": 31}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 95, "column": 39}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 96, "column": 41}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 97, "column": 35}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 98, "column": 49}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 99, "column": 34}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 100, "column": 38}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 105, "column": 67}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 106, "column": 37}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 107, "column": 69}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 108, "column": 51}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 113, "column": 35}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 114, "column": 36}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 115, "column": 42}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 120, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 121, "column": 40}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 122, "column": 39}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 127, "column": 31}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 128, "column": 45}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 133, "column": 37}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 138, "column": 102}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 139, "column": 84}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 140, "column": 91}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 145, "column": 18}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 150, "column": 59}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 151, "column": 46}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 152, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 153, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 154, "column": 38}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 155, "column": 43}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 156, "column": 42}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 157, "column": 45}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 158, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 159, "column": 66}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 164, "column": 33}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 165, "column": 33}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 170, "column": 57}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 171, "column": 29}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 172, "column": 30}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 173, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 174, "column": 56}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 179, "column": 40}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 180, "column": 42}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 181, "column": 36}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 182, "column": 49}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 183, "column": 48}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 184, "column": 34}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 185, "column": 45}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 190, "column": 48}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 191, "column": 49}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 192, "column": 46}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 193, "column": 35}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 194, "column": 46}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 195, "column": 47}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 196, "column": 49}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 201, "column": 55}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 202, "column": 49}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 203, "column": 52}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 204, "column": 52}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 209, "column": 36}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 210, "column": 38}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 211, "column": 50}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 216, "column": 48}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 217, "column": 37}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 222, "column": 32}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 223, "column": 42}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 224, "column": 64}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 225, "column": 72}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 226, "column": 69}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 231, "column": 28}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 232, "column": 37}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 233, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 234, "column": 70}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 239, "column": 36}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 240, "column": 49}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 241, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 242, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 243, "column": 68}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 244, "column": 47}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 245, "column": 62}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 250, "column": 39}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 251, "column": 40}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 252, "column": 35}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 253, "column": 58}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 254, "column": 73}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 259, "column": 61}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 260, "column": 39}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 265, "column": 41}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 266, "column": 38}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 267, "column": 40}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 268, "column": 39}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 269, "column": 52}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 274, "column": 46}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 275, "column": 44}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 276, "column": 43}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 277, "column": 42}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 278, "column": 61}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 283, "column": 32}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 288, "column": 33}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 289, "column": 34}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 290, "column": 47}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 303, "column": 36}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 304, "column": 38}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 305, "column": 39}, "context": "IPCResponse"}]}, {"name": "SystemHealthData", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 12, "column": 0}, "definition": "export interface SystemHealthData {\r\n    status: string;\r\n    uptime: number;\r\n    cpu: number;\r\n    memory: number;\r\n    components?: Record<string, any>;\r\n}", "exports": true, "dependencies": ["Record"], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 76, "column": 47}, "context": "SystemHealthData"}]}, {"name": "RealTimeStatusData", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 20, "column": 0}, "definition": "export interface RealTimeStatusData {\r\n    isRunning: boolean;\r\n    isInitialized: boolean;\r\n    health: string;\r\n    message?: string;\r\n    timestamp: number;\r\n    uptime?: number;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 75, "column": 49}, "context": "RealTimeStatusData"}]}, {"name": "SystemMetricsData", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 29, "column": 0}, "definition": "export interface SystemMetricsData {\r\n    performance: any;\r\n    health: string;\r\n    uptime: number;\r\n    activeSignals: number;\r\n    pendingTrades: number;\r\n    lastUpdate: number;\r\n    systemLoad: {\r\n        activeBots: number;\r\n        dataCollectionActive: boolean;\r\n        analysisActive: boolean;\r\n    };\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 78, "column": 48}, "context": "SystemMetricsData"}]}, {"name": "ElectronAPI", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 43, "column": 0}, "definition": "export interface ElectronAPI {\r\n    stopAllGrids(): unknown;\r\n    startAutonomousTrading(): unknown;\r\n    stopAutonomousTrading(): unknown;\r\n    getAutonomousStatus(): unknown;\r\n    checkDatabaseReady(): unknown;\r\n    getCrossExchangeBalance(): unknown;\r\n    updateGridBotConfig(config: any): unknown;\r\n    generateDatabaseHealthReport(): unknown;\r\n    getDatabaseStatistics(): unknown;\r\n    storeTradingTransaction(transaction: any): unknown;\r\n    getTradingTransactions(filters: any): unknown;\r\n    updatePortfolioPosition(position: any): unknown;\r\n    getPortfolioPositions(): unknown;\r\n    storePerformanceMetrics(metrics: any): unknown;\r\n    storeWhaleWallet(wallet: any): unknown;\r\n    storeWhaleTransaction(transaction: any): unknown;\r\n    getWhaleWallets(filters: any): unknown;\r\n    storeTradingSignal(signal: any): unknown;\r\n    getTradingSignals(filters: any): unknown;\r\n    getIPCErrorStatistics(): unknown;\r\n    resetIPCErrorStatistics(): unknown;\r\n    // ========================================================================================\r\n    // Core Bot Control Methods\r\n    // ========================================================================================\r\n    startBot: () => Promise<IPCResponse>;\r\n    stopBot: () => Promise<IPCResponse>;\r\n    getBotStatus: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Real-time Status and Health Methods\r\n    // ========================================================================================\r\n    getRealTimeStatus: () => Promise<IPCResponse<RealTimeStatusData>>;\r\n    getSystemHealth: () => Promise<IPCResponse<SystemHealthData>>;\r\n    getComponentHealth: (componentName: string) => Promise<IPCResponse>;\r\n    getSystemMetrics: () => Promise<IPCResponse<SystemMetricsData>>;\r\n    getActiveBots: () => Promise<IPCResponse<any[]>>;\r\n    getSystemAlerts: () => Promise<IPCResponse<any[]>>;\r\n\r\n    // ========================================================================================\r\n    // Health Monitoring Methods\r\n    // ========================================================================================\r\n    startHealthMonitoring: () => Promise<IPCResponse>;\r\n    stopHealthMonitoring: () => Promise<IPCResponse>;\r\n    getStatusReports: (params: { limit: number; filter: any }) => Promise<IPCResponse<any[]>>;\r\n    getMonitoringStatistics: () => Promise<IPCResponse>;\r\n    runHealthCheck: (componentName?: string) => Promise<IPCResponse>;\r\n    healthCheck: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Trading and Portfolio Methods\r\n    // ========================================================================================\r\n    getPortfolioSummary: () => Promise<IPCResponse>;\r\n    getPerformanceMetrics: () => Promise<IPCResponse>;\r\n    getTradingStats: () => Promise<IPCResponse>;\r\n    getTradeHistory: (limit?: number) => Promise<IPCResponse<any[]>>;\r\n    getRiskMetrics: () => Promise<IPCResponse>;\r\n    getAssetAllocation: () => Promise<IPCResponse<any[]>>;\r\n\r\n    // ========================================================================================\r\n    // Market Data Methods\r\n    // ========================================================================================\r\n    getMarketData: (symbol: string, timeframe?: string) => Promise<IPCResponse>;\r\n    getMarketOverview: () => Promise<IPCResponse>;\r\n    getPriceHistory: (symbol: string, timeframe?: string) => Promise<IPCResponse<any[]>>;\r\n    getSentimentData?: (symbol: string) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Whale Tracking Methods\r\n    // ========================================================================================\r\n    getWhaleSignals: () => Promise<IPCResponse>;\r\n    getTrackedWhales: () => Promise<IPCResponse>;\r\n    getWhaleTrackingStatus: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Meme Coin Scanner Methods\r\n    // ========================================================================================\r\n    getMemeCoinOpportunities: () => Promise<IPCResponse>;\r\n    startMemeCoinScanner: () => Promise<IPCResponse>;\r\n    stopMemeCoinScanner: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Settings and Configuration Methods\r\n    // ========================================================================================\r\n    getSettings: () => Promise<IPCResponse>;\r\n    saveSettings: (settings: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Trading System Initialization\r\n    // ========================================================================================\r\n    initializeTrading: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Error Recovery Methods (Optional - may not be implemented)\r\n    // ========================================================================================\r\n    restartComponent?: (params: { componentName: string; strategy: string; context: any }) => Promise<IPCResponse>;\r\n    isolateComponent?: (params: { componentName: string; context: any }) => Promise<IPCResponse>;\r\n    recoverTradingSystem?: (params: { component?: string; errorType?: string }) => Promise<IPCResponse>;\r\n    triggerEmergencyProtocols?: (params: {\r\n        componentName: string;\r\n        error: string;\r\n        context: any\r\n    }) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Legacy/Compatibility Methods\r\n    // ========================================================================================\r\n    getPerformanceHistory?: (timeRange: string) => Promise<IPCResponse<any[]>>;\r\n    getArbitrageOpportunities?: () => Promise<IPCResponse<any[]>>;\r\n    getPortfolioRiskMetrics?: () => Promise<IPCResponse>;\r\n    getPortfolioPerformance?: () => Promise<IPCResponse>;\r\n    getArbitrageStats?: () => Promise<IPCResponse>;\r\n    startArbitrageScanning?: () => Promise<IPCResponse>;\r\n    stopArbitrageScanning?: () => Promise<IPCResponse>;\r\n    startPortfolioMonitoring?: () => Promise<IPCResponse>;\r\n    stopPortfolioMonitoring?: () => Promise<IPCResponse>;\r\n    executeArbitrage?: (payload: { opportunity: any }) => Promise<IPCResponse & { actualProfit?: number }>;\r\n\r\n    // ========================================================================================\r\n    // System Information Methods\r\n    // ========================================================================================\r\n    getSystemInfo: () => Promise<IPCResponse>;\r\n    getAppVersion: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Logging Methods\r\n    // ========================================================================================\r\n    getLogs: (level?: string, limit?: number) => Promise<IPCResponse<any[]>>;\r\n    clearLogs: () => Promise<IPCResponse>;\r\n    exportLogs: () => Promise<IPCResponse>;\r\n    setLogLevel: (level: string) => Promise<IPCResponse>;\r\n    reportError: (error: any, context?: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Grid Trading Methods\r\n    // ========================================================================================\r\n    startGrid: (config: any) => Promise<IPCResponse>;\r\n    stopGrid: (gridId: string) => Promise<IPCResponse>;\r\n    getGridPositions: () => Promise<IPCResponse<any[]>>;\r\n    getGridHistory: (gridId?: string) => Promise<IPCResponse<any[]>>;\r\n    updateGridConfig?: (config: any) => Promise<IPCResponse>;\r\n    getGridPresets: () => Promise<IPCResponse<any[]>>;\r\n    saveGridPreset: (preset: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Order Management Methods\r\n    // ========================================================================================\r\n    getOpenOrders: (symbol?: string) => Promise<IPCResponse<any[]>>;\r\n    getOrderHistory: (limit?: number) => Promise<IPCResponse<any[]>>;\r\n    cancelOrder: (orderId: string) => Promise<IPCResponse>;\r\n    cancelAllOrders: () => Promise<IPCResponse>;\r\n    placeLimitOrder: (params: any) => Promise<IPCResponse>;\r\n    placeMarketOrder: (params: any) => Promise<IPCResponse>;\r\n    executeTrade?: (tradeParams: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Whale Tracking Extended Methods\r\n    // ========================================================================================\r\n    toggleWhaleTracking: (enabled: boolean) => Promise<IPCResponse>;\r\n    addWhaleWallet: (address: string) => Promise<IPCResponse>;\r\n    removeWhaleWallet: (address: string) => Promise<IPCResponse>;\r\n    getWhaleHistory: (timeframe: string) => Promise<IPCResponse<any[]>>;\r\n\r\n    // ========================================================================================\r\n    // Meme Coin Scanner Extended Methods\r\n    // ========================================================================================\r\n    getScannerStatus: () => Promise<IPCResponse>;\r\n    getMemeCoinHistory: () => Promise<IPCResponse<any[]>>;\r\n    updateScannerConfig: (config: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Risk Management Methods\r\n    // ========================================================================================\r\n    setRiskParameters: (params: any) => Promise<IPCResponse>;\r\n    getRiskParameters: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Exchange Management Methods\r\n    // ========================================================================================\r\n    getExchanges: () => Promise<IPCResponse<any[]>>;\r\n    addExchange: (config: any) => Promise<IPCResponse>;\r\n    removeExchange: (params: { exchangeId: string }) => Promise<IPCResponse>;\r\n    testExchangeConnection: (params: { exchangeId: string }) => Promise<IPCResponse>;\r\n    getExchangeBalances: (params: { exchangeId: string }) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Coin Management Methods\r\n    // ========================================================================================\r\n    getCoins: () => Promise<IPCResponse<any[]>>;\r\n    saveCoin: (coin: any) => Promise<IPCResponse>;\r\n    deleteCoin: (coinId: string) => Promise<IPCResponse>;\r\n    updateCoin: (params: { coinId: string; updates: any }) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Portfolio Extended Methods\r\n    // ========================================================================================\r\n    getWalletBalance: () => Promise<IPCResponse>;\r\n    rebalancePortfolio: (target: any) => Promise<IPCResponse>;\r\n    getPortfolioOptimization: () => Promise<IPCResponse>;\r\n    getCrossExchangeBalances: () => Promise<IPCResponse>;\r\n    getExchangePortfolio: (params: { exchange: string }) => Promise<IPCResponse>;\r\n    getRebalancingOpportunities: () => Promise<IPCResponse<any[]>>;\r\n    rebalanceCrossExchangePortfolio: (config: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // DCA Trading Methods\r\n    // ========================================================================================\r\n    startDCA: (config: any) => Promise<IPCResponse>;\r\n    stopDCA: (dcaId: string) => Promise<IPCResponse>;\r\n    getDCAPositions: () => Promise<IPCResponse<any[]>>;\r\n    getDCAHistory: (params: { dcaId: string }) => Promise<IPCResponse<any[]>>;\r\n    updateDCAConfig: (params: { dcaId: string; config: any }) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Analytics Extended Methods\r\n    // ========================================================================================\r\n    getPnLReport: (params: { timeframe: string }) => Promise<IPCResponse>;\r\n    getDrawdownAnalysis: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Arbitrage Engine Methods\r\n    // ========================================================================================\r\n    getArbitragePositions: () => Promise<IPCResponse<any[]>>;\r\n    getArbitrageStatus: () => Promise<IPCResponse>;\r\n    startArbitrageEngine: () => Promise<IPCResponse>;\r\n    stopArbitrageEngine: () => Promise<IPCResponse>;\r\n    updateArbitrageConfig: (config: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Opportunity Scanner Methods\r\n    // ========================================================================================\r\n    getOpportunityScannerStats: () => Promise<IPCResponse>;\r\n    getDetectedOpportunities: () => Promise<IPCResponse<any[]>>;\r\n    startOpportunityScanner: () => Promise<IPCResponse>;\r\n    stopOpportunityScanner: () => Promise<IPCResponse>;\r\n    updateOpportunityScannerConfig: (config: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Backup & Recovery Methods\r\n    // ========================================================================================\r\n    createBackup: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Settings Extended Methods\r\n    // ========================================================================================\r\n    resetSettings: () => Promise<IPCResponse>;\r\n    exportSettings: () => Promise<IPCResponse>;\r\n    importSettings: (settings: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Additional Methods Referenced in Components\r\n    // ========================================================================================\r\n    invoke?: (channel: string, ...args: any[]) => Promise<any>;\r\n    on?: (channel: string, callback: (event: any, ...args: any[]) => void) => () => void;\r\n    onArbitrageOpportunity?: (callback: (data: any) => void) => () => void;\r\n    onArbitrageExecuted?: (callback: (data: any) => void) => () => void;\r\n\r\n    // ========================================================================================\r\n    // System Status Methods\r\n    // ========================================================================================\r\n    getSystemStatus?: () => Promise<IPCResponse>;\r\n    getDatabaseStatus?: () => Promise<IPCResponse>;\r\n    getDatabaseMetrics?: () => Promise<IPCResponse>;\r\n}", "exports": true, "dependencies": ["Promise", "IPCResponse", "RealTimeStatusData", "SystemHealthData", "SystemMetricsData"], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 310, "column": 21}, "context": "ElectronAPI"}, {"file": "app\\src\\types\\electron.d.ts", "location": {"line": 17, "column": 22}, "context": "ElectronAPI"}]}, {"name": "Window", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 309, "column": 4}, "definition": "interface Window {\r\n        electronAPI: ElectronAPI;\r\n    }", "exports": false, "dependencies": ["ElectronAPI"], "usages": []}, {"name": "ElectronAPI", "kind": "interface", "file": "app\\src\\types\\electron.d.ts", "location": {"line": 7, "column": 4}, "definition": "interface ElectronAPI {\r\n        [x: string]: any;\r\n        on?: (event: string, listener: (...args: any[]) => void) => void;\r\n        removeListener?: (event: string, listener: (...args: any[]) => void) => void;\r\n        emit?: (event: string, ...args: any[]) => void;\r\n        getMarketData?: (symbol?: string) => Promise<{ success: boolean; data: any[] }>;\r\n        fetchPriceHistory?: (symbol?: string, timeframe?: string) => Promise<{ success: boolean; data: any[] }>;\r\n    }", "exports": false, "dependencies": ["Promise"], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 310, "column": 21}, "context": "ElectronAPI"}, {"file": "app\\src\\types\\electron.d.ts", "location": {"line": 17, "column": 22}, "context": "ElectronAPI"}]}, {"name": "Window", "kind": "interface", "file": "app\\src\\types\\electron.d.ts", "location": {"line": 16, "column": 4}, "definition": "interface Window {\r\n        electronAPI?: ElectronAPI;\r\n    }", "exports": false, "dependencies": ["ElectronAPI"], "usages": []}, {"name": "Matchers", "kind": "interface", "file": "app\\src\\__tests__\\jest.d.ts", "location": {"line": 9, "column": 8}, "definition": "interface Matchers<R> {\r\n            toBeInTheDocument(): R;\r\n\r\n            toHaveClass(className: string): R;\r\n\r\n            toHaveAttribute(attr: string, value?: string): R;\r\n\r\n            toHaveTextContent(text: string | RegExp): R;\r\n\r\n            toBeVisible(): R;\r\n\r\n            toBeDisabled(): R;\r\n\r\n            toBeEnabled(): R;\r\n\r\n            toHaveFocus(): R;\r\n\r\n            toHaveStyle(style: string | object): R;\r\n\r\n            toHaveValue(value: string | number): R;\r\n\r\n            toBeChecked(): R;\r\n\r\n            toBeInvalid(): R;\r\n\r\n            toBeValid(): R;\r\n\r\n            toBeRequired(): R;\r\n\r\n            toBeEmptyDOMElement(): R;\r\n\r\n            toContainElement(element: HTMLElement | null): R;\r\n\r\n            toContainHTML(htmlText: string): R;\r\n\r\n            toHaveDisplayValue(value: string | RegExp | (string | RegExp)[]): R;\r\n\r\n            toHaveFormValues(expectedValues: Record<string, any>): R;\r\n\r\n            toHaveErrorMessage(text: string | RegExp): R;\r\n\r\n            toHaveDescription(text: string | RegExp): R;\r\n\r\n            toHaveAccessibleName(name: string | RegExp): R;\r\n\r\n            toHaveAccessibleDescription(description: string | RegExp): R;\r\n        }", "exports": false, "dependencies": ["R", "RegExp", "HTMLElement", "Record"], "usages": []}, {"name": "Symbol", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 9, "column": 0}, "definition": "export type Symbol = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 23, "column": 12}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 33, "column": 12}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 43, "column": 12}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 89, "column": 12}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 109, "column": 12}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 142, "column": 24}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 143, "column": 26}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 144, "column": 29}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 145, "column": 26}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 150, "column": 22}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 151, "column": 29}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 155, "column": 24}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 156, "column": 27}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 157, "column": 26}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 174, "column": 36}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 175, "column": 34}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 178, "column": 38}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 179, "column": 36}, "context": "Symbol"}]}, {"name": "BotId", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 10, "column": 0}, "definition": "export type BotId = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 176, "column": 38}, "context": "BotId"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 177, "column": 36}, "context": "BotId"}]}, {"name": "ComponentName", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 11, "column": 0}, "definition": "export type ComponentName = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 127, "column": 16}, "context": "ComponentName"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 160, "column": 29}, "context": "ComponentName"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 161, "column": 25}, "context": "ComponentName"}]}, {"name": "ExchangeName", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 12, "column": 0}, "definition": "export type ExchangeName = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 172, "column": 38}, "context": "ExchangeName"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 173, "column": 36}, "context": "ExchangeName"}]}, {"name": "SessionId", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 13, "column": 0}, "definition": "export type SessionId = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 170, "column": 38}, "context": "SessionId"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 171, "column": 36}, "context": "SessionId"}]}, {"name": "Timeframe", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 14, "column": 0}, "definition": "export type Timeframe = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 155, "column": 32}, "context": "Timeframe"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 157, "column": 34}, "context": "Timeframe"}]}, {"name": "FilePath", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 15, "column": 0}, "definition": "export type FilePath = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 187, "column": 27}, "context": "FilePath"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 188, "column": 40}, "context": "FilePath"}]}, {"name": "FileExtension", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 16, "column": 0}, "definition": "export type FileExtension = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 185, "column": 41}, "context": "FileExtension"}]}, {"name": "FileType", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 17, "column": 0}, "definition": "export type FileType = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 184, "column": 36}, "context": "FileType"}]}, {"name": "SearchPattern", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 18, "column": 0}, "definition": "export type SearchPattern = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 180, "column": 23}, "context": "SearchPattern"}]}, {"name": "SearchQuery", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 19, "column": 0}, "definition": "export type SearchQuery = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 183, "column": 25}, "context": "SearchQuery"}]}, {"name": "TradingConfig", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 22, "column": 0}, "definition": "export interface TradingConfig {\r\n    symbol: Symbol;\r\n    amount?: number;\r\n    price?: number;\r\n    side?: 'buy' | 'sell';\r\n    type?: 'market' | 'limit';\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["Symbol"], "usages": []}, {"name": "GridBotConfig", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 32, "column": 0}, "definition": "export interface GridBotConfig {\r\n    symbol: Symbol;\r\n    gridLevels: number;\r\n    upperPrice: number;\r\n    lowerPrice: number;\r\n    investment: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["Symbol"], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 149, "column": 23}, "context": "GridBotConfig"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 152, "column": 31}, "context": "GridBotConfig"}]}, {"name": "TradeParams", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 42, "column": 0}, "definition": "export interface TradeParams {\r\n    symbol: Symbol;\r\n    side: 'buy' | 'sell';\r\n    amount: number;\r\n    price?: number;\r\n    type: 'market' | 'limit';\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["Symbol"], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 146, "column": 22}, "context": "TradeParams"}]}, {"name": "RiskParams", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 52, "column": 0}, "definition": "export interface RiskParams {\r\n    maxPositionSize?: number;\r\n    stopLoss?: number;\r\n    takeProfit?: number;\r\n    maxDrawdown?: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 174, "column": 44}, "context": "RiskParams"}]}, {"name": "SessionData", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 61, "column": 0}, "definition": "export interface SessionData {\r\n    startTime: number;\r\n    endTime?: number;\r\n    trades: any[];\r\n    performance: any;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 170, "column": 49}, "context": "SessionData"}]}, {"name": "ExchangeConfig", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 70, "column": 0}, "definition": "export interface ExchangeConfig {\r\n    apiKey: string;\r\n    secret: string;\r\n    sandbox?: boolean;\r\n    rateLimit?: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 172, "column": 52}, "context": "ExchangeConfig"}]}, {"name": "BotMetrics", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 79, "column": 0}, "definition": "export interface BotMetrics {\r\n    totalTrades: number;\r\n    profitLoss: number;\r\n    winRate: number;\r\n    averageReturn: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 176, "column": 45}, "context": "BotMetrics"}]}, {"name": "MarketAnalysis", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 88, "column": 0}, "definition": "export interface MarketAnalysis {\r\n    symbol: Symbol;\r\n    trend: 'bullish' | 'bearish' | 'neutral';\r\n    signals: any[];\r\n    timestamp: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["Symbol"], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 178, "column": 46}, "context": "MarketAnalysis"}]}, {"name": "AppSettings", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 97, "column": 0}, "definition": "export interface AppSettings {\r\n    api?: any;\r\n    trading?: any;\r\n    whaleTracking?: any;\r\n    performance?: any;\r\n    monitoring?: any;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 164, "column": 22}, "context": "AppSettings"}]}, {"name": "CoinData", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 107, "column": 0}, "definition": "export interface CoinData {\r\n    id?: string;\r\n    symbol: Symbol;\r\n    name: string;\r\n    price?: number;\r\n    marketCap?: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["Symbol"], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 167, "column": 18}, "context": "CoinData"}]}, {"name": "SearchOptions", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 117, "column": 0}, "definition": "export interface SearchOptions {\r\n    includeContent?: boolean;\r\n    fileTypes?: string[];\r\n    maxResults?: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 183, "column": 38}, "context": "SearchOptions"}]}, {"name": "StatusFilter", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 125, "column": 0}, "definition": "export interface StatusFilter {\r\n    level?: 'info' | 'warning' | 'error';\r\n    component?: ComponentName;\r\n    timeRange?: {\r\n        start: number;\r\n        end: number;\r\n    };\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["ComponentName"], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 191, "column": 36}, "context": "StatusFilter"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 137, "column": 0}, "definition": "export type IpcHandler<T = any, R = any> = (event: IpcMainInvokeEvent, ...args: T[]) => Promise<R> | R;", "exports": true, "dependencies": ["IpcMainInvokeEvent", "T", "Promise", "R"], "usages": []}, {"name": "IpcHandlerParams", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 140, "column": 0}, "definition": "export interface IpcHandlerParams {\r\n    // Trading endpoints\r\n    'get-open-orders': [Symbol?];\r\n    'get-order-history': [Symbol?];\r\n    'cancel-order': [string, Symbol];\r\n    'cancel-all-orders': [Symbol];\r\n    'execute-trade': [TradeParams];\r\n\r\n    // Grid bot endpoints\r\n    'start-grid-bot': [GridBotConfig];\r\n    'stop-grid-bot': [Symbol];\r\n    'get-grid-bot-history': [Symbol];\r\n    'update-grid-bot-config': [GridBotConfig];\r\n\r\n    // Market data endpoints\r\n    'get-market-data': [Symbol, Timeframe];\r\n    'get-sentiment-data': [Symbol];\r\n    'get-price-history': [Symbol, Timeframe, number?, number?];\r\n\r\n    // Health monitoring endpoints\r\n    'get-component-health': [ComponentName];\r\n    'run-health-check': [ComponentName?];\r\n\r\n    // Settings endpoints\r\n    'save-settings': [AppSettings];\r\n\r\n    // Coin management endpoints\r\n    'save-coin': [CoinData];\r\n\r\n    // Context engine endpoints\r\n    'context-store-trading-session': [SessionId, SessionData];\r\n    'context-get-trading-session': [SessionId];\r\n    'context-store-exchange-config': [ExchangeName, ExchangeConfig];\r\n    'context-get-exchange-config': [ExchangeName];\r\n    'context-store-position-risk': [Symbol, RiskParams];\r\n    'context-get-position-risk': [Symbol];\r\n    'context-store-bot-performance': [BotId, BotMetrics];\r\n    'context-get-bot-performance': [BotId];\r\n    'context-store-market-analysis': [Symbol, MarketAnalysis];\r\n    'context-get-market-analysis': [Symbol];\r\n    'context-search': [SearchPattern];\r\n\r\n    // Workspace search endpoints\r\n    'workspace-search': [SearchQuery, SearchOptions?];\r\n    'workspace-get-files-by-type': [FileType];\r\n    'workspace-get-files-by-extension': [FileExtension];\r\n    'workspace-get-recent-files': [number?];\r\n    'workspace-get-file': [FilePath];\r\n    'workspace-get-file-dependencies': [FilePath];\r\n\r\n    // Status reporting endpoints\r\n    'get-status-reports': [number?, StatusFilter?];\r\n}", "exports": true, "dependencies": ["Symbol", "TradeParams", "GridBotConfig", "Timeframe", "ComponentName", "AppSettings", "CoinData", "SessionId", "SessionData", "ExchangeName", "ExchangeConfig", "RiskParams", "BotId", "BotMetrics", "MarketAnalysis", "SearchPattern", "SearchQuery", "SearchOptions", "FileType", "FileExtension", "FilePath", "StatusFilter"], "usages": []}, {"name": "IpcResponse", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 195, "column": 0}, "definition": "export interface IpcResponse<T = any> {\r\n    success: boolean;\r\n    data?: T;\r\n    error?: string;\r\n}", "exports": true, "dependencies": ["T"], "usages": []}, {"name": "SystemStatus", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 201, "column": 0}, "definition": "export interface SystemStatus {\r\n    isRunning: boolean;\r\n    isInitialized: boolean;\r\n    health: 'healthy' | 'warning' | 'error' | 'unknown';\r\n    message?: string;\r\n    timestamp: number;\r\n    uptime?: number;\r\n}", "exports": true, "dependencies": [], "usages": []}, {"name": "SystemMetrics", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 210, "column": 0}, "definition": "export interface SystemMetrics {\r\n    performance: any;\r\n    health: string;\r\n    uptime: number;\r\n    activeSignals: number;\r\n    pendingTrades: number;\r\n    lastUpdate: number;\r\n    systemLoad: {\r\n        activeBots: number;\r\n        dataCollectionActive: boolean;\r\n        analysisActive: boolean;\r\n    };\r\n}", "exports": true, "dependencies": [], "usages": []}], "interfaces": [{"name": "IPCResponse", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 6, "column": 0}, "definition": "export interface IPCResponse<T = any> {\r\n    success: boolean;\r\n    data?: T;\r\n    error?: string;\r\n}", "exports": true, "dependencies": ["T"], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 68, "column": 28}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 69, "column": 27}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 70, "column": 32}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 75, "column": 37}, "context": "IPCResponse<RealTimeStatusData>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 76, "column": 35}, "context": "IPCResponse<SystemHealthData>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 77, "column": 59}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 78, "column": 36}, "context": "IPCResponse<SystemMetricsData>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 79, "column": 33}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 80, "column": 35}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 85, "column": 41}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 86, "column": 40}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 87, "column": 74}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 88, "column": 43}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 89, "column": 56}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 90, "column": 31}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 95, "column": 39}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 96, "column": 41}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 97, "column": 35}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 98, "column": 49}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 99, "column": 34}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 100, "column": 38}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 105, "column": 67}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 106, "column": 37}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 107, "column": 69}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 108, "column": 51}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 113, "column": 35}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 114, "column": 36}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 115, "column": 42}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 120, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 121, "column": 40}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 122, "column": 39}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 127, "column": 31}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 128, "column": 45}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 133, "column": 37}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 138, "column": 102}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 139, "column": 84}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 140, "column": 91}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 145, "column": 18}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 150, "column": 59}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 151, "column": 46}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 152, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 153, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 154, "column": 38}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 155, "column": 43}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 156, "column": 42}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 157, "column": 45}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 158, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 159, "column": 66}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 164, "column": 33}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 165, "column": 33}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 170, "column": 57}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 171, "column": 29}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 172, "column": 30}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 173, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 174, "column": 56}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 179, "column": 40}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 180, "column": 42}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 181, "column": 36}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 182, "column": 49}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 183, "column": 48}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 184, "column": 34}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 185, "column": 45}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 190, "column": 48}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 191, "column": 49}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 192, "column": 46}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 193, "column": 35}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 194, "column": 46}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 195, "column": 47}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 196, "column": 49}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 201, "column": 55}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 202, "column": 49}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 203, "column": 52}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 204, "column": 52}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 209, "column": 36}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 210, "column": 38}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 211, "column": 50}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 216, "column": 48}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 217, "column": 37}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 222, "column": 32}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 223, "column": 42}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 224, "column": 64}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 225, "column": 72}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 226, "column": 69}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 231, "column": 28}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 232, "column": 37}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 233, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 234, "column": 70}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 239, "column": 36}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 240, "column": 49}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 241, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 242, "column": 44}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 243, "column": 68}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 244, "column": 47}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 245, "column": 62}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 250, "column": 39}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 251, "column": 40}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 252, "column": 35}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 253, "column": 58}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 254, "column": 73}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 259, "column": 61}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 260, "column": 39}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 265, "column": 41}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 266, "column": 38}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 267, "column": 40}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 268, "column": 39}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 269, "column": 52}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 274, "column": 46}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 275, "column": 44}, "context": "IPCResponse<any[]>"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 276, "column": 43}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 277, "column": 42}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 278, "column": 61}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 283, "column": 32}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 288, "column": 33}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 289, "column": 34}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 290, "column": 47}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 303, "column": 36}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 304, "column": 38}, "context": "IPCResponse"}, {"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 305, "column": 39}, "context": "IPCResponse"}]}, {"name": "SystemHealthData", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 12, "column": 0}, "definition": "export interface SystemHealthData {\r\n    status: string;\r\n    uptime: number;\r\n    cpu: number;\r\n    memory: number;\r\n    components?: Record<string, any>;\r\n}", "exports": true, "dependencies": ["Record"], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 76, "column": 47}, "context": "SystemHealthData"}]}, {"name": "RealTimeStatusData", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 20, "column": 0}, "definition": "export interface RealTimeStatusData {\r\n    isRunning: boolean;\r\n    isInitialized: boolean;\r\n    health: string;\r\n    message?: string;\r\n    timestamp: number;\r\n    uptime?: number;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 75, "column": 49}, "context": "RealTimeStatusData"}]}, {"name": "SystemMetricsData", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 29, "column": 0}, "definition": "export interface SystemMetricsData {\r\n    performance: any;\r\n    health: string;\r\n    uptime: number;\r\n    activeSignals: number;\r\n    pendingTrades: number;\r\n    lastUpdate: number;\r\n    systemLoad: {\r\n        activeBots: number;\r\n        dataCollectionActive: boolean;\r\n        analysisActive: boolean;\r\n    };\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 78, "column": 48}, "context": "SystemMetricsData"}]}, {"name": "ElectronAPI", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 43, "column": 0}, "definition": "export interface ElectronAPI {\r\n    stopAllGrids(): unknown;\r\n    startAutonomousTrading(): unknown;\r\n    stopAutonomousTrading(): unknown;\r\n    getAutonomousStatus(): unknown;\r\n    checkDatabaseReady(): unknown;\r\n    getCrossExchangeBalance(): unknown;\r\n    updateGridBotConfig(config: any): unknown;\r\n    generateDatabaseHealthReport(): unknown;\r\n    getDatabaseStatistics(): unknown;\r\n    storeTradingTransaction(transaction: any): unknown;\r\n    getTradingTransactions(filters: any): unknown;\r\n    updatePortfolioPosition(position: any): unknown;\r\n    getPortfolioPositions(): unknown;\r\n    storePerformanceMetrics(metrics: any): unknown;\r\n    storeWhaleWallet(wallet: any): unknown;\r\n    storeWhaleTransaction(transaction: any): unknown;\r\n    getWhaleWallets(filters: any): unknown;\r\n    storeTradingSignal(signal: any): unknown;\r\n    getTradingSignals(filters: any): unknown;\r\n    getIPCErrorStatistics(): unknown;\r\n    resetIPCErrorStatistics(): unknown;\r\n    // ========================================================================================\r\n    // Core Bot Control Methods\r\n    // ========================================================================================\r\n    startBot: () => Promise<IPCResponse>;\r\n    stopBot: () => Promise<IPCResponse>;\r\n    getBotStatus: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Real-time Status and Health Methods\r\n    // ========================================================================================\r\n    getRealTimeStatus: () => Promise<IPCResponse<RealTimeStatusData>>;\r\n    getSystemHealth: () => Promise<IPCResponse<SystemHealthData>>;\r\n    getComponentHealth: (componentName: string) => Promise<IPCResponse>;\r\n    getSystemMetrics: () => Promise<IPCResponse<SystemMetricsData>>;\r\n    getActiveBots: () => Promise<IPCResponse<any[]>>;\r\n    getSystemAlerts: () => Promise<IPCResponse<any[]>>;\r\n\r\n    // ========================================================================================\r\n    // Health Monitoring Methods\r\n    // ========================================================================================\r\n    startHealthMonitoring: () => Promise<IPCResponse>;\r\n    stopHealthMonitoring: () => Promise<IPCResponse>;\r\n    getStatusReports: (params: { limit: number; filter: any }) => Promise<IPCResponse<any[]>>;\r\n    getMonitoringStatistics: () => Promise<IPCResponse>;\r\n    runHealthCheck: (componentName?: string) => Promise<IPCResponse>;\r\n    healthCheck: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Trading and Portfolio Methods\r\n    // ========================================================================================\r\n    getPortfolioSummary: () => Promise<IPCResponse>;\r\n    getPerformanceMetrics: () => Promise<IPCResponse>;\r\n    getTradingStats: () => Promise<IPCResponse>;\r\n    getTradeHistory: (limit?: number) => Promise<IPCResponse<any[]>>;\r\n    getRiskMetrics: () => Promise<IPCResponse>;\r\n    getAssetAllocation: () => Promise<IPCResponse<any[]>>;\r\n\r\n    // ========================================================================================\r\n    // Market Data Methods\r\n    // ========================================================================================\r\n    getMarketData: (symbol: string, timeframe?: string) => Promise<IPCResponse>;\r\n    getMarketOverview: () => Promise<IPCResponse>;\r\n    getPriceHistory: (symbol: string, timeframe?: string) => Promise<IPCResponse<any[]>>;\r\n    getSentimentData?: (symbol: string) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Whale Tracking Methods\r\n    // ========================================================================================\r\n    getWhaleSignals: () => Promise<IPCResponse>;\r\n    getTrackedWhales: () => Promise<IPCResponse>;\r\n    getWhaleTrackingStatus: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Meme Coin Scanner Methods\r\n    // ========================================================================================\r\n    getMemeCoinOpportunities: () => Promise<IPCResponse>;\r\n    startMemeCoinScanner: () => Promise<IPCResponse>;\r\n    stopMemeCoinScanner: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Settings and Configuration Methods\r\n    // ========================================================================================\r\n    getSettings: () => Promise<IPCResponse>;\r\n    saveSettings: (settings: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Trading System Initialization\r\n    // ========================================================================================\r\n    initializeTrading: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Error Recovery Methods (Optional - may not be implemented)\r\n    // ========================================================================================\r\n    restartComponent?: (params: { componentName: string; strategy: string; context: any }) => Promise<IPCResponse>;\r\n    isolateComponent?: (params: { componentName: string; context: any }) => Promise<IPCResponse>;\r\n    recoverTradingSystem?: (params: { component?: string; errorType?: string }) => Promise<IPCResponse>;\r\n    triggerEmergencyProtocols?: (params: {\r\n        componentName: string;\r\n        error: string;\r\n        context: any\r\n    }) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Legacy/Compatibility Methods\r\n    // ========================================================================================\r\n    getPerformanceHistory?: (timeRange: string) => Promise<IPCResponse<any[]>>;\r\n    getArbitrageOpportunities?: () => Promise<IPCResponse<any[]>>;\r\n    getPortfolioRiskMetrics?: () => Promise<IPCResponse>;\r\n    getPortfolioPerformance?: () => Promise<IPCResponse>;\r\n    getArbitrageStats?: () => Promise<IPCResponse>;\r\n    startArbitrageScanning?: () => Promise<IPCResponse>;\r\n    stopArbitrageScanning?: () => Promise<IPCResponse>;\r\n    startPortfolioMonitoring?: () => Promise<IPCResponse>;\r\n    stopPortfolioMonitoring?: () => Promise<IPCResponse>;\r\n    executeArbitrage?: (payload: { opportunity: any }) => Promise<IPCResponse & { actualProfit?: number }>;\r\n\r\n    // ========================================================================================\r\n    // System Information Methods\r\n    // ========================================================================================\r\n    getSystemInfo: () => Promise<IPCResponse>;\r\n    getAppVersion: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Logging Methods\r\n    // ========================================================================================\r\n    getLogs: (level?: string, limit?: number) => Promise<IPCResponse<any[]>>;\r\n    clearLogs: () => Promise<IPCResponse>;\r\n    exportLogs: () => Promise<IPCResponse>;\r\n    setLogLevel: (level: string) => Promise<IPCResponse>;\r\n    reportError: (error: any, context?: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Grid Trading Methods\r\n    // ========================================================================================\r\n    startGrid: (config: any) => Promise<IPCResponse>;\r\n    stopGrid: (gridId: string) => Promise<IPCResponse>;\r\n    getGridPositions: () => Promise<IPCResponse<any[]>>;\r\n    getGridHistory: (gridId?: string) => Promise<IPCResponse<any[]>>;\r\n    updateGridConfig?: (config: any) => Promise<IPCResponse>;\r\n    getGridPresets: () => Promise<IPCResponse<any[]>>;\r\n    saveGridPreset: (preset: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Order Management Methods\r\n    // ========================================================================================\r\n    getOpenOrders: (symbol?: string) => Promise<IPCResponse<any[]>>;\r\n    getOrderHistory: (limit?: number) => Promise<IPCResponse<any[]>>;\r\n    cancelOrder: (orderId: string) => Promise<IPCResponse>;\r\n    cancelAllOrders: () => Promise<IPCResponse>;\r\n    placeLimitOrder: (params: any) => Promise<IPCResponse>;\r\n    placeMarketOrder: (params: any) => Promise<IPCResponse>;\r\n    executeTrade?: (tradeParams: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Whale Tracking Extended Methods\r\n    // ========================================================================================\r\n    toggleWhaleTracking: (enabled: boolean) => Promise<IPCResponse>;\r\n    addWhaleWallet: (address: string) => Promise<IPCResponse>;\r\n    removeWhaleWallet: (address: string) => Promise<IPCResponse>;\r\n    getWhaleHistory: (timeframe: string) => Promise<IPCResponse<any[]>>;\r\n\r\n    // ========================================================================================\r\n    // Meme Coin Scanner Extended Methods\r\n    // ========================================================================================\r\n    getScannerStatus: () => Promise<IPCResponse>;\r\n    getMemeCoinHistory: () => Promise<IPCResponse<any[]>>;\r\n    updateScannerConfig: (config: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Risk Management Methods\r\n    // ========================================================================================\r\n    setRiskParameters: (params: any) => Promise<IPCResponse>;\r\n    getRiskParameters: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Exchange Management Methods\r\n    // ========================================================================================\r\n    getExchanges: () => Promise<IPCResponse<any[]>>;\r\n    addExchange: (config: any) => Promise<IPCResponse>;\r\n    removeExchange: (params: { exchangeId: string }) => Promise<IPCResponse>;\r\n    testExchangeConnection: (params: { exchangeId: string }) => Promise<IPCResponse>;\r\n    getExchangeBalances: (params: { exchangeId: string }) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Coin Management Methods\r\n    // ========================================================================================\r\n    getCoins: () => Promise<IPCResponse<any[]>>;\r\n    saveCoin: (coin: any) => Promise<IPCResponse>;\r\n    deleteCoin: (coinId: string) => Promise<IPCResponse>;\r\n    updateCoin: (params: { coinId: string; updates: any }) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Portfolio Extended Methods\r\n    // ========================================================================================\r\n    getWalletBalance: () => Promise<IPCResponse>;\r\n    rebalancePortfolio: (target: any) => Promise<IPCResponse>;\r\n    getPortfolioOptimization: () => Promise<IPCResponse>;\r\n    getCrossExchangeBalances: () => Promise<IPCResponse>;\r\n    getExchangePortfolio: (params: { exchange: string }) => Promise<IPCResponse>;\r\n    getRebalancingOpportunities: () => Promise<IPCResponse<any[]>>;\r\n    rebalanceCrossExchangePortfolio: (config: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // DCA Trading Methods\r\n    // ========================================================================================\r\n    startDCA: (config: any) => Promise<IPCResponse>;\r\n    stopDCA: (dcaId: string) => Promise<IPCResponse>;\r\n    getDCAPositions: () => Promise<IPCResponse<any[]>>;\r\n    getDCAHistory: (params: { dcaId: string }) => Promise<IPCResponse<any[]>>;\r\n    updateDCAConfig: (params: { dcaId: string; config: any }) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Analytics Extended Methods\r\n    // ========================================================================================\r\n    getPnLReport: (params: { timeframe: string }) => Promise<IPCResponse>;\r\n    getDrawdownAnalysis: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Arbitrage Engine Methods\r\n    // ========================================================================================\r\n    getArbitragePositions: () => Promise<IPCResponse<any[]>>;\r\n    getArbitrageStatus: () => Promise<IPCResponse>;\r\n    startArbitrageEngine: () => Promise<IPCResponse>;\r\n    stopArbitrageEngine: () => Promise<IPCResponse>;\r\n    updateArbitrageConfig: (config: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Opportunity Scanner Methods\r\n    // ========================================================================================\r\n    getOpportunityScannerStats: () => Promise<IPCResponse>;\r\n    getDetectedOpportunities: () => Promise<IPCResponse<any[]>>;\r\n    startOpportunityScanner: () => Promise<IPCResponse>;\r\n    stopOpportunityScanner: () => Promise<IPCResponse>;\r\n    updateOpportunityScannerConfig: (config: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Backup & Recovery Methods\r\n    // ========================================================================================\r\n    createBackup: () => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Settings Extended Methods\r\n    // ========================================================================================\r\n    resetSettings: () => Promise<IPCResponse>;\r\n    exportSettings: () => Promise<IPCResponse>;\r\n    importSettings: (settings: any) => Promise<IPCResponse>;\r\n\r\n    // ========================================================================================\r\n    // Additional Methods Referenced in Components\r\n    // ========================================================================================\r\n    invoke?: (channel: string, ...args: any[]) => Promise<any>;\r\n    on?: (channel: string, callback: (event: any, ...args: any[]) => void) => () => void;\r\n    onArbitrageOpportunity?: (callback: (data: any) => void) => () => void;\r\n    onArbitrageExecuted?: (callback: (data: any) => void) => () => void;\r\n\r\n    // ========================================================================================\r\n    // System Status Methods\r\n    // ========================================================================================\r\n    getSystemStatus?: () => Promise<IPCResponse>;\r\n    getDatabaseStatus?: () => Promise<IPCResponse>;\r\n    getDatabaseMetrics?: () => Promise<IPCResponse>;\r\n}", "exports": true, "dependencies": ["Promise", "IPCResponse", "RealTimeStatusData", "SystemHealthData", "SystemMetricsData"], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 310, "column": 21}, "context": "ElectronAPI"}, {"file": "app\\src\\types\\electron.d.ts", "location": {"line": 17, "column": 22}, "context": "ElectronAPI"}]}, {"name": "Window", "kind": "interface", "file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 309, "column": 4}, "definition": "interface Window {\r\n        electronAPI: ElectronAPI;\r\n    }", "exports": false, "dependencies": ["ElectronAPI"], "usages": []}, {"name": "ElectronAPI", "kind": "interface", "file": "app\\src\\types\\electron.d.ts", "location": {"line": 7, "column": 4}, "definition": "interface ElectronAPI {\r\n        [x: string]: any;\r\n        on?: (event: string, listener: (...args: any[]) => void) => void;\r\n        removeListener?: (event: string, listener: (...args: any[]) => void) => void;\r\n        emit?: (event: string, ...args: any[]) => void;\r\n        getMarketData?: (symbol?: string) => Promise<{ success: boolean; data: any[] }>;\r\n        fetchPriceHistory?: (symbol?: string, timeframe?: string) => Promise<{ success: boolean; data: any[] }>;\r\n    }", "exports": false, "dependencies": ["Promise"], "usages": [{"file": "app\\src\\types\\electron-api.d.ts", "location": {"line": 310, "column": 21}, "context": "ElectronAPI"}, {"file": "app\\src\\types\\electron.d.ts", "location": {"line": 17, "column": 22}, "context": "ElectronAPI"}]}, {"name": "Window", "kind": "interface", "file": "app\\src\\types\\electron.d.ts", "location": {"line": 16, "column": 4}, "definition": "interface Window {\r\n        electronAPI?: ElectronAPI;\r\n    }", "exports": false, "dependencies": ["ElectronAPI"], "usages": []}, {"name": "Matchers", "kind": "interface", "file": "app\\src\\__tests__\\jest.d.ts", "location": {"line": 9, "column": 8}, "definition": "interface Matchers<R> {\r\n            toBeInTheDocument(): R;\r\n\r\n            toHaveClass(className: string): R;\r\n\r\n            toHaveAttribute(attr: string, value?: string): R;\r\n\r\n            toHaveTextContent(text: string | RegExp): R;\r\n\r\n            toBeVisible(): R;\r\n\r\n            toBeDisabled(): R;\r\n\r\n            toBeEnabled(): R;\r\n\r\n            toHaveFocus(): R;\r\n\r\n            toHaveStyle(style: string | object): R;\r\n\r\n            toHaveValue(value: string | number): R;\r\n\r\n            toBeChecked(): R;\r\n\r\n            toBeInvalid(): R;\r\n\r\n            toBeValid(): R;\r\n\r\n            toBeRequired(): R;\r\n\r\n            toBeEmptyDOMElement(): R;\r\n\r\n            toContainElement(element: HTMLElement | null): R;\r\n\r\n            toContainHTML(htmlText: string): R;\r\n\r\n            toHaveDisplayValue(value: string | RegExp | (string | RegExp)[]): R;\r\n\r\n            toHaveFormValues(expectedValues: Record<string, any>): R;\r\n\r\n            toHaveErrorMessage(text: string | RegExp): R;\r\n\r\n            toHaveDescription(text: string | RegExp): R;\r\n\r\n            toHaveAccessibleName(name: string | RegExp): R;\r\n\r\n            toHaveAccessibleDescription(description: string | RegExp): R;\r\n        }", "exports": false, "dependencies": ["R", "RegExp", "HTMLElement", "Record"], "usages": []}, {"name": "TradingConfig", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 22, "column": 0}, "definition": "export interface TradingConfig {\r\n    symbol: Symbol;\r\n    amount?: number;\r\n    price?: number;\r\n    side?: 'buy' | 'sell';\r\n    type?: 'market' | 'limit';\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["Symbol"], "usages": []}, {"name": "GridBotConfig", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 32, "column": 0}, "definition": "export interface GridBotConfig {\r\n    symbol: Symbol;\r\n    gridLevels: number;\r\n    upperPrice: number;\r\n    lowerPrice: number;\r\n    investment: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["Symbol"], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 149, "column": 23}, "context": "GridBotConfig"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 152, "column": 31}, "context": "GridBotConfig"}]}, {"name": "TradeParams", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 42, "column": 0}, "definition": "export interface TradeParams {\r\n    symbol: Symbol;\r\n    side: 'buy' | 'sell';\r\n    amount: number;\r\n    price?: number;\r\n    type: 'market' | 'limit';\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["Symbol"], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 146, "column": 22}, "context": "TradeParams"}]}, {"name": "RiskParams", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 52, "column": 0}, "definition": "export interface RiskParams {\r\n    maxPositionSize?: number;\r\n    stopLoss?: number;\r\n    takeProfit?: number;\r\n    maxDrawdown?: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 174, "column": 44}, "context": "RiskParams"}]}, {"name": "SessionData", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 61, "column": 0}, "definition": "export interface SessionData {\r\n    startTime: number;\r\n    endTime?: number;\r\n    trades: any[];\r\n    performance: any;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 170, "column": 49}, "context": "SessionData"}]}, {"name": "ExchangeConfig", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 70, "column": 0}, "definition": "export interface ExchangeConfig {\r\n    apiKey: string;\r\n    secret: string;\r\n    sandbox?: boolean;\r\n    rateLimit?: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 172, "column": 52}, "context": "ExchangeConfig"}]}, {"name": "BotMetrics", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 79, "column": 0}, "definition": "export interface BotMetrics {\r\n    totalTrades: number;\r\n    profitLoss: number;\r\n    winRate: number;\r\n    averageReturn: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 176, "column": 45}, "context": "BotMetrics"}]}, {"name": "MarketAnalysis", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 88, "column": 0}, "definition": "export interface MarketAnalysis {\r\n    symbol: Symbol;\r\n    trend: 'bullish' | 'bearish' | 'neutral';\r\n    signals: any[];\r\n    timestamp: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["Symbol"], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 178, "column": 46}, "context": "MarketAnalysis"}]}, {"name": "AppSettings", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 97, "column": 0}, "definition": "export interface AppSettings {\r\n    api?: any;\r\n    trading?: any;\r\n    whaleTracking?: any;\r\n    performance?: any;\r\n    monitoring?: any;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 164, "column": 22}, "context": "AppSettings"}]}, {"name": "CoinData", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 107, "column": 0}, "definition": "export interface CoinData {\r\n    id?: string;\r\n    symbol: Symbol;\r\n    name: string;\r\n    price?: number;\r\n    marketCap?: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["Symbol"], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 167, "column": 18}, "context": "CoinData"}]}, {"name": "SearchOptions", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 117, "column": 0}, "definition": "export interface SearchOptions {\r\n    includeContent?: boolean;\r\n    fileTypes?: string[];\r\n    maxResults?: number;\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 183, "column": 38}, "context": "SearchOptions"}]}, {"name": "StatusFilter", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 125, "column": 0}, "definition": "export interface StatusFilter {\r\n    level?: 'info' | 'warning' | 'error';\r\n    component?: ComponentName;\r\n    timeRange?: {\r\n        start: number;\r\n        end: number;\r\n    };\r\n\r\n    [key: string]: any;\r\n}", "exports": true, "dependencies": ["ComponentName"], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 191, "column": 36}, "context": "StatusFilter"}]}, {"name": "IpcHandlerParams", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 140, "column": 0}, "definition": "export interface IpcHandlerParams {\r\n    // Trading endpoints\r\n    'get-open-orders': [Symbol?];\r\n    'get-order-history': [Symbol?];\r\n    'cancel-order': [string, Symbol];\r\n    'cancel-all-orders': [Symbol];\r\n    'execute-trade': [TradeParams];\r\n\r\n    // Grid bot endpoints\r\n    'start-grid-bot': [GridBotConfig];\r\n    'stop-grid-bot': [Symbol];\r\n    'get-grid-bot-history': [Symbol];\r\n    'update-grid-bot-config': [GridBotConfig];\r\n\r\n    // Market data endpoints\r\n    'get-market-data': [Symbol, Timeframe];\r\n    'get-sentiment-data': [Symbol];\r\n    'get-price-history': [Symbol, Timeframe, number?, number?];\r\n\r\n    // Health monitoring endpoints\r\n    'get-component-health': [ComponentName];\r\n    'run-health-check': [ComponentName?];\r\n\r\n    // Settings endpoints\r\n    'save-settings': [AppSettings];\r\n\r\n    // Coin management endpoints\r\n    'save-coin': [CoinData];\r\n\r\n    // Context engine endpoints\r\n    'context-store-trading-session': [SessionId, SessionData];\r\n    'context-get-trading-session': [SessionId];\r\n    'context-store-exchange-config': [ExchangeName, ExchangeConfig];\r\n    'context-get-exchange-config': [ExchangeName];\r\n    'context-store-position-risk': [Symbol, RiskParams];\r\n    'context-get-position-risk': [Symbol];\r\n    'context-store-bot-performance': [BotId, BotMetrics];\r\n    'context-get-bot-performance': [BotId];\r\n    'context-store-market-analysis': [Symbol, MarketAnalysis];\r\n    'context-get-market-analysis': [Symbol];\r\n    'context-search': [SearchPattern];\r\n\r\n    // Workspace search endpoints\r\n    'workspace-search': [SearchQuery, SearchOptions?];\r\n    'workspace-get-files-by-type': [FileType];\r\n    'workspace-get-files-by-extension': [FileExtension];\r\n    'workspace-get-recent-files': [number?];\r\n    'workspace-get-file': [FilePath];\r\n    'workspace-get-file-dependencies': [FilePath];\r\n\r\n    // Status reporting endpoints\r\n    'get-status-reports': [number?, StatusFilter?];\r\n}", "exports": true, "dependencies": ["Symbol", "TradeParams", "GridBotConfig", "Timeframe", "ComponentName", "AppSettings", "CoinData", "SessionId", "SessionData", "ExchangeName", "ExchangeConfig", "RiskParams", "BotId", "BotMetrics", "MarketAnalysis", "SearchPattern", "SearchQuery", "SearchOptions", "FileType", "FileExtension", "FilePath", "StatusFilter"], "usages": []}, {"name": "IpcResponse", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 195, "column": 0}, "definition": "export interface IpcResponse<T = any> {\r\n    success: boolean;\r\n    data?: T;\r\n    error?: string;\r\n}", "exports": true, "dependencies": ["T"], "usages": []}, {"name": "SystemStatus", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 201, "column": 0}, "definition": "export interface SystemStatus {\r\n    isRunning: boolean;\r\n    isInitialized: boolean;\r\n    health: 'healthy' | 'warning' | 'error' | 'unknown';\r\n    message?: string;\r\n    timestamp: number;\r\n    uptime?: number;\r\n}", "exports": true, "dependencies": [], "usages": []}, {"name": "SystemMetrics", "kind": "interface", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 210, "column": 0}, "definition": "export interface SystemMetrics {\r\n    performance: any;\r\n    health: string;\r\n    uptime: number;\r\n    activeSignals: number;\r\n    pendingTrades: number;\r\n    lastUpdate: number;\r\n    systemLoad: {\r\n        activeBots: number;\r\n        dataCollectionActive: boolean;\r\n        analysisActive: boolean;\r\n    };\r\n}", "exports": true, "dependencies": [], "usages": []}], "types": [{"name": "Symbol", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 9, "column": 0}, "definition": "export type Symbol = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 23, "column": 12}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 33, "column": 12}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 43, "column": 12}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 89, "column": 12}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 109, "column": 12}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 142, "column": 24}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 143, "column": 26}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 144, "column": 29}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 145, "column": 26}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 150, "column": 22}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 151, "column": 29}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 155, "column": 24}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 156, "column": 27}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 157, "column": 26}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 174, "column": 36}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 175, "column": 34}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 178, "column": 38}, "context": "Symbol"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 179, "column": 36}, "context": "Symbol"}]}, {"name": "BotId", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 10, "column": 0}, "definition": "export type BotId = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 176, "column": 38}, "context": "BotId"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 177, "column": 36}, "context": "BotId"}]}, {"name": "ComponentName", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 11, "column": 0}, "definition": "export type ComponentName = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 127, "column": 16}, "context": "ComponentName"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 160, "column": 29}, "context": "ComponentName"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 161, "column": 25}, "context": "ComponentName"}]}, {"name": "ExchangeName", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 12, "column": 0}, "definition": "export type ExchangeName = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 172, "column": 38}, "context": "ExchangeName"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 173, "column": 36}, "context": "ExchangeName"}]}, {"name": "SessionId", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 13, "column": 0}, "definition": "export type SessionId = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 170, "column": 38}, "context": "SessionId"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 171, "column": 36}, "context": "SessionId"}]}, {"name": "Timeframe", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 14, "column": 0}, "definition": "export type Timeframe = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 155, "column": 32}, "context": "Timeframe"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 157, "column": 34}, "context": "Timeframe"}]}, {"name": "FilePath", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 15, "column": 0}, "definition": "export type FilePath = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 187, "column": 27}, "context": "FilePath"}, {"file": "app\\types\\ipc-types.d.ts", "location": {"line": 188, "column": 40}, "context": "FilePath"}]}, {"name": "FileExtension", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 16, "column": 0}, "definition": "export type FileExtension = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 185, "column": 41}, "context": "FileExtension"}]}, {"name": "FileType", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 17, "column": 0}, "definition": "export type FileType = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 184, "column": 36}, "context": "FileType"}]}, {"name": "SearchPattern", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 18, "column": 0}, "definition": "export type SearchPattern = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 180, "column": 23}, "context": "SearchPattern"}]}, {"name": "SearchQuery", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 19, "column": 0}, "definition": "export type SearchQuery = string;", "exports": true, "dependencies": [], "usages": [{"file": "app\\types\\ipc-types.d.ts", "location": {"line": 183, "column": 25}, "context": "SearchQuery"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": "type", "file": "app\\types\\ipc-types.d.ts", "location": {"line": 137, "column": 0}, "definition": "export type IpcHandler<T = any, R = any> = (event: IpcMainInvokeEvent, ...args: T[]) => Promise<R> | R;", "exports": true, "dependencies": ["IpcMainInvokeEvent", "T", "Promise", "R"], "usages": []}], "classes": [], "enums": [], "globalTypes": {}, "tsConfigTypes": []}