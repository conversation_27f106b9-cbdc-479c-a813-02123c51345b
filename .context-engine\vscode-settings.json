{"sonarlint.connectedMode.project": {"connectionId": "SonarLint-Visual Studio Code 8", "projectKey": "xphoenix1996_electron-app"}, "python-envs.pythonProjects": [{"path": "", "envManager": "ms-python.python:venv", "packageManager": "ms-python.python:pip"}], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.format.enable": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": true, "files.eol": "\n", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true}}