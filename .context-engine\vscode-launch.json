{"version": "0.2.0", "configurations": [{"name": "Debug Node.js with <PERSON><PERSON><PERSON>", "type": "node", "request": "launch", "program": "${workspaceFolder}/app/main.js", "console": "integratedTerminal", "restart": true, "runtimeExecutable": "node", "skipFiles": ["<node_internals>/**", "node_modules/**"], "env": {"NODE_ENV": "development"}, "internalConsoleOptions": "openOnSessionStart", "outputCapture": "std"}, {"name": "Debug Current File", "type": "node", "request": "launch", "program": "${file}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**", "node_modules/**"]}, {"name": "Attach to Process", "type": "node", "request": "attach", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}", "remoteRoot": null}]}