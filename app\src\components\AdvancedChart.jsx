import React, {useCallback, useEffect, useRef, useState} from 'react';
import PropTypes from 'prop-types';

/** @typedef {import('@mui/material').SxProps} SxProps */
/**
 * @typedef {Object} Particle
 * @property {string} id
 * @property {number} x
 * @property {number} y
 * @property {number} size
 * @property {string} color
 * @property {'up' | 'down'} direction
 */
/**
 * @typedef {Object} TooltipPayloadItem
 * @property {string} dataKey
 * @property {any} value
 * @property {string} color
 * @property {string} name
 * @property {number} [payloadValue]
 */
/**
 * @typedef {Object} ChartDataPoint
 * @property {string} name
 * @property {number} value
 * @property {number} [volume]
 * @property {number} [open]
 * @property {number} [high]
 * @property {number} [low]
 * @property {number} [close]
 * @property {string} [date]
 * @property {number} [time]
 */
import {Box, FormControl, IconButton, InputLabel, MenuItem, Select, Typography} from '@mui/material';
import {
    Area,
    Area<PERSON>hart,
    <PERSON>rush,
    CartesianGrid,
    Line,
    LineChart,
    ReferenceLine,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis
} from 'recharts';
import {Fullscreen, FullscreenExit, ShowChart, Timeline} from '@mui/icons-material';
import {motion} from 'framer-motion';

/**
 * Custom tooltip component with proper prop types
 */
/**
 * Custom tooltip component with TypeScript types
 * @param {Object} props
 * @param {boolean} [props.active]
 * @param {TooltipPayloadItem[]} [props.payload]
 * @param {string} [props.label]
 * @param {string} [props.glowColor]
 * @param {SxProps} [props.sx]
 */
const CustomTooltip = ({
                           active = false,
                           payload = [],
                           label = '',
                           glowColor = '#00eaff',
                           sx = {}
                       }) => {
    if (active && payload?.length) {
        return (
            <Box
                sx={{
                    backgroundColor: 'rgba(24, 26, 32, 0.95)',
                    border: `1px solid ${glowColor}`,
                    borderRadius: 2,
                    p: 2,
                    backdropFilter: 'blur(10px)',
                    boxShadow: `0 0 20px ${glowColor}40`,
                    ...sx
                }}
            >
                <Typography variant="body2" sx={{color: '#fff', mb: 1}}>
                    {label}
                </Typography>
                {payload.map((entry, index) => (
                    <Typography
                        key={`${entry.dataKey}-${index}`}
                        variant="body2"
                        sx={{color: entry.color || '#fff'}}
                    >
                        {`${entry.name}: ${entry.value}`}
                    </Typography>
                ))}
            </Box>
        );
    }
    return null;
};

CustomTooltip.propTypes = {
    active: PropTypes.bool,
    payload: PropTypes.array,
    label: PropTypes.string,
    glowColor: PropTypes.string,
    sx: PropTypes.object
};

/**
 * AdvancedChart component props
 * @typedef {Object} AdvancedChartProps
 * @property {ChartDataPoint[]} [data] - Array of data points for the chart
 * @property {string} [title] - Chart title
 * @property {number|string} [height] - Chart height
 * @property {string} [glowColor] - Glow color for chart elements
 * @property {boolean} [showIndicators] - Whether to show technical indicators
 * @property {boolean} [showVolume] - Whether to show volume data
 * @property {SxProps} [sx] - Additional styles
 */

/**
 * AdvancedChart - A reusable chart component with advanced features
 * @param {AdvancedChartProps} props - Component props
 */
const AdvancedChart = ({
                           data = [],
                           title = 'Advanced Chart',
                           height = 400,
                           glowColor = '#00eaff',
                           showIndicators = true,
                           // showVolume is currently unused, so it's omitted to avoid unused variable errors
                           sx = {}
                       }) => {
    /** @type {[Particle[], React.Dispatch<React.SetStateAction<Particle[]>>]} */
    /** @type {[Particle[], React.Dispatch<React.SetStateAction<Particle[]>>]} */
    const [particles, setParticles] = useState(
        /** @type {Particle[]} */([]),
    );
    const [isFullscreen, setIsFullscreen] = useState(false);
    const chartRef = useRef(/** @type {null | HTMLDivElement} */(null));
    // Remove a particle by its ID
    /**
     * @param {string} particleId
     */
    const removeParticle = useCallback(
        /** @param {string} particleId */
        (particleId) => {
            setParticles(prevParticles => prevParticles.filter(p => p.id !== particleId));
        },
        [],
    );

    // Add a particle effect
    /**
     * @param {number} x
     * @param {number} y
     * @param {boolean} isIncrease
     */
    const addParticle = useCallback(
        /** @param {number} x @param {number} y @param {boolean} isIncrease */
        (x, y, isIncrease) => {
            if (!chartRef.current) {
                return;
            }
            const particleId = Math.random().toString(36).slice(2, 11);
            /** @type {Particle} */
            const newParticle = {
                id: particleId,
                x: x - 5,
                y: y - 5,
                size: 10,
                color: isIncrease ? '#4caf50' : '#f44336',
                direction: isIncrease ? 'up' : 'down'
            };
            setParticles(prevParticles => [...prevParticles, newParticle]);
            setTimeout(() => {
                removeParticle(particleId);
            }, 1000);
        },
        [removeParticle],
    );

    // Add particle effect when data changes
    useEffect(() => {
        if (data.length > 1) {
            const lastDataPoint = data[data.length - 1];
            const secondLastDataPoint = data[data.length - 2];
            if (chartRef.current) {
                const chartRect = chartRef.current.getBoundingClientRect();
                const isIncrease = lastDataPoint.value > secondLastDataPoint.value;
                addParticle(chartRect.width - 50, chartRect.height / 2, isIncrease);
            }
        }
    }, [data, addParticle]);

    // Toggle fullscreen mode
    const toggleFullscreen = () => {
        setIsFullscreen(prev => !prev);
    };

    // Calculate support and resistance levels
    const calculateLevels = () => {
        if (!data || data.length === 0) return {support: 0, resistance: 0};
        const values = data.map((d) => d.value);
        const max = Math.max(...values);
        const min = Math.min(...values);
        return {
            support: min + (max - min) * 0.2,
            resistance: min + (max - min) * 0.8
        };
    };

    const levels = calculateLevels();

    // Chart type state
    const [chartType, setChartType] = useState('area');

    // Render chart based on selected type
    const renderChart = () => {
        const commonProps = {
            data,
            margin: {top: 20, right: 30, left: 20, bottom: 20}
        };

        switch (chartType) {
            case 'line':
                return (
                    <LineChart {...commonProps}>
                        <defs>
                            <linearGradient id="lineGlow" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor={glowColor} stopOpacity={0.8}/>
                                <stop offset="95%" stopColor={glowColor} stopOpacity={0.1}/>
                            </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)"/>
                        <XAxis
                            dataKey="name"
                            stroke="#888"
                            fontSize={12}
                        />
                        <YAxis
                            stroke="#888"
                            fontSize={12}
                        />
                        <Tooltip
                            content={<CustomTooltip glowColor={glowColor}/>}
                        />
                        <Line
                            type="monotone"
                            dataKey="value"
                            stroke={glowColor}
                            strokeWidth={2}
                            dot={{fill: glowColor, strokeWidth: 2, r: 4}}
                            activeDot={{
                                r: 6,
                                stroke: glowColor,
                                strokeWidth: 2,
                                fill: '#fff'
                            }}
                        />
                        {showIndicators && (
                            <>
                                <ReferenceLine
                                    y={levels.resistance}
                                    stroke="#ff6b6b"
                                    strokeDasharray="5 5"
                                    label={{value: 'Resistance', position: 'insideTopRight'}}
                                />
                                <ReferenceLine
                                    y={levels.support}
                                    stroke="#4ecdc4"
                                    strokeDasharray="5 5"
                                    label={{value: 'Support', position: 'insideBottomRight'}}
                                />
                            </>
                        )}
                        <Brush
                            dataKey="name"
                            height={30}
                            stroke={glowColor}
                            fill="rgba(0,234,255,0.1)"
                        />
                    </LineChart>
                );
            case 'area':
            default:
                return (
                    <AreaChart {...commonProps}>
                        <defs>
                            <linearGradient id="colorGlow" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor={glowColor} stopOpacity={0.8}/>
                                <stop offset="95%" stopColor={glowColor} stopOpacity={0.1}/>
                            </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)"/>
                        <XAxis
                            dataKey="name"
                            stroke="#888"
                            fontSize={12}
                        />
                        <YAxis
                            stroke="#888"
                            fontSize={12}
                        />
                        <Tooltip
                            content={<CustomTooltip glowColor={glowColor}/>}
                        />
                        <Area
                            type="monotone"
                            dataKey="value"
                            stroke={glowColor}
                            fillOpacity={1}
                            fill="url(#colorGlow)"
                            strokeWidth={2}
                        />
                        {showIndicators && (
                            <>
                                <ReferenceLine
                                    y={levels.resistance}
                                    stroke="#ff6b6b"
                                    strokeDasharray="5 5"
                                    label={{value: 'Resistance', position: 'insideTopRight'}}
                                />
                                <ReferenceLine
                                    y={levels.support}
                                    stroke="#4ecdc4"
                                    strokeDasharray="5 5"
                                    label={{value: 'Support', position: 'insideBottomRight'}}
                                />
                            </>
                        )}
                        <Brush
                            dataKey="name"
                            height={30}
                            stroke={glowColor}
                            fill="rgba(0,234,255,0.1)"
                        />
                    </AreaChart>
                );
        }
    };

    return (
        <Box sx={{height, width: '100%', position: 'relative', ...sx}}>
            {/* Chart Header */}
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    p: 2,
                    borderBottom: `1px solid ${glowColor}20`
                }}
            >
                <Typography
                    variant="h6"
                    sx={{
                        color: glowColor,
                        fontWeight: 600,
                        textShadow: `0 0 10px ${glowColor}40`
                    }}
                >
                    {title}
                </Typography>

                <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                    <FormControl size="small" sx={{minWidth: 120}}>
                        <InputLabel sx={{color: '#888'}}>Chart Type</InputLabel>
                        <Select
                            value={chartType}
                            onChange={(e) => setChartType(e.target.value)}
                            sx={{
                                color: '#fff',
                                '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: `${glowColor}40`
                                },
                                '&:hover .MuiOutlinedInput-notchedOutline': {
                                    borderColor: glowColor
                                }
                            }}
                        >
                            <MenuItem value="area">
                                <ShowChart sx={{mr: 1, fontSize: 18}}/>
                                Area
                            </MenuItem>
                            <MenuItem value="line">
                                <Timeline sx={{mr: 1, fontSize: 18}}/>
                                Line
                            </MenuItem>
                        </Select>
                    </FormControl>

                    <IconButton
                        onClick={toggleFullscreen}
                        sx={{
                            color: glowColor,
                            '&:hover': {
                                backgroundColor: `${glowColor}20`,
                                transform: 'scale(1.1)'
                            }
                        }}
                    >
                        {isFullscreen ? <FullscreenExit/> : <Fullscreen/>}
                    </IconButton>
                </Box>
            </Box>

            {/* Chart Content */}
            <Box sx={{p: 2, height: 'calc(100% - 80px)'}}>
                <div ref={chartRef} style={{width: '100%', height: '100%'}}>
                    <ResponsiveContainer width="100%" height="100%">
                        {renderChart()}
                    </ResponsiveContainer>
                </div>
            </Box>

            {/* Particle Trails */}
            <Box sx={{position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', pointerEvents: 'none'}}>
                {particles.map((particle) => (
                    <motion.div
                        key={particle.id}
                        initial={{
                            opacity: 1,
                            x: 0,
                            y: 0,
                            scale: 0.5
                        }}
                        animate={{
                            y: particle.direction === 'up' ? -50 : 50,
                            opacity: 0,
                            scale: 1.5
                        }}
                        transition={{
                            duration: 0.8,
                            ease: 'easeOut'
                        }}
                        style={{
                            position: 'absolute',
                            width: `${particle.size}px`,
                            height: `${particle.size}px`,
                            borderRadius: '50%',
                            backgroundColor: particle.color,
                            boxShadow: `0 0 10px ${particle.color}`,
                            left: `${particle.x}px`,
                            top: `${particle.y}px`
                        }}
                    />
                ))}
            </Box>
        </Box>
    );
};

// PropTypes for runtime validation in development
AdvancedChart.propTypes = {
    data: PropTypes.arrayOf(PropTypes.shape({
        name: PropTypes.string,
        value: PropTypes.number,
        volume: PropTypes.number,
        open: PropTypes.number,
        high: PropTypes.number,
        low: PropTypes.number,
        close: PropTypes.number,
        date: PropTypes.string,
        time: PropTypes.number
    })),
    title: PropTypes.string,
    height: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    glowColor: PropTypes.string,
    showIndicators: PropTypes.bool,
    showVolume: PropTypes.bool,
    sx: PropTypes.object
};

// @ts-ignore - Add displayName to function component
AdvancedChart.displayName = 'AdvancedChart';

export default AdvancedChart;
