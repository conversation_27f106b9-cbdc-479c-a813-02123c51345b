/* System-Wide Error <PERSON> Styles */

.critical-error-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #ff4444, #cc0000);
    color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(255, 68, 68, 0.3);
    z-index: 10000;
    max-width: 400px;
    display: flex;
    align-items: center;
    gap: 15px;
    animation: slideInRight 0.3s ease-out;
}

.critical-error-notification .error-icon {
    font-size: 24px;
    animation: pulse 1s infinite;
}

.critical-error-notification .error-content h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: bold;
}

.critical-error-notification .error-content p {
    margin: 0 0 12px 0;
    font-size: 14px;
    opacity: 0.9;
}

.critical-error-notification button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.2s;
}

.critical-error-notification button:hover {
    background: rgba(255, 255, 255, 0.3);
}

.safe-mode-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(255, 152, 0, 0.3);
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: 12px;
    animation: slideInDown 0.3s ease-out;
}

.safe-mode-notification .safe-mode-icon {
    font-size: 20px;
}

.safe-mode-notification .safe-mode-content h3 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: bold;
}

.safe-mode-notification .safe-mode-content p {
    margin: 0;
    font-size: 12px;
    opacity: 0.9;
}

/* Safe mode body styling */
body.safe-mode {
    filter: grayscale(0.3);
}

body.safe-mode::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 152, 0, 0.05);
    pointer-events: none;
    z-index: 1;
}

/* Error boundary fallback styling */
.error-boundary-fallback {
    padding: 20px;
    margin: 20px;
    border: 2px solid #ff4444;
    border-radius: 8px;
    background: #fff5f5;
    color: #cc0000;
}

.error-boundary-fallback.degraded {
    border-color: #ff9800;
    background: #fff8e1;
    color: #f57c00;
}

.error-boundary-fallback h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
}

.error-boundary-fallback p {
    margin: 0 0 15px 0;
    font-size: 14px;
}

.error-boundary-fallback .error-actions {
    display: flex;
    gap: 10px;
}

.error-boundary-fallback button {
    padding: 8px 16px;
    border: 1px solid currentColor;
    background: transparent;
    color: inherit;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.2s;
}

.error-boundary-fallback button:hover {
    background: currentColor;
    color: white;
}

/* System health indicator */
.system-health-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    z-index: 9999;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s;
}

.system-health-indicator.healthy {
    background: rgba(76, 175, 80, 0.9);
}

.system-health-indicator.degraded {
    background: rgba(255, 152, 0, 0.9);
}

.system-health-indicator.critical {
    background: rgba(244, 67, 54, 0.9);
    animation: pulse 1s infinite;
}

.system-health-indicator .health-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

/* Circuit breaker indicator */
.circuit-breaker-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    z-index: 10001;
    animation: fadeIn 0.3s ease-out;
}

.circuit-breaker-indicator.active {
    border: 2px solid #ff4444;
    box-shadow: 0 0 20px rgba(255, 68, 68, 0.5);
}

.circuit-breaker-indicator h2 {
    margin: 0 0 15px 0;
    color: #ff4444;
    font-size: 18px;
}

.circuit-breaker-indicator p {
    margin: 0 0 20px 0;
    font-size: 14px;
    opacity: 0.8;
}

.circuit-breaker-indicator .countdown {
    font-size: 24px;
    font-weight: bold;
    color: #ff9800;
}

/* Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translate(-50%, -100%);
        opacity: 0;
    }
    to {
        transform: translate(-50%, 0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Error recovery progress */
.error-recovery-progress {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px;
    border-radius: 8px;
    z-index: 9999;
    min-width: 250px;
}

.error-recovery-progress h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #4caf50;
}

.error-recovery-progress .progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    overflow: hidden;
    margin: 8px 0;
}

.error-recovery-progress .progress-fill {
    height: 100%;
    background: #4caf50;
    transition: width 0.3s ease;
}

.error-recovery-progress .recovery-status {
    font-size: 12px;
    opacity: 0.8;
    margin: 5px 0 0 0;
}

/* Component status grid */
.component-status-grid {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px;
    border-radius: 8px;
    z-index: 9998;
    font-size: 12px;
    max-width: 300px;
    opacity: 0.1;
    transition: opacity 0.3s;
}

.component-status-grid:hover,
.component-status-grid.has-errors {
    opacity: 1;
}

.component-status-grid h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
}

.component-status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px 0;
    padding: 3px 0;
}

.component-status-item .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
}

.component-status-item .status-dot.healthy {
    background: #4caf50;
}

.component-status-item .status-dot.degraded {
    background: #ff9800;
}

.component-status-item .status-dot.failed {
    background: #f44336;
}

.component-status-item .status-dot.unknown {
    background: #9e9e9e;
}

/* Error Notification System */
.error-notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 400px;
}

.error-notification {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 16px;
    border-left: 4px solid;
    animation: slideInRight 0.3s ease-out;
}

.error-notification.error {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #fff5f5, #ffffff);
}

.error-notification.warning {
    border-left-color: #ffc107;
    background: linear-gradient(135deg, #fffbf0, #ffffff);
}

.error-notification.success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #f0fff4, #ffffff);
}

.error-notification.info {
    border-left-color: #17a2b8;
    background: linear-gradient(135deg, #f0f9ff, #ffffff);
}

.notification-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.notification-icon {
    font-size: 16px;
}

.notification-title {
    flex: 1;
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-close:hover {
    color: #333;
}

.notification-message {
    margin: 0 0 12px 0;
    font-size: 13px;
    color: #555;
    line-height: 1.4;
}

.notification-actions {
    display: flex;
    gap: 8px;
}

.notification-action-btn {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.notification-action-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

/* Error Log Modal */
.error-log-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
}

.error-log-modal {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    height: 80%;
    max-height: 600px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #333;
}

.log-metrics {
    display: flex;
    gap: 20px;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.log-metrics .metric {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.log-metrics .metric span:first-child {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.log-metrics .metric span:last-child {
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

.log-filters {
    display: flex;
    gap: 10px;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.log-filters select,
.log-filters input {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
}

.log-filters input {
    flex: 1;
}

.log-actions {
    display: flex;
    gap: 10px;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.log-actions button {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.log-actions button:hover {
    background: #f8f9fa;
}

.log-actions button.danger {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.log-actions button.danger:hover {
    background: #c82333;
}

.log-entries {
    flex: 1;
    overflow-y: auto;
    padding: 10px 20px;
}

.no-logs {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px;
}

.log-entry {
    border: 1px solid #eee;
    border-radius: 6px;
    margin-bottom: 8px;
    overflow: hidden;
}

.log-entry.error {
    border-left: 4px solid #dc3545;
}

.log-entry.warn {
    border-left: 4px solid #ffc107;
}

.log-entry.info {
    border-left: 4px solid #17a2b8;
}

.log-entry.debug {
    border-left: 4px solid #6c757d;
}

.log-entry.critical {
    border-left: 4px solid #721c24;
    background: #fff5f5;
}

.log-header {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    cursor: pointer;
    background: #fafafa;
    transition: background 0.2s;
}

.log-header:hover {
    background: #f0f0f0;
}

.log-level {
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.log-category {
    font-size: 11px;
    color: #666;
    font-weight: 500;
    min-width: 80px;
}

.log-timestamp {
    font-size: 11px;
    color: #888;
    min-width: 120px;
}

.log-message {
    flex: 1;
    font-size: 12px;
    color: #333;
}

.log-expand {
    font-size: 10px;
    color: #666;
}

.log-details {
    padding: 15px;
    background: white;
    border-top: 1px solid #eee;
}

.log-data {
    margin-bottom: 15px;
}

.log-data strong {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: #333;
}

.log-data pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    font-size: 11px;
    overflow-x: auto;
    margin: 0;
    border: 1px solid #eee;
}

.log-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.log-meta div {
    font-size: 11px;
    color: #666;
}

.log-meta strong {
    color: #333;
}

.recovery-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.recovery-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
    .critical-error-notification,
    .safe-mode-notification,
    .error-notifications-container {
        left: 10px;
        right: 10px;
        max-width: none;
        transform: none;
    }

    .safe-mode-notification {
        left: 10px;
        right: 10px;
        transform: none;
    }

    .component-status-grid,
    .error-recovery-progress {
        left: 10px;
        right: 10px;
        max-width: none;
    }

    .system-health-indicator {
        bottom: 10px;
        right: 10px;
    }

    .error-log-modal {
        width: 95%;
        height: 90%;
        margin: 20px;
    }

    .log-filters {
        flex-direction: column;
    }

    .log-actions {
        flex-wrap: wrap;
    }

    .log-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .log-meta {
        grid-template-columns: 1fr;
    }
}