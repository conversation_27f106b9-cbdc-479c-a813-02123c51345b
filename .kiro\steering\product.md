# Product Overview

## Meme Coin Trader - Autonomous Cryptocurrency Trading Platform

A sophisticated Electron-based desktop application for autonomous cryptocurrency trading with AI-powered analytics and
real-time market monitoring.

### Core Features

- **Autonomous Trading System**: Fully automated trading bot with AI-driven decision making
- **Multi-Exchange Support**: Integrated with major exchanges via CCXT library (Binance, Coinbase, etc.)
- **Meme Coin Scanner**: Specialized detection and analysis of emerging meme cryptocurrencies
- **Whale Tracker**: Monitor large wallet movements and trading patterns
- **Grid Trading Bots**: Automated grid trading strategies with configurable parameters
- **Portfolio Management**: Real-time portfolio tracking with P&L analysis
- **Risk Management**: Built-in circuit breakers, position limits, and risk controls
- **Performance Analytics**: Comprehensive trading performance metrics and reporting

### Architecture

- **Frontend**: React 18 + Material-UI desktop interface
- **Backend**: Node.js trading engine with modular component architecture
- **Desktop App**: Electron framework for cross-platform deployment
- **Database**: SQLite/MySQL support for data persistence
- **Communication**: IPC (Inter-Process Communication) between frontend and trading engine

### Target Users

Professional traders and crypto enthusiasts seeking automated trading solutions with advanced analytics and risk
management capabilities.