# Application Startup Guide

This guide provides comprehensive instructions for starting the Meme Coin Trader application in different environments.

## Table of Contents

1. [Quick Start](#quick-start)
2. [Development Startup](#development-startup)
3. [Production Startup](#production-startup)
4. [Troubleshooting](#troubleshooting)
5. [Configuration](#configuration)
6. [Monitoring](#monitoring)

## Quick Start

### Prerequisites Check

Before starting the application, ensure you have:

```bash
# Check Node.js version (18.0.0+ required)
node --version

# Check npm version (8.0.0+ recommended)
npm --version

# Check if dependencies are installed
ls node_modules || npm install
```

### Basic Startup

```bash
# Development mode
npm run dev

# Production mode
npm run start-production

# Autonomous trading mode
npm run autonomous
```

## Development Startup

### Standard Development Mode

```bash
# Start React development server and Electron
npm run dev

# Or start components separately
npm run start          # React dev server only
npm run electron-dev    # Electron in development mode
```

### Development with Hot Reload

```bash
# Start with hot reload enabled
npm run start

# In another terminal, start Electron
npm run electron-dev
```

### Development Configuration

Create a `.env.development` file:

```bash
NODE_ENV=development
REACT_APP_DEBUG_MODE=true
REACT_APP_ENABLE_CONSOLE_LOGS=true
REACT_APP_API_BASE_URL=http://localhost:3001
ELECTRON_IS_DEV=1
```

## Production Startup

### Production Build and Start

```bash
# Build for production
npm run build:production

# Start production application
npm run start-production
```

### Packaged Application Startup

```bash
# Package the application
npm run dist

# Run the packaged application
# Windows: ./dist/Meme Coin Trader Setup.exe
# macOS: ./dist/Meme Coin Trader.dmg
# Linux: ./dist/Meme Coin Trader.AppImage
```

### Production Configuration

Ensure `.env.production` is configured:

```bash
NODE_ENV=production
REACT_APP_DEBUG_MODE=false
REACT_APP_ENABLE_CONSOLE_LOGS=false
REACT_APP_API_BASE_URL=https://api.memecointrader.com
ELECTRON_IS_DEV=0
```

## Autonomous Trading Mode

### Starting Autonomous Trading

```bash
# Start autonomous trading system
cd app/trading
npm run autonomous

# Or from root directory
npm run autonomous
```

### Autonomous Configuration

Configure autonomous trading settings:

```bash
# Edit autonomous configuration
nano app/trading/config/autonomous.json

# Or use the configuration CLI
npm run config:autonomous
```

### Autonomous Monitoring

```bash
# Monitor autonomous trading
npm run monitor:autonomous

# View trading logs
tail -f app/trading/logs/autonomous.log
```

## Startup Sequence

### Application Startup Flow

1. **Environment Validation**
   - Check Node.js version
   - Validate environment variables
   - Load configuration files

2. **Dependency Initialization**
   - Load required modules
   - Initialize database connections
   - Set up IPC communication

3. **Trading System Startup**
   - Initialize TradingOrchestrator
   - Start trading engines
   - Connect to exchanges

4. **UI Initialization**
   - Start React application
   - Initialize Electron window
   - Establish IPC channels

5. **Health Checks**
   - Verify all systems operational
   - Run startup diagnostics
   - Enable monitoring

### Startup Validation

```bash
# Validate startup sequence
npm run validate:startup

# Check system health
npm run health-check

# View startup logs
npm run logs:startup
```

## Configuration

### Environment-Specific Configuration

#### Development
```bash
# .env.development
NODE_ENV=development
REACT_APP_DEBUG_MODE=true
REACT_APP_LOG_LEVEL=debug
REACT_APP_API_TIMEOUT=60000
```

#### Staging
```bash
# .env.staging
NODE_ENV=production
REACT_APP_ENVIRONMENT=staging
REACT_APP_DEBUG_MODE=true
REACT_APP_LOG_LEVEL=info
```

#### Production
```bash
# .env.production
NODE_ENV=production
REACT_APP_DEBUG_MODE=false
REACT_APP_LOG_LEVEL=warn
REACT_APP_ENABLE_ANALYTICS=true
```

### Configuration Validation

```bash
# Validate configuration
npm run config:validate

# Reset configuration to defaults
npm run config:reset

# View current configuration
npm run config:show
```

## Monitoring

### Startup Monitoring

```bash
# Monitor startup progress
npm run monitor:startup

# View real-time logs
npm run logs:realtime

# Check startup performance
npm run performance:startup
```

### Health Monitoring

```bash
# Continuous health monitoring
npm run health:monitor

# System diagnostics
npm run diagnostics

# Performance metrics
npm run metrics
```

### Log Monitoring

```bash
# View application logs
npm run logs:app

# View trading logs
npm run logs:trading

# View error logs
npm run logs:errors
```

## Troubleshooting

### Common Startup Issues

#### Port Already in Use

```bash
# Error: Port 3000 is already in use
# Solution: Kill process using the port
lsof -ti:3000 | xargs kill -9

# Or use a different port
PORT=3001 npm run start
```

#### Missing Dependencies

```bash
# Error: Module not found
# Solution: Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

#### Database Connection Issues

```bash
# Error: Database connection failed
# Solution: Initialize database
npm run init-db

# Or reset database
npm run reset-db
```

#### Trading System Startup Failure

```bash
# Error: Trading system failed to start
# Solution: Check trading configuration
npm run config:trading:validate

# Restart trading system
npm run restart:trading
```

### Debug Mode

Enable debug mode for troubleshooting:

```bash
# Set debug environment variables
export DEBUG=*
export REACT_APP_DEBUG_MODE=true
export REACT_APP_ENABLE_CONSOLE_LOGS=true

# Start with debug enabled
npm run dev
```

### Startup Diagnostics

```bash
# Run comprehensive diagnostics
npm run diagnostics:full

# Check system requirements
npm run check:requirements

# Validate environment
npm run validate:environment
```

### Performance Issues

#### Slow Startup

```bash
# Analyze startup performance
npm run performance:analyze

# Optimize startup
npm run optimize:startup

# Use performance build
npm run build:performance
```

#### Memory Issues

```bash
# Monitor memory usage
npm run memory:monitor

# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
npm run start
```

### Recovery Procedures

#### Startup Failure Recovery

```bash
# Reset to factory defaults
npm run factory-reset

# Restore from backup
npm run restore:backup

# Rebuild application
npm run rebuild
```

#### Emergency Procedures

```bash
# Emergency stop
npm run emergency:stop

# Safe mode startup
npm run start:safe-mode

# Recovery mode
npm run start:recovery
```

## Advanced Startup Options

### Custom Startup Scripts

Create custom startup scripts for specific scenarios:

```bash
# Create custom startup script
nano scripts/custom-startup.js

# Run custom startup
node scripts/custom-startup.js
```

### Startup Hooks

Configure startup hooks for custom initialization:

```bash
# Edit startup hooks
nano config/startup-hooks.json

# Enable startup hooks
export ENABLE_STARTUP_HOOKS=true
npm run start
```

### Cluster Mode

Start multiple instances for load balancing:

```bash
# Start in cluster mode
npm run start:cluster

# Monitor cluster
npm run monitor:cluster
```

## Integration with External Systems

### API Integration

```bash
# Test API connectivity
npm run test:api

# Configure API endpoints
npm run config:api
```

### Database Integration

```bash
# Initialize database
npm run init-db

# Test database connection
npm run test:db

# Migrate database
npm run migrate:db
```

### Exchange Integration

```bash
# Test exchange connections
npm run test:exchanges

# Configure exchange APIs
npm run config:exchanges
```

## Maintenance

### Regular Maintenance Tasks

```bash
# Daily health check
npm run health-check

# Weekly performance check
npm run performance:check

# Monthly system update
npm run system:update
```

### Backup Procedures

```bash
# Backup configuration
npm run backup:config

# Backup database
npm run backup:database

# Full system backup
npm run backup:full
```

---

For additional help or support, please refer to the troubleshooting section or contact the development team.