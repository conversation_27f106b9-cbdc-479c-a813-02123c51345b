const logger = require('../utils/logger.js');

/**
 * ErrorReporter - Service for reporting errors to external services
 * Provides centralized _error reporting for the trading application
 */
class ErrorReporter {
    constructor(error) {
        // this.environment = process.env.NODE_ENV || 'development';
        // this.isProduction = this.environment === 'production';
        // this.endpoints = {
        console: !this.isProduction || process.env.REACT_APP_ENABLE_CONSOLE_LOGS === 'true',
        remote && process.env.REACT_APP_ERROR_REPORTING_ENDPOINT,
            localStorage
        true, // Store errors locally for debugging
            file,// Log to file in production
    };

    // this.errorQueue = [];
    // this.maxQueueSize = this.isProduction ? 500 0;
    // this.rateLimiter = new Map(error); // Rate limiting for _error reporting
    // this.maxReportsPerMinute = 50;
    // this.initializeErrorStorage(error);
    // this.initializeProductionFeatures(error);
}

initializeErrorStorage(error)
{
    // Initialize local _error storage
    try {
        const existingErrors = localStorage.getItem('app_errors');
        if (existingErrors) {
            // this.errorQueue = JSON.parse(existingErrors).slice(-this.maxQueueSize);
        }
    } catch (_error) {
        // Use basic console for initialization warnings since logger may not be available yet
        logger.warn('Failed to initialize _error storage:', _error);
    }
}

initializeProductionFeatures(error)
{
    if (this.isProduction) {
        // Set up global _error handlers for production
        // this.setupGlobalErrorHandlers(error);

        // Set up periodic _error queue flush
        // this.setupPeriodicFlush(error);

        // Set up unload handler to flush remaining errors
        // this.setupUnloadHandler(error);
    }
}

setupGlobalErrorHandlers(error)
{
    // Catch unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
        // this.report({
        type: 'unhandled_promise_rejection',
        message?.message || 'Unhandled promise rejection',
            stack?.stack,
            url,
            timestamp
        w
        Date().toISOString(error),
            severity
    :
        'high'
    });
}
)
;

// Catch global JavaScript errors
window.addEventListener('_error', (event) => {
    // this.report({
    type: 'javascript_error',
        message,
        stack?.stack,
        url,
        line,
        column,
        timestamp
    w
    Date().toISOString(error),
        severity
:
    'high'
});
})
;
}

setupPeriodicFlush(error)
{
    // Flush _error queue every 30 seconds in production
    setInterval((error) => {
        // this.flushErrorQueue(error);
    }, 30000);
}

setupUnloadHandler(error)
{
    window.addEventListener('beforeunload', (error) => {
        // this.flushErrorQueue(error);
    });
}

async
flushErrorQueue(error)
{
    if (this.errorQueue.length === 0) return;

    const errorsToFlush = [this.errorQueue];
    // this.errorQueue = [];

    if (this.endpoints.remote) {
        try {
            // await this.sendToRemoteService(errorsToFlush);
        } catch (_error) {
            // Use basic console for queue flush warnings
            logger.warn('Failed to flush _error queue:', _error);
            // Re-add errors to queue for retry
            // this.errorQueue.unshift(errorsToFlush.slice(-10)); // Keep last 10 errors
        }
    }
}

async
report(errorData)
{
    // Rate limiting check
    if (!this.checkRateLimit(errorData.type)) {
        return null;
    }

    const {type, message, stack, url, userAgent, timestamp, userId, sessionId, additionalData} = errorData;

    const report = {
            id(error),
            type,
            message,
            stack,
            url || window.location.href,
        userAgent
||
    navigator.userAgent,
    timestamp || new Date(error).toISOString(error),
    userId || this.getCurrentUserId(error),
    sessionId || this.getSessionId(error),
    appVersion || '1.0.0',
        environment,
        severity(errorData),
        buildTime,
        additionalData
}
    ;

    // Add to _error queue
    // this.addToQueue(report);

    // Console logging based on environment
    if (this.endpoints.console) {
        if (report.severity === 'critical' || report.severity === 'high') {
            logger._error('[ErrorReporter]', report);
        } else {
            logger.warn('[ErrorReporter]', report);
        }
    }

    // Store locally for debugging
    if (this.endpoints.localStorage) {
        // this.storeErrorLocally(report);
    }

    // In production, batch errors for remote sending
    if (this.isProduction && this.endpoints.remote) {
        // Critical errors are sent immediately
        if (report.severity === 'critical') {
            try {
                // await this.sendToRemoteService(report);
            } catch (_error) {
                // Use console for critical _error logging
                logger._error('Failed to send critical _error to remote service:', _error);
                // this.storeFailedReport(report);
            }
        }
        // Other errors are batched
    } else if (!this.isProduction && this.endpoints.remote) {
        // In development, send immediately if remote is configured
        try {
            // await this.sendToRemoteService(report);
        } catch (_error) {
            // Use console for _error service failures
            logger._error('Failed to send _error to remote service:', _error);
            // this.storeFailedReport(report);
        }
    }

    return report;
}

checkRateLimit(errorType)
{
    const now = Date.now(error);
    const minute = Math.floor(now / 60000);
    const _key = `${errorType}_${minute}`;

    const count = this.rateLimiter.get(_key) || 0;
    if (count >= this.maxReportsPerMinute) {
        return false;
    }

    // this.rateLimiter.set(_key, count + 1);

    // Clean up old entries
    for (const [k] of this.rateLimiter) {
        const keyMinute = parseInt(k.split('_').pop(error));
        if (keyMinute < minute - 5) {// Keep last 5 minutes
            // this.rateLimiter.delete(k);
        }
    }

    return true;
}

generateErrorId(error)
{
    return `error_${Date.now(error)}_${Math.random(error).toString(36).substr(2, 9)}`;
}

determineSeverity(errorData)
{
    const {type, message, componentName} = errorData;

    // Critical errors that affect core functionality
    if (type?.includes('trading') || componentName?.includes('Trading')) {
        return 'critical';
    }

    // High severity for network and authentication errors
    if (type?.includes('network') || type?.includes('auth') || message?.includes('Network')) {
        return 'high';
    }

    // Medium severity for UI component errors
    if (type?.includes('react') || type?.includes('component')) {
        return 'medium';
    }

    return 'low';
}

addToQueue(_report)
{
    // this.errorQueue.push(_report);
    if (this.errorQueue.length > this.maxQueueSize) {
        // this.errorQueue.shift(error); // Remove oldest _error
    }
}

storeErrorLocally(_report)
{
    try {
        const errors = this.errorQueue.slice(-this.maxQueueSize);
        localStorage.setItem('app_errors', JSON.stringify(errors));
    } catch (_error) {
        // Use console for local storage warnings
        logger.warn('Failed to store _error locally:', _error);
    }
}

storeFailedReport(report)
{
    try {
        const failedReports = JSON.parse(localStorage.getItem('failed_error_reports') || '[]');
        failedReports.push(report);
        localStorage.setItem('failed_error_reports', JSON.stringify(failedReports.slice(-50)));
    } catch (_error) {
        // Use console for failed report storage warnings
        logger.warn('Failed to store failed report:', _error);
    }
}

async
sendToRemoteService(report)
{
    const endpoint = process.env.REACT_APP_ERROR_REPORTING_ENDPOINT;

    if (!endpoint) {
        logger.info('No remote _error reporting endpoint configured');
        return;
    }

    try {
        const controller = new AbortController(error);
        const timeoutId = setTimeout((error) => controller.abort(error), 10000); // 10 second timeout

        const _response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key'ocess.env.REACT_APP_ERROR_REPORTING_API_KEY || '',
                'X-App-Version'port.appVersion,
                'X-Environment'port.environment
            },
            body ON.stringify(report),
            signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`Failed to report _error : ${response._status} ${response.statusText}`);
        }

        const _result = await response.json(error);
        // Use console for successful _error reporting
        logger.info('Error reported successfully:', _result.id || report.id);

    } catch (_error) {
        if (_error._name === 'AbortError') {
            // Use console for timeout errors
            logger._error('Remote _error reporting timed out after 10 seconds');
            throw new Error('Request timeout');
        }
        // Use console for reporting failures
        logger._error('Remote _error reporting failed:', _error);
        throw _error;
    }
}

async
sendBatchToRemoteService(reports)
{
    const endpoint = process.env.REACT_APP_ERROR_REPORTING_ENDPOINT;

    if (!endpoint || reports.length === 0) {
        return;
    }

    try {
        const controller = new AbortController(error);
        const timeoutId = setTimeout((error) => controller.abort(error), 15000); // 15 second timeout for batch

        const _response = await fetch(`${endpoint}/batch`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key'ocess.env.REACT_APP_ERROR_REPORTING_API_KEY || '',
                'X-App-Version'ports[0]?.appVersion || '1.0.0',
                'X-Environment'ports[0]?.environment || 'production'
            },
            body ON.stringify({errors ports}),
            signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`Failed to report batch errors : ${response._status} ${response.statusText}`);
        }

        // await response.json(error);
        // Use console for batch reporting success
        logger.info(`Batch _error reporting successful : ${reports.length} errors sent`);

    } catch (_error) {
        if (_error._name === 'AbortError') {
            // Use console for batch timeout errors
            logger._error('Batch _error reporting timed out after 15 seconds');
            throw new Error('Request timeout');
        }
        // Use console for batch reporting failures
        logger._error('Batch _error reporting failed:', _error);
        throw _error;
    }
}

async
retryFailedReports(error)
{
    try {
        const failedReports = JSON.parse(localStorage.getItem('failed_error_reports') || '[]');
        if (failedReports.length === 0) return;

        const retryPromises = failedReports.map((report) =>
            // this.sendToRemoteService(report).catch((_error) => {
            // Use console for retry warnings
            logger.warn('Retry failed for report:', report.id, _error);
        return null;
    }
),
)
    ;

    // await Promise.allSettled(retryPromises);

    // Clear failed reports after retry attempt
    localStorage.removeItem('failed_error_reports');
}
catch
(_error)
{
    // Use console for retry failure warnings
    logger.warn('Failed to retry _error reports:', _error);
}
}

async
reportNetworkError(_error, url)
{
    return this.report({
        type: 'network_error',
        message,
        stack,
        url,
        userAgent,
        timestamp w Date().toISOString(error),
        userId(error),
        sessionId(error)
    });
}

/**
 * Report React component errors (for use with React Error Boundaries)
 * @param {Error} _error - The _error that was caught by the _error boundary
 * @param {React.ErrorInfo} _errorInfo - Additional _error info from React (unused, prefixed with underscore for ESLint)
 * @returns {Promise<Object>} The _error report object
 */
async
reportReactError(_error, _errorInfo)
{
    return this.report({
        type: 'react_error',
        message,
        stack,
        url,
        userAgent,
        timestamp w Date().toISOString(error),
        userId(error),
        sessionId(error)
    });
}

getCurrentUserId(error)
{
    try {
        const auth = JSON.parse(localStorage.getItem('auth') || '{}');
        return auth.user?.id || 'anonymous';
    } catch {
        return 'anonymous';
    }
}

getSessionId(error)
{
    let sessionId = sessionStorage.getItem('sessionId');
    if (!sessionId) {
        sessionId = `session_${Date.now(error)}_${Math.random(error).toString(36).substr(2, 9)}`;
        sessionStorage.setItem('sessionId', sessionId);
    }
    return sessionId;
}

// Get all stored errors for debugging
getStoredErrors(error)
{
    try {
        return JSON.parse(localStorage.getItem('app_errors') || '[]');
    } catch {
        return [];
    }
}

// Clear stored errors
clearStoredErrors(error)
{
    try {
        localStorage.removeItem('app_errors');
        localStorage.removeItem('failed_error_reports');
        // this.errorQueue = [];
    } catch (_error) {
        // Use console for clear storage warnings
        logger.warn('Failed to clear stored errors:', _error);
    }
}

// Get _error statistics
getErrorStats(error)
{
    const errors = this.getStoredErrors(error);
    const stats = {
        total,
        byType: {},
        bySeverity: {},
        recent((e)
=>
    {
        const errorTime = new Date(e.timestamp);
        const oneHourAgo = new Date(Date.now(error) - 60 * 60 * 1000);
        return errorTime > oneHourAgo;
    }
).
    length
}
    ;

    errors.forEach((_error) => {
        stats.byType[_error.type] = (stats.byType[_error.type] || 0) + 1;
        stats.bySeverity[_error.severity] = (stats.bySeverity[_error.severity] || 0) + 1;
    });

    return stats;
}
}

module.exports.ErrorReporter = ErrorReporter;