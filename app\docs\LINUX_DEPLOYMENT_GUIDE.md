# Linux Deployment Guide

## Meme Coin Trader - Linux Production Deployment

This guide provides detailed instructions for deploying the Meme Coin Trader application on Linux systems.

---

## Table of Contents

1. [Linux Prerequisites](#linux-prerequisites)
2. [Development Environment Setup](#development-environment-setup)
3. [Build Configuration](#build-configuration)
4. [Building for Linux](#building-for-linux)
5. [Package Types](#package-types)
6. [Distribution](#distribution)
7. [Linux-Specific Troubleshooting](#linux-specific-troubleshooting)
8. [Performance Optimization](#performance-optimization)
9. [Security Considerations](#security-considerations)
10. [System Integration](#system-integration)

---

## Linux Prerequisites

### System Requirements

**Development Machine:**
- Ubuntu 18.04+ / CentOS 7+ / Fedora 30+ / Arch Linux
- 8GB RAM minimum (16GB recommended)
- 50GB free disk space
- sudo privileges for package installation

**Target Systems:**
- Ubuntu 16.04+ / CentOS 7+ / Fedora 28+ / Arch Linux
- 4GB RAM minimum (8GB recommended)
- 2GB free disk space
- X11 or Wayland display server

### Required Software

#### 1. Node.js and npm

**Ubuntu/Debian:**
```bash
# Option 1: NodeSource repository (recommended)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Option 2: Using nvm (recommended for development)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18

# Verify installation
node --version  # Should be 18.0.0+
npm --version   # Should be 8.0.0+
```

**CentOS/RHEL/Fedora:**
```bash
# Option 1: NodeSource repository
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo dnf install -y nodejs npm  # Fedora
sudo yum install -y nodejs npm  # CentOS/RHEL

# Option 2: Using nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18
```

#### 2. Build Dependencies

**Ubuntu/Debian:**
```bash
# Essential build tools
sudo apt-get update
sudo apt-get install -y build-essential python3-dev python3-pip git

# Additional dependencies for native modules
sudo apt-get install -y libnss3-dev libatk-bridge2.0-dev libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libgconf-2-4

# For AppImage creation
sudo apt-get install -y fuse libfuse2
```

**CentOS/RHEL:**
```bash
# Enable EPEL repository
sudo yum install -y epel-release

# Essential build tools
sudo yum groupinstall -y "Development Tools"
sudo yum install -y python3-devel python3-pip git

# Additional dependencies
sudo yum install -y nss-devel atk-devel libdrm libXcomposite libXdamage libXrandr libXScrnSaver GConf2

# For AppImage creation
sudo yum install -y fuse fuse-libs
```

---

## Development Environment Setup

### 1. Environment Configuration

#### Shell Configuration
```bash
# Add to ~/.bashrc or ~/.zshrc
export NODE_ENV=development
export ELECTRON_IS_DEV=1
export DISPLAY=:0  # For X11 applications

# For Wayland
export WAYLAND_DISPLAY=wayland-0
export XDG_SESSION_TYPE=wayland

# Reload shell configuration
source ~/.bashrc
```

#### System Limits
```bash
# Increase file descriptor limits for development
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# Increase inotify limits for file watching
echo "fs.inotify.max_user_watches=524288" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 2. Project Setup

```bash
# Clone repository
git clone https://github.com/your-org/meme-coin-trader.git
cd meme-coin-trader

# Install dependencies
npm install

# Setup app
cd app
npm install

# Setup trading system
cd trading
npm install
cd ..
```

---

## Build Configuration

### 1. Linux-Specific electron-builder Configuration

Update `electron-builder.config.js`:

```javascript
module.exports = {
  // ... other configuration
  
  linux: {
    target: [
      {
        target: 'AppImage',
        arch: ['x64']
      },
      {
        target: 'deb',
        arch: ['x64']
      },
      {
        target: 'rpm',
        arch: ['x64']
      },
      {
        target: 'tar.gz',
        arch: ['x64']
      }
    ],
    icon: 'build-resources/icon.png',
    category: 'Office',
    description: 'Advanced cryptocurrency trading platform with AI-powered analytics',
    desktop: {
      Name: 'Meme Coin Trader',
      Comment: 'Advanced cryptocurrency trading platform',
      Keywords: 'trading;cryptocurrency;finance;bitcoin;ethereum;',
      StartupWMClass: 'Meme Coin Trader'
    }
  },
  
  appImage: {
    artifactName: '${productName}-${version}.${ext}',
    license: 'LICENSE.txt',
    category: 'Office'
  },
  
  deb: {
    packageCategory: 'Office',
    priority: 'optional',
    depends: [
      'libgtk-3-0',
      'libnotify4',
      'libnss3',
      'libxss1',
      'libxtst6',
      'xdg-utils',
      'libatspi2.0-0',
      'libdrm2',
      'libxcomposite1',
      'libxdamage1',
      'libxrandr2',
      'libgbm1',
      'libxkbcommon0',
      'libasound2'
    ]
  },
  
  rpm: {
    packageCategory: 'Office',
    license: 'MIT',
    vendor: 'Meme Coin Trader Team',
    depends: [
      'gtk3',
      'libnotify',
      'nss',
      'libXScrnSaver',
      'libXtst',
      'xdg-utils',
      'at-spi2-atk',
      'libdrm',
      'libXcomposite',
      'libXdamage',
      'libXrandr',
      'mesa-libgbm',
      'libxkbcommon',
      'alsa-lib'
    ]
  }
};
```

---

## Building for Linux

### 1. Pre-Build Validation

```bash
# Validate environment
npm run validate:environment

# Check Linux-specific requirements
echo "System Information:"
uname -a
lsb_release -a 2>/dev/null || cat /etc/os-release

echo "Node.js Information:"
node --version
npm --version

echo "Build Dependencies:"
gcc --version
python3 --version

# Check display server
echo "Display Server:"
echo "DISPLAY: $DISPLAY"
echo "WAYLAND_DISPLAY: $WAYLAND_DISPLAY"
echo "XDG_SESSION_TYPE: $XDG_SESSION_TYPE"
```

### 2. Production Build Process

#### Step 1: Clean and Prepare
```bash
# Clean previous builds
rm -rf dist build

# Set production environment
export NODE_ENV=production
export REACT_APP_VERSION=1.0.0
export BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
```

#### Step 2: Build Frontend
```bash
# Build React application
npm run build:production

# Validate build
npm run validate:build
```

#### Step 3: Build Electron Application
```bash
# Build for all Linux targets
npm run build:linux

# Or build specific targets
npm run build:linux:appimage  # AppImage (recommended)
npm run build:linux:deb       # Debian package
npm run build:linux:rpm       # RPM package
npm run build:linux:tar       # TAR.GZ archive
```

---

## Package Types

### 1. AppImage (Recommended)

**Features:**
- Portable application format
- No installation required
- Runs on most Linux distributions
- Self-contained with all dependencies

**Build Command:**
```bash
npm run build:linux:appimage
```

**Output:**
- `dist/Meme Coin Trader-1.0.0.AppImage` (~200-250MB)

**Usage:**
```bash
# Download and run
chmod +x "Meme Coin Trader-1.0.0.AppImage"
./Meme\ Coin\ Trader-1.0.0.AppImage
```

### 2. Debian Package (.deb)

**Features:**
- Native package format for Debian/Ubuntu
- Automatic dependency resolution
- System integration
- Package manager integration

**Build Command:**
```bash
npm run build:linux:deb
```

**Output:**
- `dist/meme-coin-trader_1.0.0_amd64.deb` (~180-220MB)

**Installation:**
```bash
# Install package
sudo dpkg -i meme-coin-trader_1.0.0_amd64.deb

# Fix dependencies if needed
sudo apt-get install -f

# Launch application
meme-coin-trader
```

### 3. RPM Package (.rpm)

**Features:**
- Native package format for RedHat/CentOS/Fedora
- Automatic dependency resolution
- System integration

**Build Command:**
```bash
npm run build:linux:rpm
```

**Output:**
- `dist/meme-coin-trader-1.0.0.x86_64.rpm` (~180-220MB)

**Installation:**
```bash
# Fedora/CentOS 8+
sudo dnf install meme-coin-trader-1.0.0.x86_64.rpm

# CentOS 7/RHEL 7
sudo yum install meme-coin-trader-1.0.0.x86_64.rpm
```

---

## Linux-Specific Troubleshooting

### 1. Build Issues

#### Missing Build Dependencies
```
Error: Python executable not found
Error: make: command not found
```

**Solution:**
```bash
# Ubuntu/Debian
sudo apt-get install build-essential python3-dev

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install python3-devel

# Fedora
sudo dnf groupinstall "Development Tools"
sudo dnf install python3-devel
```

#### Native Module Compilation Errors
```
Error: node-gyp rebuild failed
```

**Solution:**
```bash
# Clear npm cache
npm cache clean --force

# Rebuild native modules
npm run rebuild-native

# Install missing headers
sudo apt-get install libnss3-dev libatk-bridge2.0-dev  # Ubuntu/Debian
sudo dnf install nss-devel atk-devel  # Fedora
```

### 2. Runtime Issues

#### Application Won't Start
```bash
# Check for missing libraries
ldd /path/to/meme-coin-trader

# Check system logs
journalctl -f | grep meme-coin-trader

# Run with debug output
DEBUG=* meme-coin-trader
```

**Common Solutions:**
```bash
# Install missing GTK libraries
sudo apt-get install libgtk-3-0 libgtk-3-dev  # Ubuntu/Debian
sudo dnf install gtk3 gtk3-devel               # Fedora

# Install missing NSS libraries
sudo apt-get install libnss3 libnss3-dev      # Ubuntu/Debian
sudo dnf install nss nss-devel                # Fedora
```

#### Display Issues

**X11 Display Problems:**
```bash
# Check DISPLAY variable
echo $DISPLAY

# Allow X11 forwarding (if using SSH)
ssh -X user@hostname

# Fix X11 permissions
xhost +local:
```

**Wayland Issues:**
```bash
# Force X11 mode
export GDK_BACKEND=x11
export QT_QPA_PLATFORM=xcb

# Or run with X11 explicitly
meme-coin-trader --disable-gpu --no-sandbox
```

---

## Performance Optimization

### 1. System-Level Optimization

#### Memory Management
```bash
# Increase swap space if needed
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Add to /etc/fstab for persistence
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

#### File System Optimization
```bash
# Increase inotify limits for file watching
echo "fs.inotify.max_user_watches=524288" | sudo tee -a /etc/sysctl.conf
echo "fs.inotify.max_user_instances=256" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

### 2. Application Optimization

#### Electron Optimization for Linux
```javascript
// main.js - Linux-specific optimizations
const { app, BrowserWindow } = require('electron');

if (process.platform === 'linux') {
  // Disable hardware acceleration if causing issues
  if (process.env.DISABLE_GPU === '1') {
    app.disableHardwareAcceleration();
  }
  
  // Enable Wayland support
  if (process.env.XDG_SESSION_TYPE === 'wayland') {
    app.commandLine.appendSwitch('enable-features', 'UseOzonePlatform');
    app.commandLine.appendSwitch('ozone-platform', 'wayland');
  }
}
```

---

## Security Considerations

### 1. File System Security

#### Secure File Permissions
```bash
# Set secure permissions for application files
chmod 755 /opt/meme-coin-trader/meme-coin-trader
chmod -R 644 /opt/meme-coin-trader/resources/

# Set secure permissions for user data
chmod 700 ~/.config/meme-coin-trader
chmod 600 ~/.config/meme-coin-trader/config.json
```

#### Encrypted Storage
```javascript
// Use Linux keyring for secure storage
const keytar = require('keytar');

// Store API keys in system keyring
async function storeApiKey(service, account, password) {
  try {
    await keytar.setPassword(service, account, password);
    console.log('API key stored in system keyring');
  } catch (error) {
    console.error('Failed to store API key:', error);
  }
}
```

---

## System Integration

### 1. Desktop Integration

#### XDG Base Directory Specification
```javascript
// Follow XDG Base Directory specification
const os = require('os');
const path = require('path');

const getConfigDir = () => {
  return process.env.XDG_CONFIG_HOME || path.join(os.homedir(), '.config');
};

const getDataDir = () => {
  return process.env.XDG_DATA_HOME || path.join(os.homedir(), '.local', 'share');
};

// Use XDG directories
const configPath = path.join(getConfigDir(), 'meme-coin-trader');
const dataPath = path.join(getDataDir(), 'meme-coin-trader');
```

### 2. System Service Integration

#### Systemd User Service
```bash
# Create systemd user service
mkdir -p ~/.config/systemd/user

cat > ~/.config/systemd/user/meme-coin-trader.service << 'EOF'
[Unit]
Description=Meme Coin Trader
After=graphical-session.target

[Service]
Type=simple
ExecStart=/opt/meme-coin-trader/meme-coin-trader --no-sandbox
Restart=on-failure
RestartSec=5
Environment=DISPLAY=:0

[Install]
WantedBy=default.target
EOF

# Enable and start service
systemctl --user daemon-reload
systemctl --user enable meme-coin-trader.service
systemctl --user start meme-coin-trader.service
```

---

*Last updated: January 29, 2025*
*Version: 1.0.0*