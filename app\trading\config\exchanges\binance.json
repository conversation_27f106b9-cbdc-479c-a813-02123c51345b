{"name": "binance", "enabled": true, "testMode": true, "credentials": {"apiKey": "${BINANCE_API_KEY}", "secret": "${BINANCE_API_SECRET}", "passphrase": "${BINANCE_PASSPHRASE}", "sandbox": true}, "features": ["spot", "futures", "margin", "websocket", "orderBook"], "limits": {"orders": 1200, "requests": 1200, "perMinute": 60}, "fees": {"maker": 0.001, "taker": 0.001}, "markets": {"spot": true, "futures": true, "margin": true}, "orderTypes": {"market": true, "limit": true, "stopLoss": true, "takeProfit": true, "trailingStop": true}, "websocket": {"enabled": true, "reconnectDelay": 5000, "maxReconnectAttempts": 10}}