import React from 'react';
import PropTypes from 'prop-types';
import {Box, Typography} from '@mui/material';

/**
 * DexScreenerChart Component
 * Embeds a chart from dexscreener.com for a given coin address.
 * @param {object} props - Component props.
 * @param {string} props.address - The token address to display the chart for.
 * @returns {React.ReactElement} The DexScreenerChart component.
 */
function DexScreenerChart({address}) {
    if (!address) {
        return (
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%'}}>
                <Typography variant="body2" color="text.secondary">
                    No address provided for chart.
                </Typography>
            </Box>
        );
    }

    const chartUrl = `https://dexscreener.com/base/${address}?embed=1&theme=dark&trades=0&info=0`;

    return (
        <Box sx={{height: '100%', width: '100%', minHeight: '300px'}}>
            <iframe
                src={chartUrl}
                title="DEX Screener Chart"
                width="100%"
                height="100%"
                style={{border: 0}}
                allowFullScreen
            />
        </Box>
    );
}

DexScreenerChart.propTypes = {
    address: PropTypes.string.isRequired
};

export default DexScreenerChart;
