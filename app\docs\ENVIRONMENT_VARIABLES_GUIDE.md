# Environment Variables Configuration Guide

## Overview

This guide provides comprehensive documentation for configuring environment variables in the Meme Coin Trader application for production deployment.

## Table of Contents

1. [Core Application Variables](#core-application-variables)
2. [Database Configuration](#database-configuration)
3. [Trading System Variables](#trading-system-variables)
4. [Exchange API Configuration](#exchange-api-configuration)
5. [Security Variables](#security-variables)
6. [Monitoring and Logging](#monitoring-and-logging)
7. [Performance Tuning](#performance-tuning)
8. [Platform-Specific Variables](#platform-specific-variables)
9. [Development vs Production](#development-vs-production)
10. [Validation and Testing](#validation-and-testing)

## Core Application Variables

### Basic Environment Configuration

```bash
# Application Environment
NODE_ENV=production                    # Application environment (development, staging, production)
ELECTRON_ENV=production               # Electron-specific environment
APP_VERSION=1.0.0                     # Application version
APP_NAME="Meme Coin Trader"           # Application display name

# Runtime Configuration
DEBUG=false                           # Enable debug mode
VERBOSE_LOGGING=false                 # Enable verbose logging
DEVELOPMENT_MODE=false                # Enable development features
```

### Application Paths

```bash
# Directory Configuration
APP_DATA_DIR=/var/lib/memetrader      # Application data directory
CONFIG_DIR=/etc/memetrader            # Configuration directory
LOG_DIR=/var/log/memetrader           # Log directory
TEMP_DIR=/tmp/memetrader              # Temporary files directory
BACKUP_DIR=/var/backups/memetrader    # Backup directory

# File Paths
PID_FILE=/var/run/memetrader.pid      # Process ID file
LOCK_FILE=/var/lock/memetrader.lock   # Application lock file
```

## Database Configuration

### SQLite Configuration

```bash
# SQLite Database
DATABASE_TYPE=sqlite                   # Database type (sqlite, mysql)
DATABASE_PATH=./databases/trading_system.db  # SQLite database file path
DATABASE_TIMEOUT=30000                # Connection timeout (milliseconds)
DATABASE_POOL_SIZE=10                 # Connection pool size
DATABASE_BUSY_TIMEOUT=5000            # Busy timeout (milliseconds)

# SQLite Performance
SQLITE_CACHE_SIZE=2000                # Cache size in pages
SQLITE_PAGE_SIZE=4096                 # Page size in bytes
SQLITE_JOURNAL_MODE=WAL               # Journal mode (DELETE, TRUNCATE, PERSIST, MEMORY, WAL, OFF)
SQLITE_SYNCHRONOUS=NORMAL             # Synchronous mode (OFF, NORMAL, FULL, EXTRA)
SQLITE_TEMP_STORE=MEMORY              # Temporary storage (DEFAULT, FILE, MEMORY)
```

### MySQL Configuration

```bash
# MySQL Database
DATABASE_TYPE=mysql                   # Database type
DATABASE_HOST=localhost               # Database host
DATABASE_PORT=3306                    # Database port
DATABASE_NAME=memetrader              # Database name
DATABASE_USER=memetrader_user         # Database username
DATABASE_PASSWORD=secure_password     # Database password
DATABASE_SSL=true                     # Enable SSL connection

# MySQL Connection Pool
DATABASE_POOL_SIZE=10                 # Maximum connections in pool
DATABASE_POOL_MIN=2                   # Minimum connections in pool
DATABASE_POOL_IDLE_TIMEOUT=300000     # Idle connection timeout
DATABASE_POOL_ACQUIRE_TIMEOUT=60000   # Connection acquire timeout
DATABASE_POOL_CREATE_TIMEOUT=30000    # Connection creation timeout
DATABASE_POOL_DESTROY_TIMEOUT=5000    # Connection destruction timeout
```

### Database Backup

```bash
# Backup Configuration
DATABASE_BACKUP_ENABLED=true          # Enable automatic backups
DATABASE_BACKUP_INTERVAL=3600000      # Backup interval (1 hour)
DATABASE_BACKUP_RETENTION=7           # Number of backups to retain
DATABASE_BACKUP_PATH=/var/backups/memetrader  # Backup directory
DATABASE_BACKUP_COMPRESSION=true      # Enable backup compression
```

## Trading System Variables

### Trading Mode Configuration

```bash
# Trading Mode
TRADING_MODE=live                     # Trading mode (paper, live)
PAPER_TRADING=false                   # Enable paper trading
SIMULATION_MODE=false                 # Enable simulation mode
BACKTESTING_MODE=false                # Enable backtesting mode

# Trading Limits
MAX_CONCURRENT_TRADES=5               # Maximum concurrent trades
MAX_DAILY_TRADES=100                  # Maximum trades per day
MAX_POSITION_SIZE=0.1                 # Maximum position size (10% of portfolio)
MIN_TRADE_AMOUNT=10                   # Minimum trade amount (USD)
MAX_TRADE_AMOUNT=1000                 # Maximum trade amount (USD)
```

### Risk Management

```bash
# Risk Management
RISK_MANAGEMENT_ENABLED=true          # Enable risk management
STOP_LOSS_PERCENTAGE=0.05             # Default stop loss (5%)
TAKE_PROFIT_PERCENTAGE=0.15           # Default take profit (15%)
MAX_DRAWDOWN_PERCENTAGE=0.20          # Maximum drawdown (20%)
POSITION_SIZING_METHOD=fixed          # Position sizing (fixed, percentage, kelly)

# Circuit Breakers
CIRCUIT_BREAKER_ENABLED=true          # Enable circuit breakers
DAILY_LOSS_LIMIT=500                  # Daily loss limit (USD)
CONSECUTIVE_LOSS_LIMIT=3              # Consecutive loss limit
VOLATILITY_THRESHOLD=0.10             # Volatility threshold (10%)
```

### Trading Strategies

```bash
# Strategy Configuration
DEFAULT_STRATEGY=meme_coin_scanner    # Default trading strategy
STRATEGY_TIMEOUT=300000               # Strategy execution timeout (5 minutes)
STRATEGY_RETRY_ATTEMPTS=3             # Number of retry attempts
STRATEGY_COOLDOWN=60000               # Cooldown between strategies (1 minute)

# Meme Coin Scanner
MEME_SCANNER_ENABLED=true             # Enable meme coin scanner
MEME_SCANNER_INTERVAL=30000           # Scan interval (30 seconds)
MEME_SCANNER_MIN_VOLUME=10000         # Minimum volume threshold
MEME_SCANNER_MAX_AGE=86400            # Maximum coin age (24 hours)

# Whale Tracker
WHALE_TRACKER_ENABLED=true            # Enable whale tracker
WHALE_TRACKER_INTERVAL=60000          # Tracking interval (1 minute)
WHALE_TRACKER_MIN_AMOUNT=100000       # Minimum whale transaction amount
WHALE_TRACKER_FOLLOW_TRADES=true      # Follow whale trades
```

## Exchange API Configuration

### General Exchange Settings

```bash
# Exchange Configuration
DEFAULT_EXCHANGE=binance              # Default exchange
EXCHANGE_TIMEOUT=15000                # API timeout (15 seconds)
EXCHANGE_RETRY_ATTEMPTS=3             # Number of retry attempts
EXCHANGE_RETRY_DELAY=1000             # Delay between retries (1 second)
EXCHANGE_RATE_LIMIT=100               # Rate limit (requests per minute)

# Sandbox Mode
EXCHANGE_SANDBOX=false                # Use exchange sandbox/testnet
EXCHANGE_TEST_MODE=false              # Enable test mode
```

### Binance Configuration

```bash
# Binance API
BINANCE_API_KEY=your_api_key          # Binance API key
BINANCE_API_SECRET=your_api_secret    # Binance API secret
BINANCE_TESTNET=false                 # Use Binance testnet
BINANCE_FUTURES=true                  # Enable futures trading
BINANCE_MARGIN=false                  # Enable margin trading

# Binance Rate Limits
BINANCE_RATE_LIMIT_ORDERS=10          # Orders per second
BINANCE_RATE_LIMIT_REQUESTS=1200      # Requests per minute
BINANCE_WEIGHT_LIMIT=6000             # Weight limit per minute
```

### Coinbase Configuration

```bash
# Coinbase Pro API
COINBASE_API_KEY=your_api_key         # Coinbase API key
COINBASE_API_SECRET=your_api_secret   # Coinbase API secret
COINBASE_PASSPHRASE=your_passphrase   # Coinbase passphrase
COINBASE_SANDBOX=false                # Use Coinbase sandbox
COINBASE_PRO=true                     # Use Coinbase Pro

# Coinbase Rate Limits
COINBASE_RATE_LIMIT_PUBLIC=10         # Public API requests per second
COINBASE_RATE_LIMIT_PRIVATE=5         # Private API requests per second
```

### Additional Exchanges

```bash
# Kraken API
KRAKEN_API_KEY=your_api_key           # Kraken API key
KRAKEN_API_SECRET=your_api_secret     # Kraken API secret
KRAKEN_TIER=3                         # API tier level

# Bybit API
BYBIT_API_KEY=your_api_key            # Bybit API key
BYBIT_API_SECRET=your_api_secret      # Bybit API secret
BYBIT_TESTNET=false                   # Use Bybit testnet

# OKX API
OKX_API_KEY=your_api_key              # OKX API key
OKX_API_SECRET=your_api_secret        # OKX API secret
OKX_PASSPHRASE=your_passphrase        # OKX passphrase
```

## Security Variables

### Encryption Configuration

```bash
# Encryption
ENCRYPTION_ENABLED=true               # Enable encryption
ENCRYPTION_ALGORITHM=aes-256-gcm      # Encryption algorithm
ENCRYPTION_KEY_LENGTH=32              # Key length in bytes
MASTER_KEY_PATH=/etc/memetrader/master.key  # Master key file path

# API Key Security
API_KEY_ENCRYPTION=true               # Encrypt API keys
CREDENTIAL_ENCRYPTION=true            # Encrypt credentials
SECURE_STORAGE=true                   # Use secure storage
```

### Authentication

```bash
# Authentication
AUTH_ENABLED=false                    # Enable authentication (for multi-user)
AUTH_METHOD=local                     # Authentication method (local, oauth, ldap)
SESSION_TIMEOUT=3600000               # Session timeout (1 hour)
JWT_SECRET=your_jwt_secret            # JWT signing secret
JWT_EXPIRY=24h                        # JWT token expiry

# API Security
API_RATE_LIMITING=true                # Enable API rate limiting
API_CORS_ENABLED=true                 # Enable CORS
API_HTTPS_ONLY=false                  # Require HTTPS only
```

### Network Security

```bash
# Network Configuration
PROXY_ENABLED=false                   # Enable proxy
PROXY_HOST=proxy.example.com          # Proxy host
PROXY_PORT=8080                       # Proxy port
PROXY_USERNAME=proxy_user             # Proxy username
PROXY_PASSWORD=proxy_password         # Proxy password

# SSL/TLS
SSL_ENABLED=false                     # Enable SSL/TLS
SSL_CERT_PATH=/etc/ssl/certs/app.crt  # SSL certificate path
SSL_KEY_PATH=/etc/ssl/private/app.key # SSL private key path
SSL_CA_PATH=/etc/ssl/certs/ca.crt     # SSL CA certificate path
```

## Monitoring and Logging

### Logging Configuration

```bash
# Logging
LOG_LEVEL=info                        # Log level (error, warn, info, debug, trace)
LOG_FORMAT=json                       # Log format (json, text, combined)
LOG_TIMESTAMP=true                    # Include timestamps
LOG_COLORS=false                      # Enable colored output (disable in production)

# File Logging
LOG_FILE_ENABLED=true                 # Enable file logging
LOG_FILE_PATH=/var/log/memetrader/app.log  # Log file path
LOG_FILE_MAX_SIZE=10MB                # Maximum log file size
LOG_FILE_MAX_FILES=5                  # Maximum number of log files
LOG_FILE_ROTATION=true                # Enable log rotation

# Console Logging
LOG_CONSOLE_ENABLED=true              # Enable console logging
LOG_CONSOLE_LEVEL=info                # Console log level
```

### Performance Monitoring

```bash
# Performance Monitoring
PERFORMANCE_MONITORING=true           # Enable performance monitoring
METRICS_COLLECTION=true               # Enable metrics collection
METRICS_INTERVAL=30000                # Metrics collection interval (30 seconds)
METRICS_RETENTION=86400000            # Metrics retention (24 hours)

# Health Checks
HEALTH_CHECK_ENABLED=true             # Enable health checks
HEALTH_CHECK_INTERVAL=30000           # Health check interval (30 seconds)
HEALTH_CHECK_TIMEOUT=5000             # Health check timeout (5 seconds)
HEALTH_CHECK_PORT=3001                # Health check port
```

### Error Reporting

```bash
# Error Reporting
ERROR_REPORTING=true                  # Enable error reporting
ERROR_REPORTING_LEVEL=error           # Error reporting level
ERROR_STACK_TRACE=true                # Include stack traces
ERROR_CONTEXT=true                    # Include error context

# External Error Reporting
SENTRY_ENABLED=false                  # Enable Sentry error reporting
SENTRY_DSN=your_sentry_dsn            # Sentry DSN
SENTRY_ENVIRONMENT=production         # Sentry environment
SENTRY_RELEASE=1.0.0                  # Sentry release version
```

## Performance Tuning

### Node.js Performance

```bash
# Node.js Configuration
NODE_OPTIONS=--max-old-space-size=4096  # Maximum heap size (4GB)
UV_THREADPOOL_SIZE=16                 # Thread pool size
NODE_MAX_SEMI_SPACE_SIZE=256          # Semi-space size (256MB)
NODE_GC_INTERVAL=100                  # Garbage collection interval

# V8 Engine Options
V8_OPTIMIZE_FOR_SIZE=false            # Optimize for size vs speed
V8_MAX_OLD_SPACE_SIZE=4096            # Maximum old space size
V8_INITIAL_OLD_SPACE_SIZE=1024        # Initial old space size
```

### Application Performance

```bash
# Performance Settings
WORKER_THREADS=4                      # Number of worker threads
CONCURRENT_REQUESTS=100               # Maximum concurrent requests
REQUEST_TIMEOUT=30000                 # Request timeout (30 seconds)
KEEP_ALIVE_TIMEOUT=5000               # Keep-alive timeout (5 seconds)

# Caching
CACHE_ENABLED=true                    # Enable caching
CACHE_TTL=300000                      # Cache TTL (5 minutes)
CACHE_MAX_SIZE=1000                   # Maximum cache entries
CACHE_TYPE=memory                     # Cache type (memory, redis)
```

### Database Performance

```bash
# Database Performance
DB_CONNECTION_POOL_SIZE=10            # Connection pool size
DB_QUERY_TIMEOUT=30000                # Query timeout (30 seconds)
DB_IDLE_TIMEOUT=300000                # Idle connection timeout (5 minutes)
DB_MAX_RETRIES=3                      # Maximum retry attempts

# Query Optimization
DB_ENABLE_QUERY_CACHE=true            # Enable query cache
DB_QUERY_CACHE_SIZE=100               # Query cache size
DB_SLOW_QUERY_THRESHOLD=1000          # Slow query threshold (1 second)
```

## Platform-Specific Variables

### Windows-Specific

```bash
# Windows Configuration
WINDOWS_SERVICE=false                 # Run as Windows service
WINDOWS_EVENT_LOG=true                # Log to Windows Event Log
WINDOWS_PERFORMANCE_COUNTERS=true     # Enable performance counters

# Windows Paths
WINDOWS_PROGRAM_DATA=%PROGRAMDATA%\MemeTrader
WINDOWS_APP_DATA=%APPDATA%\MemeTrader
WINDOWS_LOCAL_APP_DATA=%LOCALAPPDATA%\MemeTrader
```

### macOS-Specific

```bash
# macOS Configuration
MACOS_LAUNCH_DAEMON=false             # Install as launch daemon
MACOS_KEYCHAIN=true                   # Use macOS Keychain
MACOS_CONSOLE_LOG=true                # Log to macOS Console

# macOS Paths
MACOS_APP_SUPPORT=~/Library/Application\ Support/MemeTrader
MACOS_PREFERENCES=~/Library/Preferences/com.memetrader.app.plist
MACOS_LOGS=~/Library/Logs/MemeTrader
```

### Linux-Specific

```bash
# Linux Configuration
LINUX_SYSTEMD=false                   # Install as systemd service
LINUX_SYSLOG=true                     # Log to syslog
LINUX_USER=memetrader                 # Run as specific user
LINUX_GROUP=memetrader                # Run as specific group

# Linux Paths
LINUX_CONFIG_DIR=/etc/memetrader
LINUX_DATA_DIR=/var/lib/memetrader
LINUX_LOG_DIR=/var/log/memetrader
LINUX_RUN_DIR=/var/run/memetrader
```

## Development vs Production

### Development Environment

```bash
# Development Settings
NODE_ENV=development
DEBUG=true
VERBOSE_LOGGING=true
HOT_RELOAD=true
SOURCE_MAPS=true
MINIFICATION=false

# Development Database
DATABASE_TYPE=sqlite
DATABASE_PATH=./dev-database.db
DATABASE_LOGGING=true

# Development Trading
TRADING_MODE=paper
PAPER_TRADING=true
SIMULATION_MODE=true
EXCHANGE_SANDBOX=true

# Development Security
ENCRYPTION_ENABLED=false
AUTH_ENABLED=false
SSL_ENABLED=false
```

### Production Environment

```bash
# Production Settings
NODE_ENV=production
DEBUG=false
VERBOSE_LOGGING=false
HOT_RELOAD=false
SOURCE_MAPS=false
MINIFICATION=true

# Production Database
DATABASE_TYPE=mysql
DATABASE_HOST=prod-db.example.com
DATABASE_SSL=true
DATABASE_BACKUP_ENABLED=true

# Production Trading
TRADING_MODE=live
PAPER_TRADING=false
SIMULATION_MODE=false
EXCHANGE_SANDBOX=false

# Production Security
ENCRYPTION_ENABLED=true
AUTH_ENABLED=true
SSL_ENABLED=true
API_RATE_LIMITING=true
```

## Environment File Templates

### .env.production Template

```bash
# Production Environment Configuration
NODE_ENV=production
ELECTRON_ENV=production
APP_VERSION=1.0.0

# Database
DATABASE_TYPE=sqlite
DATABASE_PATH=./databases/trading_system.db
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30000
DATABASE_BACKUP_ENABLED=true

# Trading
TRADING_MODE=live
PAPER_TRADING=false
MAX_CONCURRENT_TRADES=5
RISK_MANAGEMENT_ENABLED=true

# Exchange APIs (Replace with your actual keys)
BINANCE_API_KEY=your_binance_api_key
BINANCE_API_SECRET=your_binance_api_secret
COINBASE_API_KEY=your_coinbase_api_key
COINBASE_API_SECRET=your_coinbase_api_secret
COINBASE_PASSPHRASE=your_coinbase_passphrase

# Security
ENCRYPTION_ENABLED=true
API_KEY_ENCRYPTION=true
SECURE_CREDENTIALS=true

# Monitoring
PERFORMANCE_MONITORING=true
ERROR_REPORTING=true
HEALTH_CHECK_ENABLED=true
METRICS_COLLECTION=true

# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_ROTATION=true
MAX_LOG_SIZE=10MB
MAX_LOG_FILES=5

# Performance
NODE_OPTIONS=--max-old-space-size=4096
UV_THREADPOOL_SIZE=16
WORKER_THREADS=4
```

### .env.staging Template

```bash
# Staging Environment Configuration
NODE_ENV=staging
ELECTRON_ENV=staging
APP_VERSION=1.0.0-staging

# Database
DATABASE_TYPE=mysql
DATABASE_HOST=staging-db.example.com
DATABASE_NAME=memetrader_staging
DATABASE_USER=staging_user
DATABASE_PASSWORD=staging_password
DATABASE_SSL=true

# Trading (Use testnet/sandbox)
TRADING_MODE=paper
PAPER_TRADING=true
EXCHANGE_SANDBOX=true
BINANCE_TESTNET=true

# Security (Relaxed for testing)
ENCRYPTION_ENABLED=true
AUTH_ENABLED=false
SSL_ENABLED=false

# Monitoring
PERFORMANCE_MONITORING=true
ERROR_REPORTING=true
SENTRY_ENVIRONMENT=staging

# Logging (More verbose for debugging)
LOG_LEVEL=debug
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=true
```

## Validation and Testing

### Environment Validation Script

Create `validate-env.js`:

```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Required environment variables
const requiredVars = {
  production: [
    'NODE_ENV',
    'DATABASE_TYPE',
    'TRADING_MODE',
    'LOG_LEVEL'
  ],
  development: [
    'NODE_ENV',
    'DATABASE_TYPE'
  ]
};

// Optional but recommended variables
const recommendedVars = [
  'APP_VERSION',
  'PERFORMANCE_MONITORING',
  'ERROR_REPORTING',
  'HEALTH_CHECK_ENABLED'
];

function validateEnvironment() {
  const env = process.env.NODE_ENV || 'development';
  const required = requiredVars[env] || requiredVars.development;
  
  console.log(`Validating environment variables for ${env} environment...`);
  
  let errors = [];
  let warnings = [];
  
  // Check required variables
  required.forEach(varName => {
    if (!process.env[varName]) {
      errors.push(`Missing required environment variable: ${varName}`);
    }
  });
  
  // Check recommended variables
  recommendedVars.forEach(varName => {
    if (!process.env[varName]) {
      warnings.push(`Missing recommended environment variable: ${varName}`);
    }
  });
  
  // Validate specific values
  if (process.env.NODE_ENV && !['development', 'staging', 'production'].includes(process.env.NODE_ENV)) {
    errors.push('NODE_ENV must be one of: development, staging, production');
  }
  
  if (process.env.DATABASE_TYPE && !['sqlite', 'mysql'].includes(process.env.DATABASE_TYPE)) {
    errors.push('DATABASE_TYPE must be one of: sqlite, mysql');
  }
  
  if (process.env.TRADING_MODE && !['paper', 'live'].includes(process.env.TRADING_MODE)) {
    errors.push('TRADING_MODE must be one of: paper, live');
  }
  
  if (process.env.LOG_LEVEL && !['error', 'warn', 'info', 'debug', 'trace'].includes(process.env.LOG_LEVEL)) {
    errors.push('LOG_LEVEL must be one of: error, warn, info, debug, trace');
  }
  
  // Production-specific validations
  if (env === 'production') {
    if (process.env.TRADING_MODE === 'live' && !process.env.BINANCE_API_KEY) {
      warnings.push('Live trading enabled but no exchange API keys configured');
    }
    
    if (process.env.ENCRYPTION_ENABLED === 'true' && !process.env.MASTER_KEY_PATH) {
      warnings.push('Encryption enabled but no master key path specified');
    }
  }
  
  // Report results
  if (errors.length > 0) {
    console.error('\n❌ Environment validation failed:');
    errors.forEach(error => console.error(`  - ${error}`));
    process.exit(1);
  }
  
  if (warnings.length > 0) {
    console.warn('\n⚠️  Environment validation warnings:');
    warnings.forEach(warning => console.warn(`  - ${warning}`));
  }
  
  console.log('\n✅ Environment validation passed!');
  
  // Display current configuration
  console.log('\nCurrent configuration:');
  console.log(`  Environment: ${env}`);
  console.log(`  Database: ${process.env.DATABASE_TYPE || 'not set'}`);
  console.log(`  Trading Mode: ${process.env.TRADING_MODE || 'not set'}`);
  console.log(`  Log Level: ${process.env.LOG_LEVEL || 'not set'}`);
}

// Run validation
validateEnvironment();
```

### Environment Testing Script

Create `test-env.js`:

```javascript
#!/usr/bin/env node

const { execSync } = require('child_process');

function testEnvironment() {
  console.log('Testing environment configuration...');
  
  try {
    // Test database connection
    console.log('Testing database connection...');
    execSync('node -e "require(\'./app/trading/data/DatabaseManager.js\').testConnection()"', { stdio: 'inherit' });
    
    // Test trading system initialization
    console.log('Testing trading system initialization...');
    execSync('node -e "require(\'./app/trading/TradingOrchestrator.js\').testInitialization()"', { stdio: 'inherit' });
    
    // Test API endpoints
    console.log('Testing API endpoints...');
    execSync('curl -f http://localhost:3001/health', { stdio: 'inherit' });
    
    console.log('\n✅ All environment tests passed!');
    
  } catch (error) {
    console.error('\n❌ Environment test failed:', error.message);
    process.exit(1);
  }
}

// Run tests
testEnvironment();
```

### Usage Examples

```bash
# Validate environment variables
node validate-env.js

# Test environment configuration
node test-env.js

# Load environment and start application
source .env.production && npm start

# Check specific environment variable
echo $NODE_ENV

# Set environment variable temporarily
NODE_ENV=production npm start

# Export environment variables from file
export $(cat .env.production | xargs)
```

## Best Practices

### Security Best Practices

1. **Never commit sensitive environment variables to version control**
2. **Use different API keys for different environments**
3. **Enable encryption for production environments**
4. **Rotate API keys regularly**
5. **Use secure credential storage systems**
6. **Limit API key permissions to minimum required**

### Configuration Management

1. **Use environment-specific configuration files**
2. **Validate all environment variables on startup**
3. **Provide sensible defaults for optional variables**
4. **Document all environment variables**
5. **Use configuration management tools for complex deployments**

### Monitoring and Debugging

1. **Enable comprehensive logging in production**
2. **Use structured logging (JSON format)**
3. **Monitor environment variable changes**
4. **Set up alerts for configuration errors**
5. **Regularly audit environment configurations**

---

*This environment variables guide is part of the comprehensive production deployment documentation. Ensure all variables are properly configured before deploying to production.*