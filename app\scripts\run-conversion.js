const {transform} = require('./convert-to-cjs');
const files = [
    'app/src/utils/SystemWideErrorHandler.js',
    'app/src/utils/ComponentRecoveryManager.js',
    'app/src/utils/StartupOptimizer.js',
    'app/src/services/startupService.js',
    'app/src/utils/PerformanceMonitor.js',
    'app/trading/api/health-endpoints.js',
    'app/trading/config/test-integration.js',
    'app/config-overrides.js',
    'app/trading/data/DatabaseManager.js',
    'app/trading/engines/context/ContextEngine.js',
    'app/trading/tests/test-backend.js',
    'app/trading/config/database-config.js',
    'app/trading/check-database-schema.js',
    'app/trading/autonomous-trader-service.js',
    'app/trading/analysis/RuleBasedSentiment.js',
    'app/trading/analysis/PerformanceTracker.old.js',
    'app/trading/TradingOrchestrator.js',
    'app/trading/tests/test-database-integration copy.js',
    'app/trading/tests/test-refactored-structure.js',
    'app/trading/__tests__/integration/event-coordinator-integration.test.js',
    'app/trading/engines/trading/orchestration/event-coordinator.js',
    'app/trading/engines/trading/n8n-compatibility-test.js',
    'app/trading/monitoring/health-monitoring-system.js',
    'app/trading/engines/shared/utils/ErrorBoundary.js',
    'app/trading/ai/StrategyOptimizer.js',
    'app/trading/engines/shared/utils/ErrorHandlingUtils.js',
    'app/trading/engines/config/configuration-loader.js',
    'app/trading/engines/monitoring/performance-monitor.js',
    'app/trading/engines/data-collection/DataCollector.js',
    'app/trading/data/UnifiedDatabaseInitializer.js',
    'app/electron-builder.config.js',
    'app/scripts/fix-console-statements.js',
    'app/scripts/optimize-build.js',
    'app/trading/autonomous-startup.js',
    'app/__tests__/test-ipc.js',
    'app/trading/__tests__/integration/database-trading-integration.test.js',
    'app/trading/__tests__/test-backend.js',
    'app/trading/tests/run-tests.js',
    'app/trading/monitoring/status-reporter.js',
    'app/trading/monitoring/metrics-server.js',
    'app/trading/monitoring/HealthMonitor.js',
    'app/trading/monitoring/health-monitor.js',
    'app/trading/monitoring/health-dashboard.js',
    'app/trading/monitoring/health-check.js',
    'app/trading/monitoring/health-cli.js',
    'app/trading/engines/trading/bots/GridBotManager.js',
    'app/trading/engines/trading/orchestration/component-initializer.js',
    'app/trading/engines/trading/bots/UnifiedGridBotEngine.js',
    'app/trading/engines/trading/orchestration/enhanced-component-initializer.js',
    'app/trading/engines/shared/security/health-monitor.js',
    'app/trading/data/databases/apply_unified_schema.js',
    'app/trading/data/databases/verify_database_setup.js',
    'app/trading/config/test-startup-config-loader.js',
    'app/trading/data/databases/isolate_sql_error.js',
    'app/trading/engines/shared/security/CredentialManager.js',
    'app/trading/engines/optimization/performance-monitor.js',
    'app/trading/data/databases/debug_line_67.js',
    'app/trading/engines/shared/security/risk/PositionSizingManager.js',
    'app/trading/engines/shared/security/error-handling/TradingSystemErrorHandler.js',
    'app/trading/engines/trading/ValidateTradingEngines.js',
    'app/trading/engines/shared/validation/ValidationTest.js',
    'app/trading/engines/trading/bots/FuturesGridBot.js',
    'app/trading/engines/integration/webhook-proxy.js',
    'app/trading/engines/data-collection/backtesting.js',
    'app/__tests__/test-app.js',
    'app/trading/engines/shared/security/recovery/BackupManager.js',
    'app/trading/engines/shared/security/secure-credential-manager.js',
    'app/trading/engines/shared/security/risk/LiquidationProtector.js',
    'app/trading/engines/shared/security/recovery/PositionRecoveryManager.js',
    'app/trading/engines/shared/security/recovery/RecoveryManager.js',
    'app/trading/engines/shared/security/SecureCredentialManager.js',
    'app/trading/engines/integration/event-bus.js',
    'app/trading/helpers/StartupInitializer.js',
    'app/trading/helpers/StartupHealthChecks.js',
    'app/trading/engines/logging/AuditLogger.js',
    'app/trading/engines/logging/LogManager.js',
    'app/trading/tests/integration/trading-system.test.js',
    'app/trading/data/databases/unified-database-init.js',
    'app/trading/components/GridBotManager.js',
    'app/trading/ai/CryptoDiscoveryEngine.js',
    'app/trading/components/RiskManager.js',
    'app/trading/components/StatusReporter.js',
    'app/trading/components/SystemInfoManager.js',
    'app/trading/ai/llm-coordinator.js',
    'app/trading/components/DrawdownAnalyzer.js',
    'app/trading/components/ArbitrageEngine.js',
    'app/trading/components/AlertManager.js',
    'app/trading/ai/AutonomousTrader.test.js',
    'app/trading/engines/trading/MemeCoinScanner.js',
    'app/src/components/TradingDashboard.jsx',
    'app/src/api/trading.js',
    'app/src/api/server.js',
    'app/trading/startup.js',
    'app/trading/start-trading-system.js',
    'app/trading/start-autonomous-trading.js',
    'app/trading/launchTrading.js',
    'app/trading/autonomous-trader.js',
    'app/trading/helpers/StartupPhases.js',
    'app/trading/engines/trading/AutoPositionSizer.js',
    'app/trading/monitoring/ErrorReporter.js',
    'app/trading/dependencies.js',
    'app/trading/components/ExchangeHealthMonitor.js',
    'app/trading/components/OpportunityScanner.js',
    'app/trading/components/PortfolioMonitor.js',
    'app/trading/config/ConfigurationManager.js',
    'app/trading/__tests__/comprehensive-error-handling.test.js',
    'app/trading/automatic-failure-recovery.js',
    'app/trading/config/startup-config-loader.js',
    'app/trading/config/migrations/config-migrator.js',
    'app/trading/engines/monitoring/TradingPerformanceMonitor.js',
    'app/trading/engines/trading/FuturesGridManager.js',
    'app/trading/engines/trading/AutoProfitStopManager.js',
    'app/.eslintrc.js',
    'app/.prettierrc.js',
    'app/src/components/ErrorNotificationSystem.jsx',
    'app/src/__tests__/integration/error-boundary-integration.test.js',
    'app/src/components/ArbitrageOpportunityPanel.jsx',
    'app/trading/config/config-cli.js',
    'app/trading/ai/cli.js',
    'app/src/utils/logger.js',
    'app/src/utils/StandardizedIPCHandler.js',
    'app/src/utils/validateRealTimeStatus.js',
    'app/src/services/ErrorReporter.js',
    'app/src/utils/GlobalErrorHandler.js',
    'app/src/utils/IPCErrorHandler.js',
    'app/src/utils/ElectronAPITester.js',
    'app/trading/engines/trading/TradingExecutor.js',
    'app/trading/engines/shared/security/error-handling/index.js',
    'app/src/components/LoadingFallback.js',
    'app/src/components/LazyComponents.js',
    'app/trading/engines/shared/security/error-handling/ErrorHandler.js',
    'app/main.js',
    'app/trading/engines/trading/orchestration/TradingOrchestrator.js',
    'app/src/components/AutonomousDashboard.jsx',
    'app/src/App.js',
    'app/src/components/Dashboard.jsx',
    'app/src/services/realTimeStatusService.js',
    'app/src/components/TradingStatusIndicator.jsx',
    'app/trading/engines/shared/security/safety/CircuitBreakerSystem.js',
    'app/src/components/StartupProgressPanel.jsx',
    'app/src/components/SystemStatusPanel.jsx',
    'app/trading/engines/shared/security/recovery/EnhancedRecoveryManager.js',
    'app/trading/shared/helpers/database-manager.js',
    'app/trading/monitoring/health-monitoring-integration.js',
    'app/trading/examples/precision-trading-example.js',
    'app/trading/examples/ccxt-decimal-example.js',
    'app/trading/engines/trading/whaletrader/test-whale-engine.js',
    'app/trading/engines/trading/whaletrader/WhaleSignalEngine.js',
    'app/trading/engines/exchange/ProductionExchangeConnector.js',
    'app/trading/data/transaction-manager.js',
    'app/src/utils/BuildOptimizationMonitor.js',
    'app/src/utils/AnimationOptimizer.js',
    'app/src/api/ipc-test.js',
    'app/src/config/environment.js',
    'app/src/dev/previews.js',
    'app/src/services/ipcService.js',
    'app/src/components/CoinManager.jsx',
    'app/src/components/GridTradingPanel.jsx',
    'app/src/components/WhaleTracker.jsx',
    'app/public/electron.js',
    'app/validate-syntax.js',
    'app/__tests__/e2e/simple-workflow.test.js',
    'app/__tests__/e2e/helpers/ApplicationTestHelpers.js',
    'app/__tests__/e2e/application-startup.test.js',
    'app/__tests__/e2e/system-integration.test.js',
    'app/__tests__/e2e/complete-application-workflow.test.js',
    'app/__tests__/e2e/electron-main-process-workflow.test.js',
    'app/trading/start-autonomous-mock.js',
    'app/src/__tests__/integration/start-button-integration.test.js',
    'app/src/__tests__/ipc/standardized-error-handling.test.js',
    'app/__tests__/e2e/run-comprehensive-e2e.js',
    'app/__tests__/e2e/run-e2e-tests.js',
    'app/__tests__/e2e/setup.js',
    'app/__tests__/helpers/MockClasses.js',
    'app/__tests__/test-ipc-direct.js',
    'app/__tests__/helpers/DirectIPCTester.js',
    'app/__tests__/test-ipc-end-to-end.js',
    'app/__tests__/test-ipc-error-handling-simple.js',
    'app/__tests__/test-ipc-error-handling.js',
    'app/__tests__/test-ipc-integration.js',
    'app/__tests__/test-ipc-standardized-error-handling.js',
    'app/src/__tests__/manual/validate-start-button-workflow.js',
    'app/src/__tests__/error-handling/basic-error-handling.test.js',
    'app/src/__tests__/integration/error-reporting-backend.test.js',
    'app/src/__tests__/ipc/comprehensive-ipc-test.js',
    'app/trading/engines/trading/PortfolioManager.js',
    'app/trading/config/config-test-utils.js',
    'app/trading/engines/shared/security/risk/UnifiedRiskManager.js',
    'app/trading/engines/trading/ProductionTradingExecutor.js',
    'app/trading/ib/IBDataStreamer.js',
    'app/trading/orders/OrderManager.js',
    'app/utils/StandardizedIPCHandler.js',
    'app/trading/ai/AutonomousTrader.js',
    'app/trading/analysis/TechnicalAnalyzer.js',
    'app/src/components/CrossExchangePortfolio.jsx',
    'app/src/components/MarketAnalysis.jsx',
    'app/src/components/PortfolioTracker.jsx',
    'app/src/components/SettingsModal.jsx',
    'app/src/components/SystemDiagnostics.jsx'];

for (const file of files) {
    try {
        transform(file);
        process.stdout.write(`Successfully converted ${file}\n`);
    } catch (error) {
        process.stderr.write(`Failed to convert ${file}: ${error.message}\n`);
    }
}
