import React, {use<PERSON><PERSON>back, useEffect, useMemo, useState} from 'react';
import PropTypes from 'prop-types';
import logger from '../utils/logger';
import {
  Avatar,
  Badge,
  Box,
  Chip,
  CircularProgress,
  FormControlLabel,
  Grid,
  IconButton,
  InputAdornment,
  LinearProgress,
  Switch,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography
} from '@mui/material';
import {Add, NotificationsActive, Remove, Search, Timeline, TrendingDown, TrendingUp} from '@mui/icons-material';
import VibrantButton from './VibrantButton';
import HolographicCard from './HolographicCard';

const ETH_ADDRESS_REGEX = /^(0x)?[0-9a-fA-F]{40}$/;

// Custom Whale Icon component
const WhaleIconCustom = props => (
    <svg {...props} viewBox="0 0 24 24" fill="currentColor">
        <path
            d="M17,14C17,15.66 15.66,17 14,17C12.34,17 11,15.66 11,14C11,12.34 12.34,11 14,11C15.66,11 17,12.34 17,14M22,10A1,1 0 0,0 21,9H19.5C19.13,7.83 18.2,6.88 17,6.34V4A1,1 0 0,0 16,3H15A1,1 0 0,0 14,4V6.18C12.5,5.44 10.7,5 8.75,5C4.25,5 2,8.5 2,12.5C2,15.44 3.95,19.23 8.75,19.23C12.25,19.23 14.21,17.25 15.16,16.25C15.85,16.92 16.75,17.34 17.75,17.5V20A1,1 0 0,0 18.75,21H20.25A1,1 0 0,0 21.25,20V18.5A2.5,2.5 0 0,0 23.75,16V12A2,2 0 0,0 22,10Z"/>
    </svg>
);

/** @type {Window & { electronAPI?: any }} */
const customWindow = window;
const electronAPI = customWindow.electronAPI || {
    getWhaleSignals: () => Promise.resolve({success: true, data: [], error: null}),
    getWhaleTrackingStatus: () => Promise.resolve({success: true, data: false, error: null}),
    getTrackedWallets: () => Promise.resolve({success: true, data: [], error: null}),
    getSignals: () => Promise.resolve({success: true, data: [], error: null}),
    getHistory: () => Promise.resolve({success: true, data: [], error: null}),
    toggleWhaleTracking: () => Promise.resolve({success: true, error: null}),
    addWhaleWallet: () => Promise.resolve({success: true, error: null}),
    removeWhaleWallet: () => Promise.resolve({success: true, error: null})
};

function WhaleTracker() {
    const [whaleSignals, setWhaleSignals] = useState([]);
    const [isTracking, setIsTracking] = useState(false);
    const [trackedWallets, setTrackedWallets] = useState([]);
    const [searchAddress, setSearchAddress] = useState('');
    const [alertThreshold, setAlertThreshold] = useState(1000000);
    const [showOnlyLargeTransactions, setShowOnlyLargeTransactions] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);

    const fetchData = useCallback(async () => {
        try {
            const [signals, status, wallets] = await Promise.all([
                electronAPI.getWhaleSignals(),
                electronAPI.getWhaleTrackingStatus(),
                electronAPI.getTrackedWallets()]);
            if (!signals.success) throw new Error(signals.error || 'Failed to fetch whale signals.');
            if (!status.success) throw new Error(status.error || 'Failed to fetch tracking status.');
            if (!wallets.success) throw new Error(wallets.error || 'Failed to fetch tracked wallets.');
            setWhaleSignals(signals.data || []);
            setIsTracking(status.data || false);
            setTrackedWallets(wallets.data || []);
        } catch (err) {
            logger.error('Failed to fetch whale tracking data:', err);
            setError(err.message);
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 10000); // Refresh every 10 seconds
        return () => clearInterval(interval);
    }, [fetchData]);

    const handleToggleTracking = async newStatus => {
        try {
            await electronAPI.toggleWhaleTracking(newStatus);
            setIsTracking(newStatus);
        } catch (err) {
            logger.error('Failed to toggle whale tracking:', err);
        }
    };

    const getTransactionIcon = type => {
        switch (type.toLowerCase()) {
            case 'large buy':
            case 'accumulation':
                return <TrendingUp/>;
            case 'large sell':
            case 'distribution':
                return <TrendingDown/>;
            default:
                return <Timeline/>;
        }
    };

    const formatAddress = address => {
        if (!address || address.length < 10) {
            return address || '';
        }
        return `${address.slice(0, 6)}...${address.slice(-4)}`;
    };

    const getTransactionTypeColor = type => {
        switch (type.toLowerCase()) {
            case 'large buy':
            case 'accumulation':
                return '#4caf50';
            case 'large sell':
            case 'distribution':
                return '#f44336';
            case 'transfer':
                return '#ff9800';
            default:
                return '#2196f3';
        }
    };

    const getActivityColor = activity => {
        switch (activity) {
            case 'high':
                return '#f44336';
            case 'medium':
                return '#ff9800';
            case 'low':
                return '#4caf50';
            default:
                return '#888';
        }
    };

    const handleAddWallet = async () => {
        if (ETH_ADDRESS_REGEX.test(searchAddress)) {
            try {
                const result = await electronAPI.addWhaleWallet(searchAddress);
                if (result?.success) {
                    setSearchAddress('');
                    fetchData(); // Refresh data after adding
                }
            } catch (err) {
                logger.error('Failed to add whale wallet:', err);
            }
        }
    };

    const handleRemoveWallet = async address => {
        try {
            const result = await electronAPI.removeWhaleWallet(address);
            if (result?.success) {
                fetchData(); // Refresh data after removing
            }
        } catch (err) {
            logger.error('Failed to remove whale wallet:', err);
        }
    };

    const parseAmount = amountStr => {
        if (typeof amountStr !== 'string') return 0;
        const num = parseFloat(amountStr.replace(/[^0-9.]/g, ''));
        if (amountStr.toUpperCase().includes('M')) {
            return num * 1000000;
        }
        if (amountStr.toUpperCase().includes('K')) {
            return num * 1000;
        }
        return num;
    };

    const filteredSignals = useMemo(() =>
            showOnlyLargeTransactions
                ? whaleSignals.filter(signal => parseAmount(signal.amount) >= alertThreshold)
                : whaleSignals,
        [whaleSignals, showOnlyLargeTransactions, alertThreshold],
    );

    const totalVolume = useMemo(() =>
            whaleSignals.reduce((sum, signal) => sum + parseAmount(signal.amount), 0),
        [whaleSignals],
    );

    if (isLoading) {
        return (
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh'}}>
                <CircularProgress/>
            </Box>
        );
    }

    if (error) {
        return (
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh'}}>
                <Typography color="error">{error}</Typography>
            </Box>
        );
    }

    return (
        <Box sx={{p: 3}}>
            <Box sx={{display: 'flex', alignItems: 'center', mb: 3}}>
                <WhaleIconCustom style={{color: '#00eaff', marginRight: 16, fontSize: 32}}/>
                <Box sx={{flexGrow: 1}}>
                    <Typography
                        variant="h4"
                        sx={{
                            color: '#00eaff',
                            fontWeight: 800,
                            background: 'linear-gradient(45deg, #00eaff 30%, #a259ff 90%)',
                            backgroundClip: 'text',
                            WebkitBackgroundClip: 'text',
                            WebkitTextFillColor: 'transparent'
                        }}
                    >
                        Whale Activity Tracker
                    </Typography>
                    <Typography variant="subtitle1" sx={{color: '#888'}}>
                        Monitor large wallet movements and trading patterns
                    </Typography>
                </Box>
                <FormControlLabel
                    control={
                        <Switch
                            checked={isTracking}
                            onChange={e => handleToggleTracking(e.target.checked)}
                            sx={{
                                '& .MuiSwitch-switchBase.Mui-checked': {color: '#00eaff'},
                                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {backgroundColor: '#00eaff'}
                            }}
                        />
                    }
                    label="Live Tracking"
                />
            </Box>

            <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                    <HolographicCard variant="default" elevation="medium">
                        <Typography variant="h6" sx={{color: '#00eaff', mb: 2}}>
                            Tracking Controls
                        </Typography>
                        <Box sx={{mb: 3}}>
                            <Typography variant="subtitle2" sx={{color: '#888', mb: 1}}>
                                Add Whale Wallet
                            </Typography>
                            <TextField
                                fullWidth
                                placeholder="0x742d35Cc6634C0532925a3b8D33C44C5..."
                                value={searchAddress}
                                onChange={e => setSearchAddress(e.target.value)}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <Search sx={{color: '#888'}}/>
                                        </InputAdornment>
                                    )
                                }}
                                sx={{
                                    mb: 1,
                                    '& .MuiOutlinedInput-root': {
                                        '& fieldset': {borderColor: 'rgba(0,234,255,0.3)'},
                                        '&:hover fieldset': {borderColor: 'rgba(0,234,255,0.5)'},
                                        '&.Mui-focused fieldset': {borderColor: '#00eaff'}
                                    }
                                }}
                            />
                            <VibrantButton
                                fullWidth
                                onClick={handleAddWallet}
                                disabled={!ETH_ADDRESS_REGEX.test(searchAddress)}
                                startIcon={<Add/>}
                                size="small"
                            >
                                Add Wallet
                            </VibrantButton>
                        </Box>
                        <Box sx={{mb: 3}}>
                            <Typography variant="subtitle2" sx={{color: '#888', mb: 1}}>
                                Alert Threshold
                            </Typography>
                            <TextField
                                fullWidth
                                type="number"
                                value={alertThreshold}
                                onChange={e => setAlertThreshold(parseInt(e.target.value, 10) || 0)}
                                InputProps={{
                                    startAdornment: <InputAdornment position="start">$</InputAdornment>,
                                    endAdornment: <InputAdornment position="end">USD</InputAdornment>
                                }}
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '& fieldset': {borderColor: 'rgba(0,234,255,0.3)'},
                                        '&:hover fieldset': {borderColor: 'rgba(0,234,255,0.5)'},
                                        '&.Mui-focused fieldset': {borderColor: '#00eaff'}
                                    }
                                }}
                            />
                        </Box>
                        <Box>
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={showOnlyLargeTransactions}
                                        onChange={e => setShowOnlyLargeTransactions(e.target.checked)}
                                        sx={{
                                            '& .MuiSwitch-switchBase.Mui-checked': {color: '#a259ff'},
                                            '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {backgroundColor: '#a259ff'}
                                        }}
                                    />
                                }
                                label={
                                    <Typography variant="body2" sx={{color: '#888'}}>
                                        Show only large transactions
                                    </Typography>
                                }
                            />
                        </Box>
                        <Box sx={{mt: 3}}>
                            <Typography variant="subtitle2" sx={{color: '#00eaff', mb: 1}}>
                                Today&apos;s Activity
                            </Typography>
                            <Box sx={{display: 'flex', justifyContent: 'space-between', mb: 1}}>
                                <Typography variant="body2" sx={{color: '#888'}}>
                                    Total Volume:
                                </Typography>
                                <Typography variant="body2" sx={{color: '#fff', fontWeight: 600}}>
                                    {new Intl.NumberFormat('en-US', {
                                        style: 'currency',
                                        currency: 'USD'
                                    }).format(totalVolume)}
                                </Typography>
                            </Box>
                            <Box sx={{display: 'flex', justifyContent: 'space-between'}}>
                                <Typography variant="body2" sx={{color: '#888'}}>
                                    Tracked Wallets:
                                </Typography>
                                <Typography variant="body2" sx={{color: '#fff', fontWeight: 600}}>
                                    {trackedWallets.length}
                                </Typography>
                            </Box>
                        </Box>
                    </HolographicCard>
                </Grid>

                <Grid item xs={12} md={8}>
                    <HolographicCard variant="default" elevation="medium">
                        <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2}}>
                            <Typography variant="h6" sx={{color: '#a259ff'}}>
                                Recent Whale Activity
                            </Typography>
                            <Badge badgeContent={filteredSignals.length} color="primary">
                                <NotificationsActive/>
                            </Badge>
                        </Box>
                        {filteredSignals.length > 0 ? (
                            <TableContainer>
                                <Table>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell sx={{
                                                color: '#888',
                                                borderColor: 'rgba(162,89,255,0.2)'
                                            }}>Asset</TableCell>
                                            <TableCell sx={{
                                                color: '#888',
                                                borderColor: 'rgba(162,89,255,0.2)'
                                            }}>Type</TableCell>
                                            <TableCell sx={{
                                                color: '#888',
                                                borderColor: 'rgba(162,89,255,0.2)'
                                            }}>Amount</TableCell>
                                            <TableCell sx={{
                                                color: '#888',
                                                borderColor: 'rgba(162,89,255,0.2)'
                                            }}>Time</TableCell>
                                            <TableCell sx={{
                                                color: '#888',
                                                borderColor: 'rgba(162,89,255,0.2)'
                                            }}>Confidence</TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {filteredSignals.map((signal, index) => (
                                            <TableRow key={`${signal.timestamp}-${signal.symbol}-${index}`}>
                                                <TableCell sx={{borderColor: 'rgba(162,89,255,0.1)'}}>
                                                    <Box sx={{display: 'flex', alignItems: 'center'}}>
                                                        <Avatar
                                                            sx={{
                                                                width: 32,
                                                                height: 32,
                                                                mr: 1,
                                                                backgroundColor: getTransactionTypeColor(signal.type),
                                                                fontSize: '0.8rem'
                                                            }}
                                                        >
                                                            {signal.symbol}
                                                        </Avatar>
                                                        <Typography variant="body2"
                                                                    sx={{color: '#fff', fontWeight: 600}}>
                                                            {signal.symbol}
                                                        </Typography>
                                                    </Box>
                                                </TableCell>
                                                <TableCell sx={{borderColor: 'rgba(162,89,255,0.1)'}}>
                                                    <Chip
                                                        icon={getTransactionIcon(signal.type)}
                                                        label={signal.type}
                                                        size="small"
                                                        sx={{
                                                            backgroundColor: `${getTransactionTypeColor(signal.type)}20`,
                                                            color: getTransactionTypeColor(signal.type),
                                                            fontWeight: 600
                                                        }}
                                                    />
                                                </TableCell>
                                                <TableCell sx={{color: '#fff', borderColor: 'rgba(162,89,255,0.1)'}}>
                                                    <Typography variant="body2" sx={{fontWeight: 600}}>
                                                        {signal.amount}
                                                    </Typography>
                                                </TableCell>
                                                <TableCell sx={{color: '#888', borderColor: 'rgba(162,89,255,0.1)'}}>
                                                    <Typography
                                                        variant="body2">{new Date(signal.timestamp).toLocaleTimeString()}</Typography>
                                                </TableCell>
                                                <TableCell sx={{borderColor: 'rgba(162,89,255,0.1)'}}>
                                                    <Box sx={{display: 'flex', alignItems: 'center'}}>
                                                        <LinearProgress
                                                            variant="determinate"
                                                            value={(signal.confidence || 0.8) * 100}
                                                            sx={{
                                                                width: 60,
                                                                mr: 1,
                                                                backgroundColor: 'rgba(162,89,255,0.2)',
                                                                '& .MuiLinearProgress-bar': {backgroundColor: '#a259ff'}
                                                            }}
                                                        />
                                                        <Typography variant="caption" sx={{color: '#888'}}>
                                                            {Math.round((signal.confidence || 0.8) * 100)}%
                                                        </Typography>
                                                    </Box>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        ) : (
                            <Box sx={{textAlign: 'center', py: 4, color: '#888'}}>
                                <WhaleIconCustom style={{fontSize: 64, opacity: 0.3, marginBottom: 16}}/>
                                <Typography variant="h6" sx={{mb: 1}}>
                                    No Whale Activity Detected
                                </Typography>
                                <Typography variant="body2">
                                    {isTracking ? 'Monitoring for large transactions...' : 'Enable tracking to see whale activity'}
                                </Typography>
                            </Box>
                        )}
                    </HolographicCard>
                </Grid>

                <Grid item xs={12}>
                    <HolographicCard variant="default" elevation="medium">
                        <Typography variant="h6" sx={{color: '#ffc107', mb: 2}}>
                            Tracked Whale Wallets ({trackedWallets.length})
                        </Typography>
                        <Grid container spacing={2}>
                            {trackedWallets.map(wallet => (
                                <Grid item xs={12} md={6} lg={4} key={wallet.address}>
                                    <HolographicCard variant="gold" elevation="low" sx={{p: 2}}>
                                        <Box sx={{
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            alignItems: 'center',
                                            mb: 1
                                        }}>
                                            <Typography variant="body1" sx={{color: '#ffc107', fontWeight: 'bold'}}>
                                                {wallet.name || 'Unnamed Whale'}
                                            </Typography>
                                            <IconButton size="small" onClick={() => handleRemoveWallet(wallet.address)}>
                                                <Remove sx={{color: '#ffc107', fontSize: '1rem'}}/>
                                            </IconButton>
                                        </Box>
                                        <Typography variant="caption" sx={{color: '#888', display: 'block', mb: 1}}>
                                            {formatAddress(wallet.address)}
                                        </Typography>
                                        <Box sx={{
                                            display: 'flex',
                                            justifyContent: 'space-between',
                                            alignItems: 'center',
                                            mb: 1
                                        }}>
                                            <Typography variant="body2" sx={{color: '#fff'}}>
                                                Balance: {new Intl.NumberFormat('en-US', {
                                                style: 'currency',
                                                currency: 'USD'
                                            }).format(wallet.balance)}
                                            </Typography>
                                            <Chip
                                                label={wallet.activity}
                                                size="small"
                                                sx={{
                                                    backgroundColor: `${getActivityColor(wallet.activity)}20`,
                                                    color: getActivityColor(wallet.activity),
                                                    textTransform: 'capitalize'
                                                }}
                                            />
                                        </Box>
                                        <Typography variant="caption" sx={{color: '#888'}}>
                                            Last activity: {wallet.lastTransaction}
                                        </Typography>
                                    </HolographicCard>
                                </Grid>
                            ))}
                        </Grid>
                    </HolographicCard>
                </Grid>
            </Grid>
        </Box>
    );
}

WhaleTracker.propTypes = {
    whaleSignals: PropTypes.array,
    isTracking: PropTypes.bool,
    onToggleTracking: PropTypes.func,
    onAddWhaleWallet: PropTypes.func,
    onRemoveWhaleWallet: PropTypes.func
};

export default WhaleTracker;
