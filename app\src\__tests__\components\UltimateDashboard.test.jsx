import React from 'react';
import {render, screen, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import UltimateDashboard from '../../components/UltimateDashboard';

// Mock the HolographicCard component
jest.mock('../../components/HolographicCard', () => {
    return function MockHolographicCard({children, ...props}) {
        return <div data-testid="holographic-card" {...props}>{children}</div>;
    };
});

// Mock framer-motion
jest.mock('framer-motion', () => ({
    motion: {
        div: ({children, ...props}) => <div {...props}>{children}</div>
    }
}));

// Mock recharts
jest.mock('recharts', () => ({
    ResponsiveContainer: ({children}) => <div>{children}</div>,
    AreaChart: ({children}) => <div data-testid="area-chart">{children}</div>,
    Area: () => <div data-testid="area"/>,
    XAxis: () => <div data-testid="x-axis"/>,
    YAxis: () => <div data-testid="y-axis"/>,
    CartesianGrid: () => <div data-testid="cartesian-grid"/>,
    Tooltip: () => <div data-testid="tooltip"/>
}));

describe('UltimateDashboard', () => {
    const defaultProps = {
        showNotification: jest.fn()
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('renders without crashing', async () => {
        render(<UltimateDashboard {...defaultProps} />);

        await waitFor(() => {
            expect(screen.getByText(/Quantum Elite Trading Matrix/i)).toBeInTheDocument();
        });
    });

    test('displays portfolio value', async () => {
        render(<UltimateDashboard {...defaultProps} />);

        await waitFor(() => {
            expect(screen.getByText('$15,420.83')).toBeInTheDocument();
        });
    });

    test('displays active bots count', async () => {
        render(<UltimateDashboard {...defaultProps} />);

        await waitFor(() => {
            expect(screen.getByText('7')).toBeInTheDocument();
        });
    });

    test('displays system status', async () => {
        render(<UltimateDashboard {...defaultProps} />);

        await waitFor(() => {
            expect(screen.getByText('HEALTHY')).toBeInTheDocument();
        });
    });

    test('displays total P&L', async () => {
        render(<UltimateDashboard {...defaultProps} />);

        await waitFor(() => {
            expect(screen.getByText('$847.65')).toBeInTheDocument();
        });
    });

    test('renders all metric cards', async () => {
        render(<UltimateDashboard {...defaultProps} />);

        await waitFor(() => {
            const cards = screen.getAllByTestId('holographic-card');
            expect(cards.length).toBeGreaterThan(0);
        });
    });

    test('renders portfolio performance chart', async () => {
        render(<UltimateDashboard {...defaultProps} />);

        await waitFor(() => {
            expect(screen.getByText('Portfolio Performance')).toBeInTheDocument();
            expect(screen.getByTestId('area-chart')).toBeInTheDocument();
        });
    });
});
