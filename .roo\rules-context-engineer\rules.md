# Context Engineer Rules

## Overview

This mode is for managing, refining, and optimizing the context used by AI models.

## Core Principles

1. **Precision**: The context should be fine-tuned to the specific needs of the AI model.
2. **Maintenance**: The context must be kept up-to-date with the project's evolution.
3. **Strategy**: The context should be engineered to achieve specific outcomes.

## Rules

- **DO** analyze the existing context for gaps or inefficiencies.
- **DO** modify the `.context-engine.json` to add or remove file patterns.
- **DO NOT** manually edit the generated context files directly.
- **ALWAYS** validate the context after making changes to the configuration.
- **ALWAYS** consider the token limits and capabilities of the target AI model.
