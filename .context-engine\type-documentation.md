# Type Definition Context

## Summary

- **Total Definitions**: 37
- **Interfaces**: 25
- **Type Aliases**: 12
- **Classes**: 0
- **Enums**: 0

## Global TypeScript Types

| Type | Description |
|------|-------------|

## Interfaces

### IPCResponse

- **File**: app\src\types\electron-api.d.ts:6
- **Exported**: Yes
- **Dependencies**: T
- **Usages**: 118

```typescript
export interface IPCResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
}
```

### SystemHealthData

- **File**: app\src\types\electron-api.d.ts:12
- **Exported**: Yes
- **Dependencies**: Record
- **Usages**: 1

```typescript
export interface SystemHealthData {
    status: string;
    uptime: number;
    cpu: number;
    memory: number;
    components?: Record<string, any>;
}
```

### RealTimeStatusData

- **File**: app\src\types\electron-api.d.ts:20
- **Exported**: Yes
- **Usages**: 1

```typescript
export interface RealTimeStatusData {
    isRunning: boolean;
    isInitialized: boolean;
    health: string;
    message?: string;
    timestamp: number;
    uptime?: number;
}
```

### SystemMetricsData

- **File**: app\src\types\electron-api.d.ts:29
- **Exported**: Yes
- **Usages**: 1

```typescript
export interface SystemMetricsData {
    performance: any;
    health: string;
    uptime: number;
    activeSignals: number;
    pendingTrades: number;
    lastUpdate: number;
    systemLoad: {
        activeBots: number;
        dataCollectionActive: boolean;
        analysisActive: bool
// ... (truncated)
```

### ElectronAPI

- **File**: app\src\types\electron-api.d.ts:43
- **Exported**: Yes
- **Dependencies**: Promise, IPCResponse, RealTimeStatusData, SystemHealthData, SystemMetricsData
- **Usages**: 2

```typescript
export interface ElectronAPI {
    stopAllGrids(): unknown;
    startAutonomousTrading(): unknown;
    stopAutonomousTrading(): unknown;
    getAutonomousStatus(): unknown;
    checkDatabaseReady(): unknown;
    getCrossExchangeBalance(): unknown;
    updateGridBotConfig(config: any): unknown
// ... (truncated)
```

### Window

- **File**: app\src\types\electron-api.d.ts:309
- **Exported**: No
- **Dependencies**: ElectronAPI
- **Usages**: 0

```typescript
interface Window {
        electronAPI: ElectronAPI;
    }
```

### ElectronAPI

- **File**: app\src\types\electron.d.ts:7
- **Exported**: No
- **Dependencies**: Promise
- **Usages**: 2

```typescript
interface ElectronAPI {
        [x: string]: any;
        on?: (event: string, listener: (...args: any[]) => void) => void;
        removeListener?: (event: string, listener: (...args: any[]) => void) => void;
        emit?: (event: string, ...args: any[]) => void;
        getMarketData?: (symb
// ... (truncated)
```

### Window

- **File**: app\src\types\electron.d.ts:16
- **Exported**: No
- **Dependencies**: ElectronAPI
- **Usages**: 0

```typescript
interface Window {
        electronAPI?: ElectronAPI;
    }
```

### Matchers

- **File**: app\src\__tests__\jest.d.ts:9
- **Exported**: No
- **Dependencies**: R, RegExp, HTMLElement, Record
- **Usages**: 0

```typescript
interface Matchers<R> {
            toBeInTheDocument(): R;

            toHaveClass(className: string): R;

            toHaveAttribute(attr: string, value?: string): R;

            toHaveTextContent(text: string | RegExp): R;

            toBeVisible(): R;

            toBeDisabled(): 
// ... (truncated)
```

### TradingConfig

- **File**: app\types\ipc-types.d.ts:22
- **Exported**: Yes
- **Dependencies**: Symbol
- **Usages**: 0

```typescript
export interface TradingConfig {
    symbol: Symbol;
    amount?: number;
    price?: number;
    side?: 'buy' | 'sell';
    type?: 'market' | 'limit';

    [key: string]: any;
}
```

## Type Aliases

### Symbol

- **File**: app\types\ipc-types.d.ts:9
- **Exported**: Yes
- **Usages**: 18

```typescript
export type Symbol = string;
```

### BotId

- **File**: app\types\ipc-types.d.ts:10
- **Exported**: Yes
- **Usages**: 2

```typescript
export type BotId = string;
```

### ComponentName

- **File**: app\types\ipc-types.d.ts:11
- **Exported**: Yes
- **Usages**: 3

```typescript
export type ComponentName = string;
```

### ExchangeName

- **File**: app\types\ipc-types.d.ts:12
- **Exported**: Yes
- **Usages**: 2

```typescript
export type ExchangeName = string;
```

### SessionId

- **File**: app\types\ipc-types.d.ts:13
- **Exported**: Yes
- **Usages**: 2

```typescript
export type SessionId = string;
```

### Timeframe

- **File**: app\types\ipc-types.d.ts:14
- **Exported**: Yes
- **Usages**: 2

```typescript
export type Timeframe = string;
```

### FilePath

- **File**: app\types\ipc-types.d.ts:15
- **Exported**: Yes
- **Usages**: 2

```typescript
export type FilePath = string;
```

### FileExtension

- **File**: app\types\ipc-types.d.ts:16
- **Exported**: Yes
- **Usages**: 1

```typescript
export type FileExtension = string;
```

### FileType

- **File**: app\types\ipc-types.d.ts:17
- **Exported**: Yes
- **Usages**: 1

```typescript
export type FileType = string;
```

### SearchPattern

- **File**: app\types\ipc-types.d.ts:18
- **Exported**: Yes
- **Usages**: 1

```typescript
export type SearchPattern = string;
```

