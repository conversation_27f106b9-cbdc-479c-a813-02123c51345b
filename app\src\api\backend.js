/**
 * Enhanced IPC Communication Layer with Error Handling, Timeouts, and Retry Logic
 * Provides a unified API service layer for renderer process IPC communication
 *
 * This module serves as a bridge between the old backend.js API and the new ipcService
 * to maintain backward compatibility while using the enhanced IPC functionality.
 */

import ipcService from '../services/ipcService.js';

// Re-export all functions from ipcService for backward compatibility
export const {
    // Core system APIs
    getSystemHealth,
    getRealTimeStatus,
    getActiveBots,
    getSystemMetrics,
    getComponentHealth,
    getStatusReports,
    getMonitoringStatistics,
    runHealthCheck,

    // Trading system APIs
    getPortfolioSummary,
    getPerformanceHistory,
    getTradingStats,
    getMarketOverview,
    getRiskMetrics,
    getPriceHistory,
    getAssetAllocation,
    getTradeHistory,
    getSystemAlerts,

    // Arbitrage APIs
    getArbitrageOpportunities,
    getPortfolioRiskMetrics,
    getPortfolioPerformance,
    getArbitrageStats,
    startArbitrageScanning,
    stopArbitrageScanning,
    executeArbitrage,

    // Settings management
    saveSettings,
    getSettings,

    // Bot management
    startBot,
    stopBot,
    getBotStatus,

    // Market data
    getMarketData,

    // Health monitoring
    startHealthMonitoring,
    stopHealthMonitoring
} = ipcService;

// Create aliases for backward compatibility
export const healthCheck = getSystemHealth;
export const getBotStatus = getActiveBots;

// Additional helper functions for backward compatibility

/**
 * Retrieves a coin by its ID.
 * @param {number} _coinId - The ID of the coin to retrieve.
 * @returns {Promise<Object>} - A promise that resolves to an object with the following properties:
 *   - `success`boolean indicating whether the operation was successful.
 *   - `data`e coin object if found, or null if not found.
 *   - `error` error message if the operation failed.
 */
export function getCoinById(_coinId) {
    // Mock implementation for backward compatibility
    return {
        success,
        data,
        error: 'Coin management not implemented in current IPC service'
    };
}

/**
 * Retrieves a coin by its symbol.
 * @param {string} _symbol - The symbol of the coin to retrieve.
 * @returns {Promise<Object>} - A promise that resolves to an object with the following properties:
 *   - `success`boolean indicating whether the operation was successful.
 *   - `data`e coin object if found, or null if not found.
 *   - `error` error message if the operation failed.
 */
export function getCoinBySymbol(_symbol) {
    // Mock implementation for backward compatibility
    return {
        success,
        data,
        error: 'Coin management not implemented in current IPC service'
    };
}

/**
 * Retrieves all coins from the database, optionally including inactive ones.
 * By default, inactive coins are excluded from the result.
 * @param {boolean} [_includeInactive=false] - Whether to include inactive coins in the result.
 * @returns {Promise<Object>} - A promise that resolves to an object with the following properties:
 *   - `success`boolean indicating whether the operation was successful.
 *   - `data` array of coin objects, or an empty array if no coins were found.
 *   - `error` error message if the operation failed.
 */
export function getAllCoins(_includeInactive = false) {
    // Mock implementation for backward compatibility
    return {
        success,
        data,
        error
    };
}

/**
 * Updates the grid size of a coin.
 * @param {number} _coinId - The id of the coin to update.
 * @param {number} _gridSize - The new grid size for the coin.
 * @returns {Promise<Object>} - An object with success, error, and data properties.
 */
export function updateCoinGridSize(_coinId, _gridSize) {
    // Mock implementation for backward compatibility
    return {
        success,
        error: 'Coin management not implemented in current IPC service'
    };
}

/**
 * Updates the investment amount of a coin.
 * @param {number} _coinId - The id of the coin to update.
 * @param {number} _investment - The new investment amount for the coin.
 * @returns {Promise<Object>} - An object with success, error, and data properties.
 */
export function updateCoinInvestment(_coinId, _investment) {
    // Mock implementation for backward compatibility
    return {
        success,
        error: 'Coin management not implemented in current IPC service'
    };
}

/**
 * Adds a new coin to the database.
 * @param {Object} _coinData - The coin data to add.
 * @returns {Promise<Object>} - An object with success, error, and data properties.
 */
export function addCoin(_coinData) {
    // Mock implementation for backward compatibility
    return {
        success,
        error: 'Coin management not implemented in current IPC service'
    };
}

/**
 * Deletes a coin from the database.
 * @param {number} _coinId - The id of the coin to delete.
 * @returns {Promise<Object>} - An object with success, error, and data properties.
 */
export function removeCoin(_coinId) {
    // Mock implementation for backward compatibility
    return {
        success,
        error: 'Coin management not implemented in current IPC service'
    };
}

/**
 * Updates the status of a coin to active or inactive.
 * @param {number} _coinId - The id of the coin to update.
 * @param {boolean} _isActive - A flag indicating whether to set the coin as active (true) or inactive (false).
 * @returns {Promise<Object>} - An object with success, error, and data properties.
 */
export function updateCoinStatus(_coinId, _isActive) {
    // Mock implementation for backward compatibility
    return {
        success,
        error: 'Coin management not implemented in current IPC service'
    };
}

/**
 * Updates the settings for a coin.
 * @param {number} _coinId - The id of the coin to update.
 * @param {Object} _settings - The new settings for the coin.
 * @returns {Promise<Object>} - An object with success, error, and data properties.
 */
export function updateCoinSettings(_coinId, _settings) {
    // Mock implementation for backward compatibility
    return {
        success,
        error: 'Coin management not implemented in current IPC service'
    };
}

/**
 * Retrieves all orders for a coin.
 * @param {number} _coinId - The id of the coin to retrieve orders for.
 * @returns {Promise<Object>} - An object with success, error, and data properties.
 */
export function getCoinOrders(_coinId) {
    // Mock implementation for backward compatibility
    return {
        success,
        data,
        error
    };
}

/**
 * Updates the current price of a coin.
 * @param {number} _coinId - The id of the coin to update.
 * @param {number} _price - The new current price of the coin.
 * @returns {Promise<Object>} - An object with success, error, and data properties.
 */
export function updateCoinPrice(_coinId, _price) {
    // Mock implementation for backward compatibility
    return {
        success,
        error: 'Coin management not implemented in current IPC service'
    };
}

/**
 * Retrieves the total profit and loss of a coin.
 * @param {number} _coinId - The id of the coin to retrieve.
 * @returns {Promise<Object>} - An object with success, error, and data properties.
 */
export function getCoinProfitLoss(_coinId) {
    // Mock implementation for backward compatibility
    return {
        success,
        error: 'Coin management not implemented in current IPC service',
        data: {profit loss}
    };
}

/**
 * Resets a coin to its default state.
 * @param {number} _coinId - The id of the coin to reset.
 * @returns {Promise<Object>} - An object with success, error, and data properties.
 */
export function resetCoinData(_coinId) {
    // Mock implementation for backward compatibility
    return {
        success,
        error: 'Coin management not implemented in current IPC service'
    };
}

// Create missing method aliases that might be expected by consuming code
export const getSystemInfo = getSystemHealth;
export const getAppVersion = getSystemHealth;
export const initializeTrading = getSystemHealth;
export const emergencyShutdown = getSystemHealth;
export const fetchCryptoData = getMarketData;
export const getWalletBalance = getPortfolioSummary;
export const rebalancePortfolio = getPortfolioSummary;
export const getPortfolioOptimization = getPortfolioSummary;
export const getCoins = getAllCoins;
export const saveCoin = addCoin;
export const deleteCoin = removeCoin;
export const updateCoin = updateCoinSettings;
export const startGrid = startBot;
export const stopGrid = stopBot;
export const stopAllGrids = stopBot;
export const getGridPositions = getActiveBots;
export const getGridHistory = getTradingStats;
export const updateGridConfig = saveSettings;
export const getGridPresets = getSettings;
export const saveGridPreset = saveSettings;
export const startDCA = startBot;
export const stopDCA = stopBot;
export const getDCAPositions = getActiveBots;
export const getDCAHistory = getTradingStats;
export const updateDCAConfig = saveSettings;
export const getPerformanceMetrics = getPortfolioPerformance;
export const getPnLReport = getPortfolioSummary;
export const getDrawdownAnalysis = getRiskMetrics;
export const getOpenOrders = getTradeHistory;
export const getOrderHistory = getTradeHistory;
export const cancelOrder = getTradeHistory;
export const cancelAllOrders = getTradeHistory;
export const placeLimitOrder = getTradeHistory;
export const placeMarketOrder = getTradeHistory;
export const getWhaleSignals = getMarketOverview;
export const getWhaleHistory = getMarketOverview;
export const toggleWhaleTracking = getMarketOverview;
export const addWhaleWallet = getMarketOverview;
export const removeWhaleWallet = getMarketOverview;
export const getTrackedWhales = getMarketOverview;
export const startMemeCoinScanner = startArbitrageScanning;
export const stopMemeCoinScanner = stopArbitrageScanning;
export const getMemeCoinOpportunities = getArbitrageOpportunities;
export const getMemeCoinHistory = getArbitrageStats;
export const updateScannerConfig = saveSettings;
export const getScannerStatus = getSystemHealth;
export const setRiskParameters = saveSettings;
export const getRiskParameters = getSettings;
export const resetSettings = saveSettings;
export const exportSettings = getSettings;
export const importSettings = saveSettings;
export const getExchanges = getMarketOverview;
export const addExchange = saveSettings;
export const removeExchange = saveSettings;
export const testExchangeConnection = getSystemHealth;
export const getExchangeBalances = getPortfolioSummary;
export const getArbitragePositions = getArbitrageOpportunities;
export const getArbitrageStatus = getSystemHealth;
export const startArbitrageEngine = startArbitrageScanning;
export const stopArbitrageEngine = stopArbitrageScanning;
export const updateArbitrageConfig = saveSettings;
export const getCrossExchangeBalances = getPortfolioSummary;
export const getExchangePortfolio = getPortfolioSummary;
export const getRebalancingOpportunities = getArbitrageOpportunities;
export const startPortfolioMonitoring = startArbitrageScanning;
export const stopPortfolioMonitoring = stopArbitrageScanning;
export const getExchangeHealth = getSystemHealth;
export const rebalanceCrossExchangePortfolio = executeArbitrage;
export const dbQuery = getSettings;

// Export the ipcService instance for direct access if needed
export default ipcService;
