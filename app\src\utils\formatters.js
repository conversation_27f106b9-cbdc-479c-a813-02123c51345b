import React from 'react';

/**
 * Formats a given number as a currency string with thousands/millions/billions separators.
 * @param {number} value - The number to format.
 * @returns {string} - The formatted currency string.
 * @example
 * formatCurrency(1234567) // $1.23M
 * formatCurrency(1234) // $1.23K
 * formatCurrency(12) // $12.0000
 */
export const formatCurrency = (value) => {
  // Handle null, undefined, or non-numeric values
  if (value === null || value === undefined || isNaN(value)) {
    return '$0.0000';
  }

  const numValue = Number(value);

  if (numValue >= 1000000000) {
    return `${(numValue / 1000000000).toFixed(2)}B`;
  } else if (numValue >= 1000000) {
    return `${(numValue / 1000000).toFixed(2)}M`;
  } else if (numValue >= 1000) {
    return `${(numValue / 1000).toFixed(2)}K`;
  }

  return `${numValue.toFixed(4)}`;
};

/**
 * Formats a given number as a percentage string with a green color for positive values and a red color for negative values, and a '+' sign for positive values.
 * @param {number} value - The number to format as a percentage.
 * @returns {React.ReactElement} - The formatted percentage string.
 */
export const formatPercentage = (value) => {
  if (typeof value !== 'number' || isNaN(value)) {
    return React.createElement('span', {
      style: { color: '#888', fontWeight: 'bold' },
    }, 'N/A');
  }
  const color = value >= 0 ? '#4caf50' : '#f44336';
  const sign = value >= 0 ? '+' : '';
  return React.createElement('span', {
    style: { color, fontWeight: 'bold' },
  }, `${sign}${value.toFixed(2)}%`);
};
