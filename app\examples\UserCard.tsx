import React, { useState } from 'react';
import { User } from './types';

interface UserCardProps {
  user: User;
  onEdit: (user: User) => void;
  onDelete: (userId: string) => void;
  className?: string;
}

export const UserCard: React.FC<UserCardProps> = ({
  user,
  onEdit,
  onDelete,
  className = '',
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleEdit = () => {
    setIsMenuOpen(false);
    onEdit(user);
  };

  const handleDelete = () => {
    setIsDeleting(true);
    try {
      onDelete(user.id);
    } finally {
      setIsDeleting(false);
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'text-green-600';
      case 'inactive':
        return 'text-red-600';
      case 'pending':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 relative ${className}`}>
      {/* User Avatar */}
      <div className="flex items-center mb-4">
        <img
          src={user.avatar ?? '/default-avatar.png'}
          alt={`${user.name}'s avatar`}
          className="w-12 h-12 rounded-full mr-4"
          onError={(e) => {
            e.currentTarget.src = '/default-avatar.png';
          }}
        />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{user.name}</h3>
          <p className="text-sm text-gray-600">{user.email}</p>
        </div>
      </div>

      {/* User Details */}
      <div className="space-y-2 mb-4">
        <div className="flex justify-between">
          <span className="text-sm text-gray-500">Role:</span>
          <span className="text-sm font-medium text-gray-900">{user.role}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-500">Status:</span>
          <span
            className={`text-sm font-medium ${getStatusColor(user.status)}`}
          >
            {user.status}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm text-gray-500">Joined:</span>
          <span className="text-sm font-medium text-gray-900">
            {new Date(user.createdAt).toLocaleDateString()}
          </span>
        </div>
      </div>

      {/* Actions Menu */}
      <div className="absolute top-4 right-4">
        <button
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          disabled={isDeleting}
        >
          <svg
            className="w-5 h-5 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 5v.01M12 12v.01M12 19v.01"
            />
          </svg>
        </button>

        {isMenuOpen && (
          <div className="absolute right-0 top-10 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-10">
            <button
              onClick={handleEdit}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
            >
              Edit
            </button>
            <button
              onClick={handleDelete}
              disabled={isDeleting}
              className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100 transition-colors disabled:opacity-50"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </button>
          </div>
        )}
      </div>

      {/* Click outside handler */}
      {isMenuOpen && (
        <button
          type="button"
          aria-label="Close menu"
          className="fixed inset-0 z-5"
          style={{ opacity: 0, pointerEvents: 'auto' }}
          onClick={() => setIsMenuOpen(false)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              setIsMenuOpen(false);
            }
          }}
          tabIndex={0}
        />
      )}
    </div>
  );
};