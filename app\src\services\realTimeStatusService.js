/**
 * Mock Real-Time Status Service
 * This is a temporary implementation to enable build completion
 */
class RealTimeStatusService {
  constructor() {
    this.isInitialized = false;
    this.health = 'unknown';
    this.components = {};
  }

  initialize() {
    this.isInitialized = true;
    this.health = 'healthy';
    return Promise.resolve();
  }

  getStatus() {
    return {
      isInitialized: this.isInitialized,
      health: this.health,
      components: this.components,
      timestamp: Date.now(),
    };
  }

  updateComponentStatus(componentName, status) {
    this.components[componentName] = status;
  }

  getHealth() {
    return this.health;
  }

  setHealth(status) {
    this.health = status;
  }

  destroy() {
    this.isInitialized = false;
    this.health = 'unknown';
    this.components = {};
  }
}

export default new RealTimeStatusService();