'use strict';

const logger = require('./logger.js');
const validateRealTimeStatusService = () => {
  const issues = [];
  // Check if service exists
  try {
    const realTimeStatusService = require('../services/realTimeStatusService').default;
    // Check required methods
    const requiredMethods = ['initialize', 'startPolling', 'stopPolling', 'subscribe', 'fetchAllStatus', 'getCurrentStatus', 'forceUpdate', 'getTotalActiveOperations', 'getStatusSummary'];
    requiredMethods.forEach(method => {
      if (typeof realTimeStatusService[method] !== 'function') {
        issues.push(`Missing method: ${method}`);
      }
    });
    // Check initial state
    const currentStatus = realTimeStatusService.getCurrentStatus();
    if (!currentStatus || typeof currentStatus !== 'object') {
      issues.push('Invalid initial status state');
    }
    // Check required status properties
    const requiredStatusProps = ['system', 'trading', 'operations'];
    requiredStatusProps.forEach(prop => {
      if (!currentStatus[prop]) {
        issues.push(`Missing status property: ${prop}`);
      }
    });
  } catch (error) {
    issues.push(`Failed to load real-time status service: ${error.message}`);
  }
  const isValid = issues.length === 0;
  return {
    isValid,
    issues,
  };
};
module.exports.validateRealTimeStatusService = validateRealTimeStatusService;
const validateIPCChannels = () => {
  const issues = [];
  // Check if electronAPI is available
  if (typeof window === 'undefined' || !window.electronAPI) {
    issues.push('electronAPI not available - may be running outside Electron context');
    const isValid = issues.length === 0;
    return {
      isValid,
      issues,
    };
  }
  // Check required IPC channels
  const requiredChannels = ['getRealTimeStatus', 'getActiveBots', 'getSystemMetrics', 'getSystemHealth', 'getPerformanceMetrics', 'getWhaleSignals', 'getMemeCoinOpportunities'];
  requiredChannels.forEach(channel => {
    if (typeof window.electronAPI[channel] !== 'function') {
      issues.push(`Missing IPC channel: ${channel}`);
    }
  });
  const isValid = issues.length === 0;
  return {
    isValid,
    issues,
  };
};
module.exports.validateIPCChannels = validateIPCChannels;
const validateUIComponents = () => {
  const issues = [];
  // Check if React components exist
  const components = ['SystemStatusPanel', 'RunningOperationsMonitor', 'TradingStatusIndicator', 'LiveSystemMonitor'];
  components.forEach(componentName => {
    try {
      require(`../components/${componentName}.jsx`);
    } catch (error) {
      issues.push(`Failed to load component ${componentName}: ${error.message}`);
    }
  });
  const isValid = issues.length === 0;
  return {
    isValid,
    issues,
  };
};
module.exports.validateUIComponents = validateUIComponents;
const validateRealTimeStatusImplementation = () => {
  const serviceValidation = validateRealTimeStatusService();
  const ipcValidation = validateIPCChannels();
  const uiValidation = validateUIComponents();
  const allIssues = [
    ...serviceValidation.issues.map(issue => `Service: ${issue}`),
    ...ipcValidation.issues.map(issue => `IPC: ${issue}`),
    ...uiValidation.issues.map(issue => `UI: ${issue}`),
  ];
  const isValid = serviceValidation.isValid && ipcValidation.isValid && uiValidation.isValid;
  logger.info(`✅ Service Validation: ${serviceValidation.isValid ? 'PASSED' : 'FAILED'}`);
  logger.info(`✅ IPC Validation: ${ipcValidation.isValid ? 'PASSED' : 'FAILED'}`);
  logger.info(`✅ UI Validation: ${uiValidation.isValid ? 'PASSED' : 'FAILED'}`);
  if (allIssues.length > 0) {
    logger.info('\n❌ Issues found:');
    allIssues.forEach(issue => logger.info(`  - ${issue}`));
  }
  logger.info(`\n${isValid ? '✅ VALIDATION PASSED' : '❌ VALIDATION FAILED'}`);
  return {
    isValid,
    issues: allIssues,
    details: {
      service: serviceValidation,
      ipc: ipcValidation,
      ui: uiValidation,
    },
  };
};
module.exports.validateRealTimeStatusImplementation = validateRealTimeStatusImplementation;
const testRealTimeStatusFlow = () => {
  try {
    const realTimeStatusService = require('../services/realTimeStatusService').default;
    let callbackCount = 0;
    const unsubscribe = realTimeStatusService.subscribe('test', status => {
      callbackCount++;
      logger.info(`📊 Status update received (${callbackCount}):`, status);
    });
    // Test status retrieval
    const currentStatus = realTimeStatusService.getCurrentStatus();
    logger.info('📈 Current Status:', currentStatus);
    // Test status summary
    const summary = realTimeStatusService.getStatusSummary();
    logger.info('📋 Status Summary:', summary);
    // Cleanup
    unsubscribe();
    logger.info('✅ Real-Time Status Flow Test PASSED');
    return {
      success: true,
    };
  } catch (error) {
    logger.info('❌ Real-Time Status Flow Test FAILED:', error.message);
    return {
      success: false,
      error: error.message,
    };
  }
};
module.exports.testRealTimeStatusFlow = testRealTimeStatusFlow;

// Auto-run validation if this file is executed directly
if (typeof window !== 'undefined' && window.location) {
  // Running in browser context
  setTimeout(() => {
    validateRealTimeStatusImplementation();
    testRealTimeStatusFlow();
  }, 1000);
}
