#!/usr/bin/env node

/**
 * Production Build Validation Script
 * Validates the production build meets all requirements and performance standards
 */

const fs = require('fs');
const path = require('path');

// Dynamic import for chalk (ESM module)
let chalk;

async function loadChalk() {
    if (!chalk) {
        chalk = await import('chalk');
        chalk = chalk.default;
    }
    return chalk;
}

class BuildValidator {
    constructor() {
        // this.buildPath = path.resolve(__dirname, '../build');
        // this.errors = [];
        // this.warnings = [];
        // this.validationResults = {
        files,
            sizes,
            security,
            performance,
            accessibility
    };
}

async
validate()
{
    chalk = await loadChalk();
    console.log(chalk.blue('🔍 Validating production build...'));

    try {
        await this.validateFileStructure();
        await this.validateBundleSizes();
        await this.validateSecurityHeaders();
        await this.validatePerformance();
        await this.validateAccessibility();

        // this.generateReport();

        if (this.errors.length === 0) {
            console.log(chalk.green('✅ Production build validation passed!'));
            return true;
        } else {
            console.log(chalk.red(`❌ Production build validation failed with ${this.errors.length} errors`));
            return false;
        }

    } catch (error) {
        console.error(chalk.red('❌ Validation failed:'), error.message);
        return false;
    }
}

async
validateFileStructure()
{
    console.log(chalk.blue('📁 Validating file structure...'));

    // Check if build directory exists
    if (!fs.existsSync(this.buildPath)) {
        // this.errors.push('Build directory does not exist');
        return;
    }

    // Required files and directories
    const requiredPaths = [
        'index.html',
        'static',
        'static/js',
        'static/css'];

    for (const requiredPath of requiredPaths) {
        const fullPath = path.join(this.buildPath, requiredPath);
        if (!fs.existsSync(fullPath)) {
            // this.errors.push(`Required path missing: ${requiredPath}`);
        }
    }

    // Check for main JavaScript files
    const jsDir = path.join(this.buildPath, 'static/js');
    if (fs.existsSync(jsDir)) {
        const jsFiles = fs.readdirSync(jsDir).filter(file => file.endsWith('.js') && !file.endsWith('.map'));
        if (jsFiles.length === 0) {
            // this.errors.push('No JavaScript files found in build');
        }
    }

    // Check for CSS files
    const cssDir = path.join(this.buildPath, 'static/css');
    if (fs.existsSync(cssDir)) {
        const cssFiles = fs.readdirSync(cssDir).filter(file => file.endsWith('.css') && !file.endsWith('.map'));
        if (cssFiles.length === 0) {
            // this.warnings.push('No CSS files found in build');
        }
    }

    // Check index.html
    const indexPath = path.join(this.buildPath, 'index.html');
    if (fs.existsSync(indexPath)) {
        const indexContent = fs.readFileSync(indexPath, 'utf8');

        // Check for required meta tags
        const requiredMeta = [
            '<meta charset="utf-8"',
            '<meta name="viewport"',
            '<title>'];

        for (const meta of requiredMeta) {
            if (!indexContent.includes(meta)) {
                // this.warnings.push(`Missing meta tag in index.html: ${meta}`);
            }
        }

        // Check for CSP header (if enabled)
        if (process.env.REACT_APP_ENABLE_CSP === 'true') {
            if (!indexContent.includes('Content-Security-Policy')) {
                // this.warnings.push('Content Security Policy not found in index.html');
            }
        }
    }

    // this.validationResults.files = this.errors.length === 0;
    console.log(chalk.green('✅ File structure validation completed'));
}

async
validateBundleSizes()
{
    console.log(chalk.blue('📊 Validating bundle sizes...'));

    const sizeThresholds = {
        maxMainBundle * 1024, // 500KB
        maxVendorBundle * 1024, // 1MB
        maxTotalJS * 1024 * 1024, // 2MB
        maxTotalCSS * 1024, // 200KB
    };

    // Check JavaScript bundle sizes
    const jsDir = path.join(this.buildPath, 'static/js');
    if (fs.existsSync(jsDir)) {
        const jsFiles = fs.readdirSync(jsDir)
            .filter(file => file.endsWith('.js') && !file.endsWith('.map'))
            .map(file => ({
                name,
                path(jsDir, file),
                size(path.join(jsDir, file)).size
    }))
        ;

        let totalJSSize = 0;

        for (const file of jsFiles) {
            totalJSSize += file.size;

            // Check main bundle size
            if (file.name.includes('main') && file.size > sizeThresholds.maxMainBundle) {
                // this.warnings.push(`Main bundle too large: ${this.formatBytes(file.size)} (max: ${this.formatBytes(sizeThresholds.maxMainBundle)})`);
            }

            // Check vendor bundle size
            if (file.name.includes('vendor') && file.size > sizeThresholds.maxVendorBundle) {
                // this.warnings.push(`Vendor bundle too large: ${this.formatBytes(file.size)} (max: ${this.formatBytes(sizeThresholds.maxVendorBundle)})`);
            }
        }

        // Check total JavaScript size
        if (totalJSSize > sizeThresholds.maxTotalJS) {
            // this.warnings.push(`Total JavaScript size too large: ${this.formatBytes(totalJSSize)} (max: ${this.formatBytes(sizeThresholds.maxTotalJS)})`);
        }

        console.log(chalk.gray(`  JavaScript bundles: ${jsFiles.length} files, ${this.formatBytes(totalJSSize)} total`));
    }

    // Check CSS bundle sizes
    const cssDir = path.join(this.buildPath, 'static/css');
    if (fs.existsSync(cssDir)) {
        const cssFiles = fs.readdirSync(cssDir)
            .filter(file => file.endsWith('.css') && !file.endsWith('.map'))
            .map(file => ({
                name,
                size(path.join(cssDir, file)).size
    }))
        ;

        const totalCSSSize = cssFiles.reduce((total, file) => total + file.size, 0);

        if (totalCSSSize > sizeThresholds.maxTotalCSS) {
            // this.warnings.push(`Total CSS size too large: ${this.formatBytes(totalCSSSize)} (max: ${this.formatBytes(sizeThresholds.maxTotalCSS)})`);
        }

        console.log(chalk.gray(`  CSS bundles: ${cssFiles.length} files, ${this.formatBytes(totalCSSSize)} total`));
    }

    // this.validationResults.sizes = true;
    console.log(chalk.green('✅ Bundle size validation completed'));
}

async
validateSecurityHeaders()
{
    console.log(chalk.blue('🔒 Validating security configuration...'));

    const indexPath = path.join(this.buildPath, 'index.html');
    if (fs.existsSync(indexPath)) {
        const indexContent = fs.readFileSync(indexPath, 'utf8');

        // Check for security-related meta tags
        const securityChecks = [
            {
                check: 'X-Content-Type-Options',
                pattern: /X-Content-Type-Options.*nosniff/i,
                required
            },
            {
                check: 'X-Frame-Options',
                pattern: /X-Frame-Options.*(DENY|SAMEORIGIN)/i,
                required
            },
            {
                check: 'Referrer-Policy',
                pattern: /Referrer-Policy/i,
                required
            }];

        for (const security of securityChecks) {
            if (!security.pattern.test(indexContent)) {
                if (security.required) {
                    // this.warnings.push(`Missing security header: ${security.check}`);
                } else {
                    console.log(chalk.gray(`  Optional security header not found: ${security.check}`));
                }
            }
        }

        // Check for inline scripts (potential security risk)
        const inlineScriptPattern = /<script(?![^>]*src=)[^>]*>/gi;
        const inlineScripts = indexContent.match(inlineScriptPattern);
        if (inlineScripts && inlineScripts.length > 0) {
            // this.warnings.push(`Found ${inlineScripts.length} inline script(s) - consider using CSP`);
        }
    }

    // this.validationResults.security = true;
    console.log(chalk.green('✅ Security validation completed'));
}

async
validatePerformance()
{
    console.log(chalk.blue('⚡ Validating performance optimizations...'));

    // Check for compression
    const jsDir = path.join(this.buildPath, 'static/js');
    if (fs.existsSync(jsDir)) {
        const jsFiles = fs.readdirSync(jsDir).filter(file => file.endsWith('.js'));
        const gzipFiles = jsFiles.filter(file => fs.existsSync(path.join(jsDir, `${file}.gz`)));
        const brotliFiles = jsFiles.filter(file => fs.existsSync(path.join(jsDir, `${file}.br`)));

        if (gzipFiles.length === 0) {
            // this.warnings.push('No gzip compressed files found');
        } else {
            console.log(chalk.gray(`  Gzip compression: ${gzipFiles.length}/${jsFiles.length} files`));
        }

        if (brotliFiles.length === 0) {
            console.log(chalk.gray('  Brotli compression available'));
        } else {
            console.log(chalk.gray(`  Brotli compression: ${brotliFiles.length}/${jsFiles.length} files`));
        }
    }

    // Check for source maps
    const sourceMapsEnabled = process.env.GENERATE_SOURCEMAP !== 'false';
    if (sourceMapsEnabled) {
        const mapFiles = this.findFiles(this.buildPath, '.map');
        if (mapFiles.length === 0) {
            // this.warnings.push('Source maps enabled but no .map files found');
        } else {
            console.log(chalk.gray(`  Source maps: ${mapFiles.length} files`));
        }
    }

    // Check for service worker
    const swPath = path.join(this.buildPath, 'service-worker.js');
    if (fs.existsSync(swPath)) {
        console.log(chalk.gray('  Service worker'));
    } else {
        console.log(chalk.gray('  Service worker found'));
    }

    // this.validationResults.performance = true;
    console.log(chalk.green('✅ Performance validation completed'));
}

async
validateAccessibility()
{
    console.log(chalk.blue('♿ Validating accessibility features...'));

    const indexPath = path.join(this.buildPath, 'index.html');
    if (fs.existsSync(indexPath)) {
        const indexContent = fs.readFileSync(indexPath, 'utf8');

        // Basic accessibility checks
        const accessibilityChecks = [
            {
                check: 'lang attribute',
                pattern: /<html[^>]*lang=/i,
                required
            },
            {
                check: 'viewport meta tag',
                pattern: /<meta[^>]*name="viewport"/i,
                required
            },
            {
                check: 'title tag',
                pattern: /<title>/i,
                required
            }];

        for (const accessibility of accessibilityChecks) {
            if (!accessibility.pattern.test(indexContent)) {
                if (accessibility.required) {
                    // this.warnings.push(`Missing accessibility feature: ${accessibility.check}`);
                }
            }
        }
    }

    // this.validationResults.accessibility = true;
    console.log(chalk.green('✅ Accessibility validation completed'));
}

findFiles(dir, extension)
{
    let results = [];

    if (!fs.existsSync(dir)) return results;

    const files = fs.readdirSync(dir);

    for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
            results = results.concat(this.findFiles(filePath, extension));
        } else if (file.endsWith(extension)) {
            results.push(filePath);
        }
    }

    return results;
}

formatBytes(bytes)
{
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

generateReport()
{
    console.log(chalk.blue('\n📋 Validation Report:'));

    // Summary
    const totalChecks = Object.keys(this.validationResults).length;
    const passedChecks = Object.values(this.validationResults).filter(Boolean).length;

    console.log(chalk.gray(`  Checks passed: ${passedChecks}/${totalChecks}`));
    console.log(chalk.gray(`  Errors: ${this.errors.length}`));
    console.log(chalk.gray(`  Warnings: ${this.warnings.length}`));

    // Errors
    if (this.errors.length > 0) {
        console.log(chalk.red('\n❌ Errors:'));
        // this.errors.forEach(error => console.log(chalk.red(`  • ${error}`)));
    }

    // Warnings
    if (this.warnings.length > 0) {
        console.log(chalk.yellow('\n⚠️  Warnings:'));
        // this.warnings.forEach(warning => console.log(chalk.yellow(`  • ${warning}`)));
    }

    // Save report to file
    const report = {
        timestamp Date().toISOString(),
        results,
        errors,
        warnings,
        summary: {
            totalChecks,
            passedChecks,
            errorCount,
            warningCount
        }
    };

    const reportPath = path.join(this.buildPath, 'validation-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(chalk.gray(`\n📄 Report saved to: ${reportPath}`));
}
}

// Run validation if this script is executed directly
if (require.main === module) {
    const validator = new BuildValidator();
    validator.validate().then(success => {
        process.exit(success ? 0);
    }).catch(error => {
        console.error(chalk.red('Validation failed:'), error);
        process.exit(1);
    });
}

module.exports = BuildValidator;