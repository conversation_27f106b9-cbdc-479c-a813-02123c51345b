/**
 * @fileoverview Electron API type definitions
 * @description Comprehensive type definitions for Electron APIs used in the application
 */

export interface IPCResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
}

export interface SystemHealthData {
    status: string;
    uptime: number;
    cpu: number;
    memory: number;
    components?: Record<string, any>;
}

export interface RealTimeStatusData {
    isRunning: boolean;
    isInitialized: boolean;
    health: string;
    message?: string;
    timestamp: number;
    uptime?: number;
}

export interface SystemMetricsData {
    performance: any;
    health: string;
    uptime: number;
    activeSignals: number;
    pendingTrades: number;
    lastUpdate: number;
    systemLoad: {
        activeBots: number;
        dataCollectionActive: boolean;
        analysisActive: boolean;
    };
}

export interface ElectronAPI {
    // ========================================================================================
    startBot: () => Promise<IPCResponse>;
    stopBot: () => Promise<IPCResponse>;
    getBotStatus: () => Promise<IPCResponse>;
    // ========================================================================================
    getRealTimeStatus: () => Promise<IPCResponse<RealTimeStatusData>>;
    getSystemHealth: () => Promise<IPCResponse<SystemHealthData>>;
    getComponentHealth: (componentName: string) => Promise<IPCResponse>;
    getSystemMetrics: () => Promise<IPCResponse<SystemMetricsData>>;
    getActiveBots: () => Promise<IPCResponse<any[]>>;
    getSystemAlerts: () => Promise<IPCResponse<any[]>>;
    // ========================================================================================
    startHealthMonitoring: () => Promise<IPCResponse>;
    stopHealthMonitoring: () => Promise<IPCResponse>;
    getStatusReports: (params: { limit: number; filter: any }) => Promise<IPCResponse<any[]>>;
    getMonitoringStatistics: () => Promise<IPCResponse>;
    runHealthCheck: (componentName?: string) => Promise<IPCResponse>;
    healthCheck: () => Promise<IPCResponse>;
    // ========================================================================================
    getPortfolioSummary: () => Promise<IPCResponse>;
    getPerformanceMetrics: () => Promise<IPCResponse>;
    getTradingStats: () => Promise<IPCResponse>;
    getTradeHistory: (limit?: number) => Promise<IPCResponse<any[]>>;
    getRiskMetrics: () => Promise<IPCResponse>;
    getAssetAllocation: () => Promise<IPCResponse<any[]>>;

    // ========================================================================================
    // Core Bot Control Methods
    // ========================================================================================
    getMarketData: (symbol: string, timeframe?: string) => Promise<IPCResponse>;
    getMarketOverview: () => Promise<IPCResponse>;
    getPriceHistory: (symbol: string, timeframe?: string) => Promise<IPCResponse<any[]>>;

    // ========================================================================================
    // Real-time Status and Health Methods
    getSentimentData?: (symbol: string) => Promise<IPCResponse>;
    // ========================================================================================
    getWhaleSignals: () => Promise<IPCResponse>;
    getTrackedWhales: () => Promise<IPCResponse>;
    getWhaleTrackingStatus: () => Promise<IPCResponse>;
    // ========================================================================================
    getMemeCoinOpportunities: () => Promise<IPCResponse>;
    startMemeCoinScanner: () => Promise<IPCResponse>;

    // ========================================================================================
    // Health Monitoring Methods
    stopMemeCoinScanner: () => Promise<IPCResponse>;
    // ========================================================================================
    getSettings: () => Promise<IPCResponse>;
    saveSettings: (settings: any) => Promise<IPCResponse>;
    // ========================================================================================
    initializeTrading: () => Promise<IPCResponse>;
    // ========================================================================================
    restartComponent?: (params: { componentName: string; strategy: string; context: any }) => Promise<IPCResponse>;
    isolateComponent?: (params: { componentName: string; context: any }) => Promise<IPCResponse>;

    // ========================================================================================
    // Trading and Portfolio Methods
    recoverTradingSystem?: (params: { component?: string; errorType?: string }) => Promise<IPCResponse>;
    triggerEmergencyProtocols?: (params: {
        componentName: string;
        error: string;
        context: any
    }) => Promise<IPCResponse>;
    // ========================================================================================
    getPerformanceHistory?: (timeRange: string) => Promise<IPCResponse<any[]>>;
    getArbitrageOpportunities?: () => Promise<IPCResponse<any[]>>;
    getPortfolioRiskMetrics?: () => Promise<IPCResponse>;
    getPortfolioPerformance?: () => Promise<IPCResponse>;

    // ========================================================================================
    // Market Data Methods
    getArbitrageStats?: () => Promise<IPCResponse>;
    startArbitrageScanning?: () => Promise<IPCResponse>;
    stopArbitrageScanning?: () => Promise<IPCResponse>;
    startPortfolioMonitoring?: () => Promise<IPCResponse>;

    // ========================================================================================
    // Whale Tracking Methods
    stopPortfolioMonitoring?: () => Promise<IPCResponse>;
    executeArbitrage?: (payload: { opportunity: any }) => Promise<IPCResponse & { actualProfit?: number }>;
    // ========================================================================================
    getSystemInfo: () => Promise<IPCResponse>;

    // ========================================================================================
    // Meme Coin Scanner Methods
    getAppVersion: () => Promise<IPCResponse>;
    // ========================================================================================
    getLogs: (level?: string, limit?: number) => Promise<IPCResponse<any[]>>;
    clearLogs: () => Promise<IPCResponse>;

    // ========================================================================================
    // Settings and Configuration Methods
    exportLogs: () => Promise<IPCResponse>;
    setLogLevel: (level: string) => Promise<IPCResponse>;

    // ========================================================================================
    // Trading System Initialization
    reportError: (error: any, context?: any) => Promise<IPCResponse>;

    // ========================================================================================
    // Error Recovery Methods (Optional - may not be implemented)
    // ========================================================================================
    startGrid: (config: any) => Promise<IPCResponse>;
    stopGrid: (gridId: string) => Promise<IPCResponse>;
    getGridPositions: () => Promise<IPCResponse<any[]>>;
    getGridHistory: (gridId?: string) => Promise<IPCResponse<any[]>>;

    // ========================================================================================
    // Legacy/Compatibility Methods
    updateGridConfig?: (config: any) => Promise<IPCResponse>;
    getGridPresets: () => Promise<IPCResponse<any[]>>;
    saveGridPreset: (preset: any) => Promise<IPCResponse>;
    // ========================================================================================
    getOpenOrders: (symbol?: string) => Promise<IPCResponse<any[]>>;
    getOrderHistory: (limit?: number) => Promise<IPCResponse<any[]>>;
    cancelOrder: (orderId: string) => Promise<IPCResponse>;
    cancelAllOrders: () => Promise<IPCResponse>;
    placeLimitOrder: (params: any) => Promise<IPCResponse>;
    placeMarketOrder: (params: any) => Promise<IPCResponse>;
    executeTrade?: (tradeParams: any) => Promise<IPCResponse>;

    // ========================================================================================
    // System Information Methods
    // ========================================================================================
    toggleWhaleTracking: (enabled: boolean) => Promise<IPCResponse>;
    addWhaleWallet: (address: string) => Promise<IPCResponse>;

    // ========================================================================================
    // Logging Methods
    removeWhaleWallet: (address: string) => Promise<IPCResponse>;
    getWhaleHistory: (timeframe: string) => Promise<IPCResponse<any[]>>;
    // ========================================================================================
    getScannerStatus: () => Promise<IPCResponse>;
    getMemeCoinHistory: () => Promise<IPCResponse<any[]>>;
    updateScannerConfig: (config: any) => Promise<IPCResponse>;

    // ========================================================================================
    // Grid Trading Methods
    // ========================================================================================
    setRiskParameters: (params: any) => Promise<IPCResponse>;
    getRiskParameters: () => Promise<IPCResponse>;
    // ========================================================================================
    getExchanges: () => Promise<IPCResponse<any[]>>;
    addExchange: (config: any) => Promise<IPCResponse>;
    removeExchange: (params: { exchangeId: string }) => Promise<IPCResponse>;
    testExchangeConnection: (params: { exchangeId: string }) => Promise<IPCResponse>;
    getExchangeBalances: (params: { exchangeId: string }) => Promise<IPCResponse>;

    // ========================================================================================
    // Order Management Methods
    // ========================================================================================
    getCoins: () => Promise<IPCResponse<any[]>>;
    saveCoin: (coin: any) => Promise<IPCResponse>;
    deleteCoin: (coinId: string) => Promise<IPCResponse>;
    updateCoin: (params: { coinId: string; updates: any }) => Promise<IPCResponse>;
    // ========================================================================================
    getWalletBalance: () => Promise<IPCResponse>;
    rebalancePortfolio: (target: any) => Promise<IPCResponse>;
    getPortfolioOptimization: () => Promise<IPCResponse>;

    // ========================================================================================
    // Whale Tracking Extended Methods
    getCrossExchangeBalances: () => Promise<IPCResponse>;
    getExchangePortfolio: (params: { exchange: string }) => Promise<IPCResponse>;
    getRebalancingOpportunities: () => Promise<IPCResponse<any[]>>;
    rebalanceCrossExchangePortfolio: (config: any) => Promise<IPCResponse>;

    // ========================================================================================
    // Meme Coin Scanner Extended Methods
    // ========================================================================================
    startDCA: (config: any) => Promise<IPCResponse>;
    stopDCA: (dcaId: string) => Promise<IPCResponse>;
    getDCAPositions: () => Promise<IPCResponse<any[]>>;

    // ========================================================================================
    // Risk Management Methods
    getDCAHistory: (params: { dcaId: string }) => Promise<IPCResponse<any[]>>;
    updateDCAConfig: (params: { dcaId: string; config: any }) => Promise<IPCResponse>;

    // ========================================================================================
    // Exchange Management Methods
    // ========================================================================================
    getPnLReport: (params: { timeframe: string }) => Promise<IPCResponse>;
    getDrawdownAnalysis: () => Promise<IPCResponse>;
    // ========================================================================================
    getArbitragePositions: () => Promise<IPCResponse<any[]>>;
    getArbitrageStatus: () => Promise<IPCResponse>;
    startArbitrageEngine: () => Promise<IPCResponse>;

    // ========================================================================================
    // Coin Management Methods
    stopArbitrageEngine: () => Promise<IPCResponse>;
    updateArbitrageConfig: (config: any) => Promise<IPCResponse>;
    // ========================================================================================
    getOpportunityScannerStats: () => Promise<IPCResponse>;
    getDetectedOpportunities: () => Promise<IPCResponse<any[]>>;

    // ========================================================================================
    // Portfolio Extended Methods
    startOpportunityScanner: () => Promise<IPCResponse>;
    stopOpportunityScanner: () => Promise<IPCResponse>;
    updateOpportunityScannerConfig: (config: any) => Promise<IPCResponse>;
    // ========================================================================================
    createBackup: () => Promise<IPCResponse>;
    // ========================================================================================
    resetSettings: () => Promise<IPCResponse>;
    exportSettings: () => Promise<IPCResponse>;
    importSettings: (settings: any) => Promise<IPCResponse>;

    // ========================================================================================
    // DCA Trading Methods
    // ========================================================================================
    invoke?: (channel: string, ...args: any[]) => Promise<any>;
    on?: (channel: string, callback: (event: any, ...args: any[]) => void) => () => void;
    onArbitrageOpportunity?: (callback: (data: any) => void) => () => void;
    onArbitrageExecuted?: (callback: (data: any) => void) => () => void;
    // ========================================================================================
    getSystemStatus?: () => Promise<IPCResponse>;

    // ========================================================================================
    // Analytics Extended Methods
    getDatabaseStatus?: () => Promise<IPCResponse>;
    getDatabaseMetrics?: () => Promise<IPCResponse>;

    // ========================================================================================
    // Arbitrage Engine Methods

    stopAllGrids(): unknown;

    startAutonomousTrading(): unknown;

    stopAutonomousTrading(): unknown;

    getAutonomousStatus(): unknown;

    checkDatabaseReady(): unknown;

    // ========================================================================================
    // Opportunity Scanner Methods

    getCrossExchangeBalance(): unknown;

    updateGridBotConfig(config: any): unknown;

    generateDatabaseHealthReport(): unknown;

    getDatabaseStatistics(): unknown;

    storeTradingTransaction(transaction: any): unknown;

    // ========================================================================================
    // Backup & Recovery Methods

    getTradingTransactions(filters: any): unknown;

    // ========================================================================================
    // Settings Extended Methods

    updatePortfolioPosition(position: any): unknown;

    getPortfolioPositions(): unknown;

    storePerformanceMetrics(metrics: any): unknown;

    // ========================================================================================
    // Additional Methods Referenced in Components

    storeWhaleWallet(wallet: any): unknown;

    storeWhaleTransaction(transaction: any): unknown;

    getWhaleWallets(filters: any): unknown;

    storeTradingSignal(signal: any): unknown;

    // ========================================================================================
    // System Status Methods

    getTradingSignals(filters: any): unknown;

    getIPCErrorStatistics(): unknown;

    resetIPCErrorStatistics(): unknown;
}

declare global {
    interface Window {
        electronAPI: ElectronAPI;
    }
}
