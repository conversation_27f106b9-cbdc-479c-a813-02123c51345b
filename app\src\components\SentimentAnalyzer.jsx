// Import logger for consistent logging
import logger from '../utils/logger';

import React, {useCallback, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {
    Avatar,
    Box,
    Chip,
    CircularProgress,
    Grid,
    LinearProgress,
    List,
    ListItem,
    ListItemAvatar,
    ListItemText,
    Typography
} from '@mui/material';
import {LocalFireDepartment, Psychology, TrendingDown, TrendingUp} from '@mui/icons-material';
import HolographicCard from './HolographicCard';

const customWindow = window;
const electronAPI = {
    getSentiment: () => Promise.resolve({success: true, data: {}}),
    getTrendingTopics: () => Promise.resolve({success: true, data: []}),
    ...customWindow.electronAPI
};

const SentimentAnalyzer = () => {
    const [sentiment, setSentiment] = useState({
        overall: 50,
        bullish: 30,
        neutral: 40,
        bearish: 30
    });
    const [trendingTopics, setTrendingTopics] = useState([]);
    const [loading, setLoading] = useState(true);

    const fetchData = useCallback(async () => {
        try {
            const [sentimentData, topics] = await Promise.all([
                electronAPI.getSentiment(),
                electronAPI.getTrendingTopics()]);

            if (sentimentData?.success) {
                setSentiment(prev => ({...prev, ...sentimentData.data}));
            }
            if (topics?.success) {
                setTrendingTopics(topics.data || []);
            }
        } catch (error) {
            logger.error('Failed to fetch sentiment data:', error);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 45000); // Refresh every 45 seconds
        return () => clearInterval(interval);
    }, [fetchData]);

    const getSentimentColor = (score) => {
        if ((score || 0) >= 70) return '#4caf50';
        if ((score || 0) >= 40) return '#ffc107';
        return '#f44336';
    };

    const getSentimentIcon = (trend) => {
        switch (trend) {
            case 'bullish':
                return <TrendingUp sx={{color: '#4caf50'}}/>;
            case 'bearish':
                return <TrendingDown sx={{color: '#f44336'}}/>;
            default:
                return <Psychology sx={{color: '#ffc107'}}/>;
        }
    };

    if (loading) {
        return (
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh'}}>
                <CircularProgress/>
            </Box>
        );
    }

    return (
        <Box sx={{p: 3}}>
            <Typography variant="h4" sx={{color: '#ffc107', fontWeight: 800, mb: 3}}>
                <Psychology sx={{mr: 2}}/>
                Sentiment Analyzer
            </Typography>

            <Grid container spacing={3}>
                {/* Overall Sentiment */}
                <Grid item xs={12} md={6}>
                    <HolographicCard variant="gold" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#ffc107', mb: 2}}>
                            Overall Sentiment
                        </Typography>
                        <Box sx={{mb: 3}}>
                            <Typography variant="h3" sx={{
                                color: getSentimentColor(sentiment?.overall || 0),
                                fontWeight: 800,
                                mb: 1
                            }}>
                                {(sentiment?.overall || 0).toFixed(1)}%
                            </Typography>
                            <LinearProgress
                                variant="determinate"
                                value={sentiment?.overall || 0}
                                sx={{
                                    height: 10,
                                    borderRadius: 5,
                                    backgroundColor: 'rgba(255,255,255,0.1)',
                                    '& .MuiLinearProgress-bar': {
                                        backgroundColor: getSentimentColor(sentiment?.overall || 0),
                                        borderRadius: 5
                                    }
                                }}
                            />
                        </Box>
                    </HolographicCard>
                </Grid>

                {/* Sentiment Breakdown */}
                <Grid item xs={12} md={6}>
                    <HolographicCard variant="premium" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#00eaff', mb: 2}}>
                            Sentiment Breakdown
                        </Typography>
                        <Grid container spacing={2}>
                            {[
                                {label: 'Bullish', value: sentiment?.bullish || 0, color: '#4caf50'},
                                {label: 'Neutral', value: sentiment?.neutral || 0, color: '#ffc107'},
                                {label: 'Bearish', value: sentiment?.bearish || 0, color: '#f44336'}].map((item) => (
                                <Grid item xs={12} key={item.label}>
                                    <Box sx={{display: 'flex', justifyContent: 'space-between', mb: 1}}>
                                        <Typography variant="body2" sx={{color: '#888'}}>
                                            {item.label}
                                        </Typography>
                                        <Typography variant="body2" sx={{color: item.color, fontWeight: 600}}>
                                            {item.value}%
                                        </Typography>
                                    </Box>
                                    <LinearProgress
                                        variant="determinate"
                                        value={item.value}
                                        sx={{
                                            height: 6,
                                            borderRadius: 3,
                                            backgroundColor: 'rgba(255,255,255,0.1)',
                                            '& .MuiLinearProgress-bar': {
                                                backgroundColor: item.color,
                                                borderRadius: 3
                                            }
                                        }}
                                    />
                                </Grid>
                            ))}
                        </Grid>
                    </HolographicCard>
                </Grid>

                {/* Trending Topics */}
                <Grid item xs={12}>
                    <HolographicCard variant="secondary" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#a259ff', mb: 2}}>
                            <LocalFireDepartment sx={{mr: 1}}/>
                            Trending Topics ({trendingTopics.length})
                        </Typography>
                        <List>
                            {trendingTopics.map((topic, index) => (
                                <ListItem key={topic?.id || index} sx={{px: 0}}>
                                    <ListItemAvatar>
                                        <Avatar sx={{bgcolor: getSentimentColor(topic?.sentiment || 0)}}>
                                            {getSentimentIcon(topic?.trend || 'neutral')}
                                        </Avatar>
                                    </ListItemAvatar>
                                    <ListItemText
                                        primary={
                                            <Typography sx={{color: '#fff', fontWeight: 600}}>
                                                {topic?.title || 'Unknown Topic'}
                                            </Typography>
                                        }
                                        secondary={
                                            <Box sx={{
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                alignItems: 'center'
                                            }}>
                                                <Typography sx={{color: '#888', fontSize: '0.8rem'}}>
                                                    {topic?.mentions || 0} mentions
                                                </Typography>
                                                <Chip
                                                    label={`${topic?.sentiment || 0}%`}
                                                    size="small"
                                                    sx={{
                                                        backgroundColor: `${getSentimentColor(topic?.sentiment || 0)}20`,
                                                        color: getSentimentColor(topic?.sentiment || 0)
                                                    }}
                                                />
                                            </Box>
                                        }
                                    />
                                </ListItem>
                            ))}
                        </List>
                    </HolographicCard>
                </Grid>
            </Grid>
        </Box>
    );
};

SentimentAnalyzer.propTypes = {
    sentiment: PropTypes.object,
    trendingTopics: PropTypes.array
};

export default SentimentAnalyzer;