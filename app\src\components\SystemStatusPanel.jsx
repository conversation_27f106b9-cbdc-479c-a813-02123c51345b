import React, {useCallback, useEffect, useRef, useState} from 'react';
import PropTypes from 'prop-types';
import {
  <PERSON><PERSON>,
  Badge,
  Box,
  Card,
  CardContent,
  Chip,
  Collapse,
  Divider,
  Grid,
  IconButton,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography
} from '@mui/material';
import {
  AttachMoney,
  CheckCircle,
  Circle,
  Error as ErrorIcon,
  ExpandLess,
  ExpandMore,
  Memory,
  NetworkCheck,
  Refresh,
  Speed,
  Timer,
  TrendingUp
} from '@mui/icons-material';
import {AnimatePresence, motion} from 'framer-motion';
import realTimeStatusService from '../services/realTimeStatusService';

const SystemStatusPanel = ({
                               refreshInterval = 3000
                           }) => {
    const [status, setStatus] = useState({
        system: {
            isRunning: false,
            isInitialized: false,
            health: 'unknown',
            components: {},
            startupProgress: null,
            startupStatus: null,
            timestamp: null
        },
        trading: {
            activeBots: [],
            activeSignals: 0,
            pendingTrades: 0,
            performance: {}
        },
        operations: {
            systemMetrics: null
        }
    });
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [expanded, setExpanded] = useState(true);
    const [lastUpdate, setLastUpdate] = useState(null);
    const unsubscribeRefs = useRef([]);

    // Initialize real-time status service
    // Initialize real-time status service
    useEffect(() => {
        // Subscribe to various status updates
        const systemStatusUnsubscribe = realTimeStatusService.addListener('system-status', systemStatus => {
            setStatus(prevStatus => ({
                ...prevStatus,
                system: systemStatus
            }));
            setLastUpdate(new Date());
            setLoading(false);
            setError(null);
        });
        const startupStatusUnsubscribe = realTimeStatusService.addListener('startup-status', data => {
            setStatus(prevStatus => ({
                ...prevStatus,
                system: {
                    ...prevStatus.system,
                    startupStatus: data.message,
                    startupProgress: data.progress
                }
            }));
        });
        const componentStatusUnsubscribe = realTimeStatusService.addListener('component-status', data => {
            setStatus(prevStatus => ({
                ...prevStatus,
                system: {
                    ...prevStatus.system,
                    components: {
                        ...prevStatus.system.components,
                        [data.component]: {
                            status: data.status,
                            message: data.message,
                            lastUpdate: Date.now()
                        }
                    }
                }
            }));
        });
        const tradingUpdateUnsubscribe = realTimeStatusService.addListener('trading-update', data => {
            setStatus(prevStatus => ({
                ...prevStatus,
                trading: {
                    ...prevStatus.trading,
                    ...data
                }
            }));
        });

        // Store unsubscribe functions
        unsubscribeRefs.current = [systemStatusUnsubscribe, startupStatusUnsubscribe, componentStatusUnsubscribe, tradingUpdateUnsubscribe];

        // Start polling
        realTimeStatusService.startPolling(refreshInterval);

        // Initial status fetch
        realTimeStatusService.forceRefresh();
        return () => {
            // Clean up all subscriptions
            unsubscribeRefs.current.forEach(unsubscribe => unsubscribe());
            realTimeStatusService.stopPolling();
        };
    }, [refreshInterval]);
    const handleRefresh = useCallback(async () => {
        setLoading(true);
        try {
            await realTimeStatusService.forceUpdate();
        } catch (err) {
            setError(err.message || 'Failed to refresh status');
        } finally {
            setLoading(false);
        }
    }, []);


    const formatUptime = uptime => {
        if (!uptime) return 'N/A';
        const seconds = Math.floor(uptime / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);
        if (days > 0) {
            return `${days}d ${hours % 24}h`;
        } else if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    };
    const getComponentStatusColor = componentStatus => {
        switch (componentStatus) {
            case 'ready':
                return 'success';
            case 'initializing':
                return 'warning';
            case 'error':
            case 'not_initialized':
                return 'error';
            default:
                return 'default';
        }
    };

    const getTotalActiveOperations = () => {
        // Example: sum of all active operations
        const whaleSignals = status.operations.whaleSignals?.length || 0;
        const memeOpportunities = status.operations.memeOpportunities?.length || 0;
        const activeBots = status.trading.activeBots.length || 0;
        const activeSignals = status.trading.activeSignals || 0;
        const pendingTrades = status.trading.pendingTrades || 0;
        return whaleSignals + memeOpportunities + activeBots + activeSignals + pendingTrades;
    };

    const totalOperations = getTotalActiveOperations();

    if (loading && !status.system.timestamp) {
        return (
            <Card>
                <CardContent>
                    <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2}}>
                        <Typography variant="h6">System Status</Typography>
                        <motion.div
                            animate={{rotate: 360}}
                            transition={{duration: 1, repeat: Infinity, ease: 'linear'}}
                        >
                            <Circle sx={{fontSize: '1rem'}}/>
                        </motion.div>
                    </Box>
                    <LinearProgress/>
                </CardContent>
            </Card>
        );
    }
    if (error && !status.system.timestamp) {
        return (
            <Card>
                <CardContent>
                    <Typography variant="h6" gutterBottom>System Status</Typography>
                    <Alert
                        severity="error"
                        action={
                            <IconButton color="inherit" size="small" onClick={handleRefresh}>
                                <Refresh/>
                            </IconButton>
                        }
                    >
                        {error}
                    </Alert>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardContent>
                <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2}}>
                    <Typography variant="h6">
                        <Badge badgeContent={totalOperations} color="primary" max={99}>
                            System Status
                        </Badge>
                    </Typography>
                    <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                        {lastUpdate && (
                            <Typography variant="caption" color="text.secondary">
                                {lastUpdate.toLocaleTimeString()}
                            </Typography>
                        )}
                        <IconButton onClick={() => setExpanded(!expanded)} size="small">
                            {expanded ? <ExpandLess/> : <ExpandMore/>}
                        </IconButton>
                        <IconButton onClick={handleRefresh} size="small" disabled={loading}>
                            <Refresh/>
                        </IconButton>
                    </Box>
                </Box>
                <Collapse in={expanded}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                            <AnimatePresence>
                                <motion.div
                                    initial={{opacity: 0, y: -10}}
                                    animate={{opacity: 1, y: 0}}
                                    transition={{duration: 0.3}}
                                >
                                    <Box sx={{display: 'flex', alignItems: 'center', mb: 2}}>
                                        <Typography variant="subtitle1" sx={{mr: 1}}>
                                            Trading System:
                                        </Typography>
                                        <Chip
                                            icon={status.system.isRunning ? <CheckCircle color="success"/> :
                                                <ErrorIcon color="error"/>}
                                            label={status.system.isRunning ? 'Running' : 'Stopped'}
                                            color={status.system.isRunning ? 'success' : 'error'}
                                            size="small"
                                            sx={{mr: 2}}
                                        />
                                        <Typography variant="subtitle1" sx={{mr: 1}}>
                                            Uptime:
                                        </Typography>
                                        <Chip
                                            icon={<Timer/>}
                                            label={formatUptime(status.operations.systemMetrics?.uptime)}
                                            color="info"
                                            size="small"
                                        />
                                    </Box>
                                </motion.div>
                            </AnimatePresence>
                        </Grid>
                        <Grid item xs={12} md={6}>
                            <Typography variant="subtitle2" gutterBottom>
                                Real-time Activity
                            </Typography>
                            <List dense>
                                <ListItem>
                                    <ListItemIcon>
                                        <TrendingUp
                                            color={status.trading.activeBots.length > 0 ? 'success' : 'disabled'}/>
                                    </ListItemIcon>
                                    <ListItemText
                                        primary="Active Bots"
                                        secondary={
                                            <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                                <Typography
                                                    variant="body2">{status.trading.activeBots.length}</Typography>
                                                {status.trading.activeBots.length > 0 && (
                                                    <Chip label="Running" size="small" color="success"
                                                          variant="outlined"/>
                                                )}
                                            </Box>
                                        }
                                    />
                                </ListItem>
                                <ListItem>
                                    <ListItemIcon>
                                        <Speed color={status.trading.activeSignals > 0 ? 'warning' : 'disabled'}/>
                                    </ListItemIcon>
                                    <ListItemText
                                        primary="Active Signals"
                                        secondary={
                                            <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                                <Typography variant="body2">{status.trading.activeSignals}</Typography>
                                                {status.trading.activeSignals > 0 && (
                                                    <Chip label="Monitoring" size="small" color="warning"
                                                          variant="outlined"/>
                                                )}
                                            </Box>
                                        }
                                    />
                                </ListItem>
                                <ListItem>
                                    <ListItemIcon>
                                        <NetworkCheck color={status.trading.pendingTrades > 0 ? 'info' : 'disabled'}/>
                                    </ListItemIcon>
                                    <ListItemText
                                        primary="Pending Trades"
                                        secondary={
                                            <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                                <Typography variant="body2">{status.trading.pendingTrades}</Typography>
                                                {status.trading.pendingTrades > 0 && (
                                                    <Chip label="Processing" size="small" color="info"
                                                          variant="outlined"/>
                                                )}
                                            </Box>
                                        }
                                    />
                                </ListItem>
                                <ListItem>
                                    <ListItemIcon>
                                        <Memory
                                            color={status.operations.whaleSignals?.length > 0 ? 'primary' : 'disabled'}/>
                                    </ListItemIcon>
                                    <ListItemText
                                        primary="Whale Signals"
                                        secondary={
                                            <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                                <Typography
                                                    variant="body2">{status.operations.whaleSignals?.length || 0}</Typography>
                                                {status.operations.whaleSignals?.length > 0 && (
                                                    <Chip label="Active" size="small" color="primary"
                                                          variant="outlined"/>
                                                )}
                                            </Box>
                                        }
                                    />
                                </ListItem>
                                <ListItem>
                                    <ListItemIcon>
                                        <AttachMoney
                                            color={status.operations.memeOpportunities?.length > 0 ? 'secondary' : 'disabled'}/>
                                    </ListItemIcon>
                                    <ListItemText
                                        primary="Meme Opportunities"
                                        secondary={
                                            <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                                <Typography
                                                    variant="body2">{status.operations.memeOpportunities?.length || 0}</Typography>
                                                {status.operations.memeOpportunities?.length > 0 && (
                                                    <Chip label="Scanning" size="small" color="secondary"
                                                          variant="outlined"/>
                                                )}
                                            </Box>
                                        }
                                    />
                                </ListItem>
                            </List>
                        </Grid>
                        <Grid item xs={12}>
                            <Divider sx={{my: 2}}/>
                            <Typography variant="subtitle2" gutterBottom>
                                Active Trading Bots ({status.trading.activeBots.length})
                            </Typography>
                            {status.trading.activeBots.length > 0 ? (
                                <Box sx={{display: 'flex', flexWrap: 'wrap', gap: 1}}>
                                    <AnimatePresence>
                                        {status.trading.activeBots.slice(0, 6).map((bot, index) => (
                                            <motion.div
                                                key={bot.id || index}
                                                initial={{opacity: 0, scale: 0.8}}
                                                animate={{opacity: 1, scale: 1}}
                                                exit={{opacity: 0, scale: 0.8}}
                                                transition={{duration: 0.2}}
                                            >
                                                <Chip
                                                    label={`${bot.symbol || 'Unknown'} (${bot.exchange || 'N/A'})`}
                                                    color="primary"
                                                    size="small"
                                                    variant="outlined"
                                                />
                                            </motion.div>
                                        ))}
                                    </AnimatePresence>
                                    {status.trading.activeBots.length > 6 && (
                                        <Chip
                                            label={`+${status.trading.activeBots.length - 6} more`}
                                            color="default"
                                            size="small"
                                            variant="outlined"
                                        />
                                    )}
                                </Box>
                            ) : (
                                <Typography variant="body2" color="text.secondary">
                                    No active bots
                                </Typography>
                            )}
                        </Grid>
                        {status.system.components && Object.keys(status.system.components).length > 0 && (
                            <Grid item xs={12}>
                                <Divider sx={{my: 2}}/>
                                <Typography variant="subtitle2" gutterBottom>
                                    System Components
                                </Typography>
                                <Box sx={{display: 'flex', flexWrap: 'wrap', gap: 1}}>
                                    {Object.entries(status.system.components).map(([name, comp]) => (
                                        <Chip
                                            key={name}
                                            label={`${name}: ${comp.status}`}
                                            color={getComponentStatusColor(comp.status)}
                                            size="small"
                                            variant="outlined"
                                        />
                                    ))}
                                </Box>
                            </Grid>
                        )}
                        <Grid item xs={12}>
                            <Divider sx={{my: 2}}/>
                            <Typography variant="subtitle2" gutterBottom>
                                Performance Summary
                            </Typography>
                            <Box sx={{display: 'flex', flexWrap: 'wrap', gap: 1}}>
                                {status.trading.performance.totalTrades > 0 && (
                                    <Chip
                                        label={`${status.trading.performance.totalTrades} Trades`}
                                        color="info"
                                        size="small"
                                        variant="outlined"
                                    />
                                )}
                                {status.trading.performance.totalProfit && (
                                    <Chip
                                        label={`Profit: ${status.trading.performance.totalProfit}`}
                                        color="success"
                                        size="small"
                                        variant="outlined"
                                    />
                                )}
                                {status.trading.performance.winRate && (
                                    <Chip
                                        label={`${(status.trading.performance.winRate * 100).toFixed(1)}% Win Rate`}
                                        color="warning"
                                        size="small"
                                        variant="outlined"
                                    />
                                )}
                            </Box>
                        </Grid>
                    </Grid>
                </Collapse>
            </CardContent>
        </Card>
    );
};

SystemStatusPanel.propTypes = {
    refreshInterval: PropTypes.number
};

export default SystemStatusPanel;
