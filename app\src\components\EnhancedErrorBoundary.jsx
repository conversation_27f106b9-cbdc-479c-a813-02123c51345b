// Import logger for consistent logging
import logger from '../utils/logger';

import React from 'react';
import PropTypes from 'prop-types';
import ErrorBoundary from './ErrorBoundary';
import {ErrorReporter} from '../services/ErrorReporter';

/**
 * EnhancedErrorBoundary - Application-level error boundary with integrated error reporting
 *
 * Provides comprehensive error handling including:
 * - React component error boundaries
 * - Error reporting to external services
 * - Fallback UI components for different error types
 * - Error recovery mechanisms
 */
class EnhancedErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.errorReporter = new ErrorReporter();
        this.state = {
            errorCount: 0,
            lastError: null,
            errorHistory: []
        };
    }

    static getDerivedStateFromError(error) {
        return {
            hasError: true,
            error
        };
    }

    componentDidCatch(error, errorInfo) {
        // Log to console for development
        logger.error('EnhancedErrorBoundary caught an error:', error, errorInfo);

        // Update component state
        this.setState(prevState => ({
            errorCount: prevState.errorCount + 1,
            lastError: {
                error,
                errorInfo,
                timestamp: new Date().toISOString()
            },
            errorHistory: [...prevState.errorHistory, {
                error: error.message,
                stack: error.stack,
                componentStack: errorInfo.componentStack,
                timestamp: new Date().toISOString()
            }]
        }));

        // Report error to error reporting service
        this.reportError(error, errorInfo);
    }

    async reportError(error, errorInfo) {
        try {
            await this.errorReporter.report({
                type: 'react_error_boundary',
                message: error.message,
                stack: error.stack,
                componentStack: errorInfo.componentStack,
                url: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                userId: this.getCurrentUserId(),
                sessionId: this.getSessionId()
            });
        } catch (reportingError) {
            logger.error('Failed to report error:', reportingError);
        }
    }

    getCurrentUserId() {
        // Get user ID from auth context or localStorage
        try {
            const auth = JSON.parse(localStorage.getItem('auth') || '{}');
            return auth.user?.id || 'anonymous';
        } catch {
            return 'anonymous';
        }
    }

    getSessionId() {
        let sessionId = sessionStorage.getItem('sessionId');
        if (!sessionId) {
            sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            sessionStorage.setItem('sessionId', sessionId);
        }
        return sessionId;
    }

    handleErrorRecovery = () => {
        this.setState({
            hasError: false,
            error: null
        });
    };

    handleGoHome = () => {
        window.location.href = '/';
    };

    handleReload = () => {
        window.location.reload();
    };

    render() {
        const {hasError, error, errorInfo} = this.state;
        const {children, fallback: CustomFallback} = this.props;

        if (hasError) {
            if (CustomFallback) {
                return (
                    <CustomFallback
                        error={error}
                        errorInfo={errorInfo}
                        onRetry={this.handleErrorRecovery}
                        onGoHome={this.handleGoHome}
                        onReload={this.handleReload}
                    />
                );
            }

            // Use the standard ErrorBoundary with enhanced logging
            return (
                <ErrorBoundary
                    onError={this.handleErrorRecovery}
                    onGoHome={this.handleGoHome}
                >
                    {children}
                </ErrorBoundary>
            );
        }

        return children;
    }
}

EnhancedErrorBoundary.propTypes = {
    children: PropTypes.node.isRequired,
    fallback: PropTypes.elementType
};

export default EnhancedErrorBoundary;
