/**
 * @fileoverview Enhanced Startup Progress Panel
 * @description Shows detailed startup progress tracking in TradingOrchestrator
 */

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  CardContent,
  LinearProgress,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Alert,
  Collapse,
  IconButton
} from '@mui/material';
import {
  CheckCircle,
  RadioButtonUnchecked,
  Error,
  Warning,
  ExpandMore,
  ExpandLess,
  Settings,
  Storage,
  TrendingUp,
  Security,
  MonitorHeart
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

const STARTUP_STEPS = [
  {
    id: 'status-reporter',
    name: 'Status Reporter',
    description: 'Initializing status reporting system',
    icon: MonitorHeart,
    estimatedTime: 500
  },
  {
    id: 'configuration',
    name: 'Configuration Manager',
    description: 'Loading configuration and feature flags',
    icon: Settings,
    estimatedTime: 1000
  },
  {
    id: 'database',
    name: 'Database Initialization',
    description: 'Connecting to database and creating tables',
    icon: Storage,
    estimatedTime: 2000
  },
  {
    id: 'trading-components',
    name: 'Trading Components',
    description: 'Initializing AI trader, scanners, and analyzers',
    icon: TrendingUp,
    estimatedTime: 1500
  },
  {
    id: 'component-managers',
    name: 'Component Managers',
    description: 'Setting up alert, arbitrage, and grid managers',
    icon: Security,
    estimatedTime: 800
  }
];

const EnhancedStartupProgressPanel = ({ 
  isVisible = false, 
  onComplete = () => {},
  onError = () => {},
  autoStart = false 
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [stepStatus, setStepStatus] = useState({});
  const [overallProgress, setOverallProgress] = useState(0);
  const [isStarting, setIsStarting] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [errors, setErrors] = useState([]);
  const [expanded, setExpanded] = useState(true);
  const [startTime, setStartTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState(0);

  // Initialize step status
  useEffect(() => {
    const initialStatus = {};
    STARTUP_STEPS.forEach(step => {
      initialStatus[step.id] = {
        status: 'pending', // pending, running, completed, error
        message: '',
        startTime: null,
        endTime: null
      };
    });
    setStepStatus(initialStatus);
  }, []);

  // Auto-start if enabled
  useEffect(() => {
    if (autoStart && isVisible && !isStarting && !isComplete) {
      startStartupProcess();
    }
  }, [autoStart, isVisible, isStarting, isComplete]);

  // Update elapsed time
  useEffect(() => {
    let interval;
    if (isStarting && startTime) {
      interval = setInterval(() => {
        setElapsedTime(Date.now() - startTime);
      }, 100);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isStarting, startTime]);

  const startStartupProcess = async () => {
    if (isStarting) return;

    setIsStarting(true);
    setIsComplete(false);
    setErrors([]);
    setCurrentStep(0);
    setOverallProgress(0);
    setStartTime(Date.now());

    try {
      // Start the trading orchestrator via IPC
      if (window.electronAPI) {
        await simulateStartupProcess();
      } else {
        // Fallback simulation for testing
        await simulateStartupProcess();
      }
    } catch (error) {
      console.error('Startup process failed:', error);
      setErrors(prev => [...prev, error.message]);
      onError(error);
    }
  };

  const simulateStartupProcess = async () => {
    for (let i = 0; i < STARTUP_STEPS.length; i++) {
      const step = STARTUP_STEPS[i];
      setCurrentStep(i);

      // Update step status to running
      setStepStatus(prev => ({
        ...prev,
        [step.id]: {
          ...prev[step.id],
          status: 'running',
          startTime: Date.now(),
          message: step.description
        }
      }));

      try {
        // Simulate step execution
        await executeStep(step, i);

        // Update step status to completed
        setStepStatus(prev => ({
          ...prev,
          [step.id]: {
            ...prev[step.id],
            status: 'completed',
            endTime: Date.now(),
            message: `${step.name} initialized successfully`
          }
        }));

        // Update overall progress
        const progress = ((i + 1) / STARTUP_STEPS.length) * 100;
        setOverallProgress(progress);

      } catch (error) {
        // Update step status to error
        setStepStatus(prev => ({
          ...prev,
          [step.id]: {
            ...prev[step.id],
            status: 'error',
            endTime: Date.now(),
            message: error.message
          }
        }));

        setErrors(prev => [...prev, `${step.name}: ${error.message}`]);
        throw error;
      }
    }

    // Complete startup
    setIsComplete(true);
    setIsStarting(false);
    onComplete();
  };

  const executeStep = async (step, stepIndex) => {
    // Simulate actual IPC calls based on step
    switch (step.id) {
      case 'status-reporter':
        await new Promise(resolve => setTimeout(resolve, step.estimatedTime));
        break;
        
      case 'configuration':
        if (window.electronAPI) {
          await window.electronAPI.getConfig('risk-management');
        }
        await new Promise(resolve => setTimeout(resolve, step.estimatedTime));
        break;
        
      case 'database':
        if (window.electronAPI) {
          await window.electronAPI.getSystemHealth();
        }
        await new Promise(resolve => setTimeout(resolve, step.estimatedTime));
        break;
        
      case 'trading-components':
        if (window.electronAPI) {
          await window.electronAPI.getComponentHealth();
        }
        await new Promise(resolve => setTimeout(resolve, step.estimatedTime));
        break;
        
      case 'component-managers':
        if (window.electronAPI) {
          await window.electronAPI.getSystemStatus();
        }
        await new Promise(resolve => setTimeout(resolve, step.estimatedTime));
        break;
        
      default:
        await new Promise(resolve => setTimeout(resolve, step.estimatedTime));
    }
  };

  const getStepIcon = (step, status) => {
    const IconComponent = step.icon;
    
    switch (status?.status) {
      case 'completed':
        return <CheckCircle color="success" />;
      case 'running':
        return <IconComponent color="primary" className="animate-pulse" />;
      case 'error':
        return <Error color="error" />;
      default:
        return <RadioButtonUnchecked color="disabled" />;
    }
  };

  const getStepColor = (status) => {
    switch (status?.status) {
      case 'completed':
        return 'success';
      case 'running':
        return 'primary';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatTime = (ms) => {
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getTotalEstimatedTime = () => {
    return STARTUP_STEPS.reduce((total, step) => total + step.estimatedTime, 0);
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card sx={{ maxWidth: 600, margin: 'auto', mt: 2 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6" component="h2">
              Trading System Startup
            </Typography>
            <Box display="flex" alignItems="center" gap={1}>
              {startTime && (
                <Chip 
                  label={`${formatTime(elapsedTime)} / ${formatTime(getTotalEstimatedTime())}`}
                  size="small"
                  variant="outlined"
                />
              )}
              <IconButton
                onClick={() => setExpanded(!expanded)}
                size="small"
              >
                {expanded ? <ExpandLess /> : <ExpandMore />}
              </IconButton>
            </Box>
          </Box>

          {/* Overall Progress */}
          <Box mb={3}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
              <Typography variant="body2" color="textSecondary">
                Overall Progress
              </Typography>
              <Typography variant="body2" color="textSecondary">
                {Math.round(overallProgress)}%
              </Typography>
            </Box>
            <LinearProgress 
              variant="determinate" 
              value={overallProgress}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </Box>

          {/* Current Status */}
          {isStarting && currentStep < STARTUP_STEPS.length && (
            <Alert 
              severity="info" 
              sx={{ mb: 2 }}
              icon={<STARTUP_STEPS[currentStep].icon />}
            >
              {STARTUP_STEPS[currentStep].description}
            </Alert>
          )}

          {/* Completion Status */}
          {isComplete && (
            <Alert severity="success" sx={{ mb: 2 }}>
              Trading system startup completed successfully!
            </Alert>
          )}

          {/* Error Messages */}
          {errors.length > 0 && (
            <Alert severity="error" sx={{ mb: 2 }}>
              <Typography variant="body2" fontWeight="bold">
                Startup Errors:
              </Typography>
              {errors.map((error, index) => (
                <Typography key={index} variant="body2">
                  • {error}
                </Typography>
              ))}
            </Alert>
          )}

          {/* Detailed Steps */}
          <Collapse in={expanded}>
            <List dense>
              {STARTUP_STEPS.map((step, index) => {
                const status = stepStatus[step.id];
                const isActive = index === currentStep && isStarting;
                
                return (
                  <motion.div
                    key={step.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <ListItem
                      sx={{
                        bgcolor: isActive ? 'action.hover' : 'transparent',
                        borderRadius: 1,
                        mb: 0.5
                      }}
                    >
                      <ListItemIcon>
                        {getStepIcon(step, status)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box display="flex" alignItems="center" gap={1}>
                            <Typography variant="body2" fontWeight="medium">
                              {step.name}
                            </Typography>
                            <Chip
                              label={status?.status || 'pending'}
                              size="small"
                              color={getStepColor(status)}
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" color="textSecondary">
                              {status?.message || step.description}
                            </Typography>
                            {status?.startTime && status?.endTime && (
                              <Typography variant="caption" color="textSecondary" display="block">
                                Completed in {formatTime(status.endTime - status.startTime)}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                    </ListItem>
                  </motion.div>
                );
              })}
            </List>
          </Collapse>

          {/* Action Buttons */}
          <Box display="flex" justifyContent="center" gap={2} mt={2}>
            {!isStarting && !isComplete && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={startStartupProcess}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#1976d2',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Start Trading System
              </motion.button>
            )}
            
            {errors.length > 0 && !isStarting && (
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={startStartupProcess}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#f57c00',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Retry Startup
              </motion.button>
            )}
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};

EnhancedStartupProgressPanel.propTypes = {
  isVisible: PropTypes.bool,
  onComplete: PropTypes.func,
  onError: PropTypes.func,
  autoStart: PropTypes.bool
};

export default EnhancedStartupProgressPanel;