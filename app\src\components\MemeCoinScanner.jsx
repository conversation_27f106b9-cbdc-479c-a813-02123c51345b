// Import logger for consistent logging
import logger from '../utils/logger';

import React, {useCallback, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {
  Avatar,
  Box,
  Chip,
  CircularProgress,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography
} from '@mui/material';
import {LocalFireDepartment, TrendingDown, TrendingUp} from '@mui/icons-material';
import HolographicCard from './HolographicCard';

// Safe electronAPI with fallbacks
const customWindow = window;
const electronAPI = {
    getOpportunities: () => Promise.resolve({success: true, data: []}),
    getHistory: () => Promise.resolve({success: true, data: []}),
    getStatus: () => Promise.resolve({success: true, data: {isActive: false, lastScan: null}}),
    startScanning: () => Promise.resolve({success: true}),
    stopScanning: () => Promise.resolve({success: true}),
    ...customWindow.electronAPI
};

const MemeCoinScanner = () => {
    const [opportunities, setOpportunities] = useState([]);
    const [history, setHistory] = useState([]);
    const [status, setStatus] = useState({isActive: false, lastScan: null});
    const [loading, setLoading] = useState(true);

    const fetchData = useCallback(async () => {
        try {
            const [opps, hist, stat] = await Promise.all([
                electronAPI.getOpportunities?.() || Promise.resolve({success: true, data: []}),
                electronAPI.getHistory?.() || Promise.resolve({success: true, data: []}),
                electronAPI.getStatus?.() || Promise.resolve({
                    success: true,
                    data: {isActive: false, lastScan: null}
                })]);

            if (opps?.success) setOpportunities(opps.data || []);
            if (hist?.success) setHistory(hist.data || []);
            if (stat?.success) setStatus(stat.data || {isActive: false, lastScan: null});
        } catch (error) {
            logger.error('Failed to fetch meme coin data:', error);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 30000); // Refresh every 30 seconds
        return () => clearInterval(interval);
    }, [fetchData]);

    const getTrendIcon = (trend) => {
        switch (trend) {
            case 'up':
                return <TrendingUp sx={{color: '#4caf50'}}/>;
            case 'down':
                return <TrendingDown sx={{color: '#f44336'}}/>;
            default:
                return <LocalFireDepartment sx={{color: '#ff9800'}}/>;
        }
    };

    const getTrendColor = (trend) => {
        switch (trend) {
            case 'up':
                return '#4caf50';
            case 'down':
                return '#f44336';
            default:
                return '#ff9800';
        }
    };

    if (loading) {
        return (
            <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh'}}>
                <CircularProgress/>
            </Box>
        );
    }

    return (
        <Box sx={{p: 3}}>
            <Typography variant="h4" sx={{color: '#ff9800', fontWeight: 800, mb: 3}}>
                <LocalFireDepartment sx={{mr: 2}}/>
                Meme Coin Scanner
            </Typography>

            <Grid container spacing={3}>
                {/* Scanner Status */}
                <Grid item xs={12} md={4}>
                    <HolographicCard variant="gold" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#ffc107', mb: 2}}>
                            Scanner Status
                        </Typography>
                        <Chip
                            label={status?.isActive ? 'Active' : 'Inactive'}
                            color={status?.isActive ? 'success' : 'default'}
                            sx={{mb: 2}}
                        />
                        <Typography variant="body2" sx={{color: '#888'}}>
                            Last scan: {status?.lastScan ? new Date(status.lastScan).toLocaleString() : 'Never'}
                        </Typography>
                    </HolographicCard>
                </Grid>

                {/* Current Opportunities */}
                <Grid item xs={12} md={8}>
                    <HolographicCard variant="premium" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#00eaff', mb: 2}}>
                            Current Opportunities ({opportunities.length})
                        </Typography>
                        <TableContainer>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell sx={{color: '#00eaff'}}>Coin</TableCell>
                                        <TableCell sx={{color: '#00eaff'}}>Price</TableCell>
                                        <TableCell sx={{color: '#00eaff'}}>24h Change</TableCell>
                                        <TableCell sx={{color: '#00eaff'}}>Volume</TableCell>
                                        <TableCell sx={{color: '#00eaff'}}>Trend</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {opportunities.map((coin, index) => (
                                        <TableRow key={coin?.symbol || `coin-${index}`}>
                                            <TableCell>
                                                <Box sx={{display: 'flex', alignItems: 'center'}}>
                                                    <Avatar sx={{mr: 1, bgcolor: getTrendColor(coin?.trend)}}>
                                                        {(coin?.symbol || '??').slice(0, 2)}
                                                    </Avatar>
                                                    <Typography
                                                        sx={{color: '#fff'}}>{coin?.symbol || 'Unknown'}</Typography>
                                                </Box>
                                            </TableCell>
                                            <TableCell sx={{color: '#fff'}}>${(coin?.price || 0).toFixed(6)}</TableCell>
                                            <TableCell
                                                sx={{color: (coin?.change24h || 0) >= 0 ? '#4caf50' : '#f44336'}}>
                                                {(coin?.change24h || 0) > 0 ? '+' : ''}{(coin?.change24h || 0).toFixed(2)}%
                                            </TableCell>
                                            <TableCell
                                                sx={{color: '#fff'}}>${(coin?.volume || 0).toLocaleString()}</TableCell>
                                            <TableCell>{getTrendIcon(coin?.trend)}</TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </HolographicCard>
                </Grid>

                {/* Scanning History */}
                <Grid item xs={12}>
                    <HolographicCard variant="secondary" elevation="medium" sx={{p: 3}}>
                        <Typography variant="h6" sx={{color: '#a259ff', mb: 2}}>
                            Scanning History ({history.length})
                        </Typography>
                        <TableContainer>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell sx={{color: '#a259ff'}}>Timestamp</TableCell>
                                        <TableCell sx={{color: '#a259ff'}}>Coins Found</TableCell>
                                        <TableCell sx={{color: '#a259ff'}}>Top Gainer</TableCell>
                                        <TableCell sx={{color: '#a259ff'}}>Status</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {history.map((scan, index) => (
                                        <TableRow key={index}>
                                            <TableCell sx={{color: '#fff'}}>
                                                {new Date(scan?.timestamp || Date.now()).toLocaleString()}
                                            </TableCell>
                                            <TableCell sx={{color: '#fff'}}>{scan?.coinsFound || 0}</TableCell>
                                            <TableCell sx={{color: '#fff'}}>{scan?.topGainer || 'N/A'}</TableCell>
                                            <TableCell>
                                                <Chip
                                                    label={scan?.status || 'unknown'}
                                                    size="small"
                                                    color={(scan?.status || 'unknown') === 'completed' ? 'success' : 'warning'}
                                                />
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </HolographicCard>
                </Grid>
            </Grid>
        </Box>
    );
};

MemeCoinScanner.propTypes = {
    opportunities: PropTypes.array,
    history: PropTypes.array,
    status: PropTypes.object
};

export default MemeCoinScanner;