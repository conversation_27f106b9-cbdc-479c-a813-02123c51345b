#!/usr/bin/env node

/**
 * Environment Validation Script
 * Validates environment configuration for production deployment
 */

const fs = require('fs');
const path = require('path');

// Simple color functions using ANSI codes
const colors = {
    blue: (text) => `\x1b[34m${text}\x1b[0m`,
    green: (text) => `\x1b[32m${text}\x1b[0m`,
    yellow: (text) => `\x1b[33m${text}\x1b[0m`,
    red: (text) => `\x1b[31m${text}\x1b[0m`,
    reset: '\x1b[0m'
};

class EnvironmentValidator {
    constructor() {
        this.errors = [];
        this.warnings = [];
        this.checks = [];
    }

    log(message, type = 'info') {
        let coloredPrefix;
        switch (type) {
            case 'success':
                coloredPrefix = colors.green(`[${type.toUpperCase()}]`);
                break;
            case 'warning':
                coloredPrefix = colors.yellow(`[${type.toUpperCase()}]`);
                break;
            case 'error':
                coloredPrefix = colors.red(`[${type.toUpperCase()}]`);
                break;
            default:
                coloredPrefix = colors.blue(`[${type.toUpperCase()}]`);
        }
        
        console.log(`${coloredPrefix} ${message}`);
    }

    addCheck(name, passed, message) {
        this.checks.push({ name, passed, message });
        if (passed) {
            this.log(`✓ ${name}: ${message}`, 'success');
        } else {
            this.log(`✗ ${name}: ${message}`, 'error');
            this.errors.push(`${name}: ${message}`);
        }
    }

    addWarning(name, message) {
        this.checks.push({ name, passed: false, message, isWarning: true });
        this.log(`⚠ ${name}: ${message}`, 'warning');
        this.warnings.push(`${name}: ${message}`);
    }

    validateNodeVersion() {
        this.log('Validating Node.js version...', 'info');
        
        const currentVersion = process.version;
        const requiredVersion = '18.0.0';
        
        const isValid = this.isVersionCompatible(currentVersion, requiredVersion);
        this.addCheck('Node.js Version', isValid,
            isValid ? `${currentVersion} (compatible)` : `${currentVersion} (requires ${requiredVersion}+)`);
    }

    isVersionCompatible(current, required) {
        const currentParts = current.replace('v', '').split('.').map(Number);
        const requiredParts = required.split('.').map(Number);
        
        for (let i = 0; i < requiredParts.length; i++) {
            if (currentParts[i] > requiredParts[i]) return true;
            if (currentParts[i] < requiredParts[i]) return false;
        }
        return true;
    }

    validateEnvironmentFiles() {
        this.log('Validating environment files...', 'info');
        
        const envFiles = [
            '.env',
            '.env.production',
            '.env.staging'
        ];

        envFiles.forEach(file => {
            const filePath = path.join(__dirname, '..', file);
            const exists = fs.existsSync(filePath);
            
            if (file === '.env.production') {
                this.addCheck(`Environment File: ${file}`, exists,
                    exists ? 'Production environment file found' : 'Production environment file missing');
            } else {
                const status = exists ? 'Found' : 'Missing (optional)';
                this.addCheck(`Environment File: ${file}`, true, status);
            }
        });
    }

    validateEnvironmentVariables() {
        this.log('Validating environment variables...', 'info');
        
        // Load production environment
        const prodEnvPath = path.join(__dirname, '..', '.env.production');
        if (fs.existsSync(prodEnvPath)) {
            const envContent = fs.readFileSync(prodEnvPath, 'utf8');
            const envVars = this.parseEnvFile(envContent);
            
            // Required variables for production
            const requiredVars = [
                'NODE_ENV',
                'REACT_APP_VERSION',
                'REACT_APP_API_BASE_URL',
                'REACT_APP_ENABLE_ERROR_REPORTING'
            ];

            requiredVars.forEach(varName => {
                const hasVar = envVars.hasOwnProperty(varName) && envVars[varName];
                this.addCheck(`Required Variable: ${varName}`, hasVar,
                    hasVar ? `Set to: ${envVars[varName]}` : 'Not set or empty');
            });

            // Validate specific values
            if (envVars.NODE_ENV !== 'production') {
                this.addWarning('NODE_ENV Value', 'Should be set to "production" for production builds');
            }

            if (envVars.REACT_APP_DEBUG_MODE === 'true') {
                this.addWarning('Debug Mode', 'Debug mode is enabled in production environment');
            }

            if (envVars.REACT_APP_ENABLE_CONSOLE_LOGS === 'true') {
                this.addWarning('Console Logs', 'Console logging is enabled in production environment');
            }
        }
    }

    parseEnvFile(content) {
        const envVars = {};
        const lines = content.split('\n');
        
        lines.forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#')) {
                const [key, ...valueParts] = line.split('=');
                if (key && valueParts.length > 0) {
                    envVars[key.trim()] = valueParts.join('=').trim();
                }
            }
        });
        
        return envVars;
    }

    validatePackageJson() {
        this.log('Validating package.json...', 'info');
        
        const packagePath = path.join(__dirname, '..', 'package.json');
        if (!fs.existsSync(packagePath)) {
            this.addCheck('package.json', false, 'File not found');
            return;
        }

        try {
            const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            
            // Check required fields
            const requiredFields = ['name', 'version', 'main', 'scripts'];
            requiredFields.forEach(field => {
                const hasField = packageJson.hasOwnProperty(field);
                this.addCheck(`package.json ${field}`, hasField,
                    hasField ? 'Present' : 'Missing');
            });

            // Check required scripts
            const requiredScripts = [
                'build:production',
                'start-production',
                'dist'
            ];

            requiredScripts.forEach(script => {
                const hasScript = packageJson.scripts && packageJson.scripts[script];
                this.addCheck(`Script: ${script}`, hasScript,
                    hasScript ? 'Defined' : 'Missing');
            });

            // Check dependencies
            if (!packageJson.dependencies) {
                this.addWarning('Dependencies', 'No dependencies defined');
            } else {
                const depCount = Object.keys(packageJson.dependencies).length;
                this.addCheck('Dependencies', depCount > 0, `${depCount} dependencies found`);
            }

        } catch (error) {
            this.addCheck('package.json Parse', false, `Parse error: ${error.message}`);
        }
    }

    validateSystemRequirements() {
        this.log('Validating system requirements...', 'info');
        
        // Check available memory
        const totalMemory = require('os').totalmem();
        const totalMemoryGB = Math.round(totalMemory / (1024 * 1024 * 1024));
        const memoryOk = totalMemoryGB >= 4;
        
        this.addCheck('System Memory', memoryOk,
            `${totalMemoryGB}GB ${memoryOk ? '(sufficient)' : '(minimum 4GB required)'}`);

        // Check available disk space
        try {
            const stats = fs.statSync(__dirname);
            // This is a simplified check - in production you'd want to check actual disk space
            this.addCheck('Disk Space', true, 'Available (detailed check recommended)');
        } catch (error) {
            this.addWarning('Disk Space', 'Could not verify disk space');
        }

        // Check platform
        const platform = process.platform;
        const supportedPlatforms = ['win32', 'darwin', 'linux'];
        const platformSupported = supportedPlatforms.includes(platform);
        
        this.addCheck('Platform Support', platformSupported,
            `${platform} ${platformSupported ? '(supported)' : '(may not be supported)'}`);
    }

    validateBuildTools() {
        this.log('Validating build tools...', 'info');
        
        // Check if webpack is available
        try {
            require.resolve('webpack');
            this.addCheck('Webpack', true, 'Available');
        } catch (error) {
            this.addCheck('Webpack', false, 'Not found in dependencies');
        }

        // Check if electron is available
        try {
            require.resolve('electron');
            this.addCheck('Electron', true, 'Available');
        } catch (error) {
            this.addCheck('Electron', false, 'Not found in dependencies');
        }

        // Check if babel is available
        try {
            require.resolve('@babel/core');
            this.addCheck('Babel', true, 'Available');
        } catch (error) {
            this.addCheck('Babel', false, 'Not found in dependencies');
        }
    }

    validateConfiguration() {
        this.log('Validating configuration files...', 'info');
        
        const configFiles = [
            'webpack.config.js',
            'webpack.config.production.js',
            'electron-builder.config.js'
        ];

        configFiles.forEach(file => {
            const filePath = path.join(__dirname, '..', file);
            const exists = fs.existsSync(filePath);
            this.addCheck(`Config File: ${file}`, exists,
                exists ? 'Found' : 'Missing');
        });
    }

    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development',
            nodeVersion: process.version,
            platform: process.platform,
            summary: {
                totalChecks: this.checks.length,
                passed: this.checks.filter(c => c.passed && !c.isWarning).length,
                failed: this.checks.filter(c => !c.passed && !c.isWarning).length,
                warnings: this.checks.filter(c => c.isWarning).length
            },
            checks: this.checks,
            errors: this.errors,
            warnings: this.warnings,
            ready: this.errors.length === 0
        };

        const reportPath = path.join(__dirname, '..', 'environment-validation-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        return report;
    }

    async run() {
        this.log('Starting environment validation...', 'info');
        
        this.validateNodeVersion();
        this.validateEnvironmentFiles();
        this.validateEnvironmentVariables();
        this.validatePackageJson();
        this.validateSystemRequirements();
        this.validateBuildTools();
        this.validateConfiguration();
        
        const report = this.generateReport();
        
        this.log('\n=== ENVIRONMENT VALIDATION SUMMARY ===', 'info');
        this.log(`Total Checks: ${report.summary.totalChecks}`, 'info');
        this.log(`Passed: ${report.summary.passed}`, 'success');
        this.log(`Failed: ${report.summary.failed}`, report.summary.failed > 0 ? 'error' : 'info');
        this.log(`Warnings: ${report.summary.warnings}`, report.summary.warnings > 0 ? 'warning' : 'info');
        
        if (report.ready) {
            this.log('\n✅ Environment is ready for production deployment!', 'success');
            return true;
        } else {
            this.log('\n❌ Environment has issues that need to be addressed.', 'error');
            return false;
        }
    }
}

// Run if called directly
if (require.main === module) {
    const validator = new EnvironmentValidator();
    validator.run().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = EnvironmentValidator;