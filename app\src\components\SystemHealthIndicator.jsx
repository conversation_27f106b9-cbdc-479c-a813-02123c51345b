import React, {useEffect, useState} from 'react';
import systemWideErrorHandler from '../utils/SystemWideErrorHandler';

/**
 * System Health Indicator Component
 * Shows the current system health status and error statistics
 */
const SystemHealthIndicator = () => {
    const [healthData, setHealthData] = useState({
        health: 'healthy',
        errorStats: {totalErrors: 0, criticalErrors: 0, recoveredErrors: 0},
        circuitBreaker: {isOpen: false, failureCount: 0},
        errorBoundaries: 0,
        timestamp: new Date().toISOString()
    });
    const [isVisible, setIsVisible] = useState(false);
    const [componentStatuses, setComponentStatuses] = useState(new Map());

    useEffect(() => {
        // Update health data periodically
        const updateHealth = () => {
            if (systemWideErrorHandler.initialized) {
                const health = systemWideErrorHandler.getSystemHealth();
                setHealthData(health);

                // Show indicator if there are issues
                setIsVisible(
                    health.health !== 'healthy' ||
                    health.errorStats.totalErrors > 0 ||
                    health.circuitBreaker.isOpen,
                );
            }
        };

        // Initial update
        updateHealth();

        // Set up periodic updates
        const interval = setInterval(updateHealth, 5000);

        // Listen for system events
        const handleSystemEvent = (event) => {
            const {eventType, data} = event.detail;

            switch (eventType) {
                case 'system-health-changed':
                case 'circuit-breaker-activated':
                case 'circuit-breaker-reset':
                    updateHealth();
                    setIsVisible(true);
                    break;
                case 'component-degraded':
                    setComponentStatuses(prev => new Map(prev).set(data.componentName, 'degraded'));
                    break;
                default:
                    break;
            }
        };

        window.addEventListener('system-event', handleSystemEvent);

        // Listen for component errors
        const handleComponentError = (event) => {
            const {componentName} = event.detail;
            setComponentStatuses(prev => new Map(prev).set(componentName, 'failed'));
            setIsVisible(true);
        };

        window.addEventListener('componentError', handleComponentError);

        return () => {
            clearInterval(interval);
            window.removeEventListener('system-event', handleSystemEvent);
            window.removeEventListener('componentError', handleComponentError);
        };
    }, []);

    if (!isVisible && healthData.health === 'healthy') {
        return null;
    }

    // Removed unused getHealthIcon function

    const getHealthText = () => {
        if (healthData.circuitBreaker.isOpen) {
            return 'Circuit Breaker Active';
        }

        switch (healthData.health) {
            case 'healthy':
                return 'System Healthy';
            case 'degraded':
                return 'System Degraded';
            case 'critical':
                return 'System Critical';
            default:
                return 'System Unknown';
        }
    };

    const hasErrors = componentStatuses.size > 0 || healthData.errorStats.totalErrors > 0;

    return (
        <>
            {/* Main health indicator */}
            <div className={`system-health-indicator ${healthData.health}`}>
                <div className="health-dot"></div>
                <span>{getHealthText()}</span>
                {healthData.errorStats.totalErrors > 0 && (
                    <span className="error-count">({healthData.errorStats.totalErrors})</span>
                )}
            </div>

            {/* Component status grid (shown when there are errors) */}
            {hasErrors && (
                <div className={`component-status-grid ${hasErrors ? 'has-errors' : ''}`}>
                    <h4>Component Status</h4>

                    {/* Show error statistics */}
                    {healthData.errorStats.totalErrors > 0 && (
                        <div className="error-stats">
                            <div className="component-status-item">
                                <span>Total Errors:</span>
                                <span>{healthData.errorStats.totalErrors}</span>
                            </div>
                            <div className="component-status-item">
                                <span>Critical:</span>
                                <span>{healthData.errorStats.criticalErrors}</span>
                            </div>
                            <div className="component-status-item">
                                <span>Recovered:</span>
                                <span>{healthData.errorStats.recoveredErrors}</span>
                            </div>
                        </div>
                    )}

                    {/* Show component statuses */}
                    {Array.from(componentStatuses.entries()).map(([componentName, status]) => (
                        <div key={componentName} className="component-status-item">
                            <span>{componentName}</span>
                            <div className={`status-dot ${status}`}></div>
                        </div>
                    ))}

                    {/* Circuit breaker status */}
                    {healthData.circuitBreaker.isOpen && (
                        <div className="component-status-item">
                            <span>Circuit Breaker:</span>
                            <div className="status-dot failed"></div>
                        </div>
                    )}

                    {/* Error boundaries count */}
                    <div className="component-status-item">
                        <span>Error Boundaries:</span>
                        <span>{healthData.errorBoundaries}</span>
                    </div>
                </div>
            )}

            {/* Circuit breaker modal */}
            {healthData.circuitBreaker.isOpen && (
                <CircuitBreakerModal
                    failureCount={healthData.circuitBreaker.failureCount}
                    onClose={() => setIsVisible(false)}
                />
            )}
        </>
    );
};

/**
 * Circuit Breaker Modal Component
 */
const CircuitBreakerModal = ({failureCount, onClose}) => {
    const [countdown, setCountdown] = useState(300); // 5 minutes

    useEffect(() => {
        const interval = setInterval(() => {
            setCountdown(prev => {
                if (prev <= 1) {
                    clearInterval(interval);
                    onClose();
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(interval);
    }, [onClose]);

    const formatTime = (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    return (
        <div className="circuit-breaker-indicator active">
            <h2>🔴 Circuit Breaker Activated</h2>
            <p>
                System has detected {failureCount} critical failures and has entered safe mode.
                Normal operations will resume automatically.
            </p>
            <div className="countdown">
                {formatTime(countdown)}
            </div>
            <p style={{fontSize: '12px', marginTop: '15px', opacity: '0.7'}}>
                The system will attempt to recover automatically when the timeout expires.
            </p>
        </div>
    );
};

export default SystemHealthIndicator;