{"name": "@jest/reporters", "description": "<PERSON><PERSON>'s reporters", "version": "29.7.0", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "@types/node": "*", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^6.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.1.3", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "slash": "^3.0.0", "string-length": "^4.0.1", "strip-ansi": "^6.0.0", "v8-to-istanbul": "^9.0.1"}, "devDependencies": {"@jest/test-utils": "^29.7.0", "@tsd/typescript": "^5.0.4", "@types/exit": "^0.1.30", "@types/glob": "^7.1.1", "@types/graceful-fs": "^4.1.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-lib-instrument": "^1.7.2", "@types/istanbul-lib-report": "^3.0.0", "@types/istanbul-lib-source-maps": "^4.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node-notifier": "^8.0.0", "jest-resolve": "^29.7.0", "mock-fs": "^5.1.2", "node-notifier": "^10.0.0", "tsd-lite": "^0.7.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-reporters"}, "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "homepage": "https://jestjs.io/", "license": "MIT", "publishConfig": {"access": "public"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630"}