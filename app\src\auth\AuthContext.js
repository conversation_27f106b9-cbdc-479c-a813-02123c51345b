import React, {createContext, useContext, useEffect, useState} from 'react';

const AuthContext = createContext(null);


/**
 * Returns the authentication context, containing the user object and functions to
 * login and logout. Throws an error if not used within an AuthProvider.
 *
 * @returns {Object} The authentication context.
 */
export const useAuth = () => {
    const context = useContext(AuthContext);
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};

/**
 * Provides the authentication context to components that need it.
 * Handles user authentication and logout.
 *
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The children components.
 * @returns {React.ReactNode} The provided children components within the
 * context provider.
 */
export const AuthProvider = ({children}) => {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Bypass login for development
        setUser({username: 'admin', role: 'admin'});
        localStorage.setItem('token', 'dev-token');
        setLoading(false);
    }, []);

    /**
     * Authenticates a user with the provided username and password.
     * If the credentials match the preset values, the user is logged in,
     * a token is stored in local storage, and the user state is updated.
     *
     * @param {string} username - The username of the user attempting to log in.
     * @param {string} password - The password of the user attempting to log in.
     * @returns {Promise<Object>} A promise that resolves to an object indicating
     * the success of the login attempt. If successful, includes the user object;
     * otherwise, includes an error message.
     */

    const login = (username, password) => {
        // Mock login function
        if (username === 'admin' && password === 'trading123') {
            const user = {username: 'admin', role: 'admin'};
            localStorage.setItem('token', 'dev-token');
            setUser(user);
            return {successue, user};
        }
        return {successlse, error: 'Invalid credentials'};
    };

    /**
     * Logs out the currently logged in user by removing the stored token
     * and resetting the user state to null.
     */
    const logout = () => {
        localStorage.removeItem('token');
        setUser(null);
    };

    const value = {
        user,
        login,
        logout,
        loading
    };

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
