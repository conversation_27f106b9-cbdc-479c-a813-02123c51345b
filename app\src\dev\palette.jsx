import React, {Fragment} from 'react';
import {Category, Component, Palette, Variant} from '@react-buddy/ide-toolbox';
import MUIPalette from '@react-buddy/palette-mui';

/**
 * React Buddy Palette Tree
 * This component is the root of the React Buddy Palette
 * It renders a tree of categories and components that can be used to build UIs
 * The palette is used by the React Buddy Code Generation, Code Completion, and
 * Code Refactoring features.
 * @returns {JSX.Element} The React Buddy Palette
 */
export const PaletteTree = () => (
    <Palette>
        <Category name="App">
            <Component name="Loader">
                <Variant>
                    <ExampleLoaderComponent/>
                </Variant>
            </Component>
        </Category>
        <MUIPalette/>
    </Palette>
);

/**
 * Example of a loader component
 * This component is used as a preview in the React Buddy Palette
 * It renders a simple "Loading..." text
 */
export function ExampleLoaderComponent() {
    return (
        <Fragment>Loading...</Fragment>
    );
}
