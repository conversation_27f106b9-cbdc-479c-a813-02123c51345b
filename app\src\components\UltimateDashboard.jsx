/**
 * @fileoverview Ultimate Trading Dashboard Component
 * @description Advanced quantum elite trading matrix dashboard with comprehensive
 * portfolio overview, bot management, and real-time market analysis.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 2024-01-01
 */

import React, {useCallback, useEffect, useState} from 'react';
import PropTypes from 'prop-types';
import {
  Avatar,
  Box,
  CardContent,
  Chip,
  Grid,
  LinearProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  useTheme
} from '@mui/material';
import {
  AccountBalance,
  Assessment,
  MonitorHeart,
  PieChart,
  Refresh,
  SmartToy,
  Timeline,
  TrendingDown,
  TrendingUp
} from '@mui/icons-material';
import {
  Area,
  AreaChart,
  CartesianGrid,
  Cell,
  Pie,
  Pie<PERSON>hart as RechartsPieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from 'recharts';
import {motion} from 'framer-motion';
import VibrantButton from './VibrantButton';
import ParticleBackground from './ParticleBackground';
import HolographicCard from './HolographicCard';

const logger = {
    info: (...args) => logger.info('[INFO]', ...args),
    error: (...args) => logger.error('[ERROR]', ...args),
    warn: (...args) => logger.warn('[WARN]', ...args)
};

/**
 * @typedef {Object} ElectronAPI
 * @property {() => Promise<{success: boolean, data: any}>} getPortfolioSummary
 * @property {() => Promise<{success: boolean, data: any}>} getTradingStats
 * @property {() => Promise<{success: boolean, data: any}>} getMarketOverview
 * @property {() => Promise<{success: boolean, data: any}>} getRiskMetrics
 * @property {() => Promise<{success: boolean, data: any[]}>} getPriceHistory
 * @property {() => Promise<{success: boolean, data: any[]}>} getAssetAllocation
 * @property {() => Promise<{success: boolean, data: any[]}>} getTradeHistory
 * @property {() => Promise<{success: boolean, data: any[]}>} getActiveBots
 * @property {() => Promise<{success: boolean, data: any}>} getSystemHealth
 * @property {(callback: (event: any, ...args: any[]) => void) => () => void} subscribeToSystemNotifications
 */

/** @type {any} */
const customWindow = window;

const api = customWindow.electronAPI || {
    getPortfolioSummary: () => Promise.resolve({
        success: true,
        data: {totalValue: 15420.83, dailyChange: 2.45, totalReturn: 12.8}
    }),
    getTradingStats: () => Promise.resolve({
        success: true,
        data: {totalTrades: 247, winRate: 68.4, profitLoss: 847.65, activeTrades: 7}
    }),
    getMarketOverview: () => Promise.resolve({
        success: true,
        data: {sentiment: 'bullish', volatility: 15.2, volume: 2840000}
    }),
    getRiskMetrics: () => Promise.resolve({success: true, data: {score: 4.2, maxDrawdown: 8.5, sharpeRatio: 1.85}}),
    getPriceHistory: () => Promise.resolve({success: true, data: generateMockPriceHistory()}),
    getAssetAllocation: () => Promise.resolve({success: true, data: generateMockAssetAllocation()}),
    getTradeHistory: () => Promise.resolve({success: true, data: generateMockTradeHistory()}),
    getActiveBots: () => Promise.resolve({success: true, data: generateMockActiveBots()}),
    getSystemHealth: () => Promise.resolve({
        success: true,
        data: {status: 'HEALTHY', uptime: 98.7, cpu: 45, memory: 62}
    }),
    subscribeToSystemNotifications: () => () => {
    }
};

// Mock data generators
function generateMockPriceHistory() {
    const data = [];
    const baseValue = 15000;
    for (let i = 0; i < 30; i++) {
        data.push({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            value: baseValue + Math.random() * 2000 - 1000 + (i * 20)
        });
    }
    return data;
}

function generateMockAssetAllocation() {
    return [
        {symbol: 'BTC', allocation: 35, value: 5397.29, color: '#f7931a'},
        {symbol: 'ETH', allocation: 25, value: 3855.21, color: '#627eea'},
        {symbol: 'SOL', allocation: 15, value: 2313.12, color: '#9945ff'},
        {symbol: 'AVAX', allocation: 12, value: 1850.50, color: '#e84142'},
        {symbol: 'MATIC', allocation: 8, value: 1233.68, color: '#8247e5'},
        {symbol: 'Others', allocation: 5, value: 771.04, color: '#00d4aa'}];
}

function generateMockTradeHistory() {
    const data = [];
    for (let i = 0; i < 24; i++) {
        data.push({
            time: `${23 - i}:00`,
            price: 50000 + Math.random() * 5000 - 2500,
            volume: Math.random() * 1000000
        });
    }
    return data;
}

function generateMockActiveBots() {
    return [
        {id: 1, name: 'Grid Bot Alpha', status: 'active', profit: 245.67, trades: 34},
        {id: 2, name: 'DCA Bot Beta', status: 'active', profit: 189.23, trades: 28},
        {id: 3, name: 'Arbitrage Gamma', status: 'active', profit: 156.78, trades: 19},
        {id: 4, name: 'Momentum Delta', status: 'active', profit: 134.45, trades: 22},
        {id: 5, name: 'Mean Reversion', status: 'active', profit: 98.32, trades: 15},
        {id: 6, name: 'Scalping Epsilon', status: 'active', profit: 76.89, trades: 41},
        {id: 7, name: 'Swing Zeta', status: 'active', profit: 67.23, trades: 12}];
}

/**
 * UltimateDashboard Header Component
 */
const UltimateDashboardHeader = ({theme, fetchDashboardData, isLoading}) => (
    <motion.div
        initial={{opacity: 0, y: -20}}
        animate={{opacity: 1, y: 0}}
        transition={{duration: 0.5}}
    >
        <Box sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4}}>
            <Typography variant="h4" sx={{
                fontWeight: 900,
                background: 'linear-gradient(45deg, #00eaff 30%, #a259ff 70%, #ff6b6b 90%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: `0 0 20px ${theme.palette.primary.main}40`
            }}>
                ⚡ QUANTUM ELITE TRADING MATRIX
            </Typography>
            <VibrantButton
                onClick={fetchDashboardData}
                startIcon={<Refresh/>}
                disabled={isLoading}
            >
                Refresh Matrix
            </VibrantButton>
        </Box>
    </motion.div>
);

UltimateDashboardHeader.displayName = 'UltimateDashboardHeader';

UltimateDashboardHeader.propTypes = {
    theme: PropTypes.object.isRequired,
    fetchDashboardData: PropTypes.func.isRequired,
    isLoading: PropTypes.bool.isRequired
};

/**
 * MetricCard - Enhanced metric card component for ultimate dashboard
 */
const MetricCard = ({title, value, subtitle, icon: Icon, color, variant = 'quantum', change, status}) => (
    <HolographicCard variant={variant} elevation="high" sx={{height: '100%'}}>
        <CardContent sx={{p: 3, textAlign: 'center'}}>
            <Box sx={{display: 'flex', justifyContent: 'center', mb: 2}}>
                <Icon sx={{fontSize: 48, color: color || 'primary.main'}}/>
            </Box>
            <Typography variant="h6" sx={{color: '#888', mb: 1}}>
                {title}
            </Typography>
            <Typography variant="h4" sx={{color: color || 'primary.main', fontWeight: 800, mb: 1}}>
                {value}
            </Typography>
            {subtitle && (
                <Typography variant="body2" sx={{color: '#666'}}>
                    {subtitle}
                </Typography>
            )}
            {status && (
                <Chip
                    label={status}
                    color={status === 'HEALTHY' ? 'success' : 'error'}
                    size="small"
                    sx={{mt: 1}}
                />
            )}
            {change && (
                <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1}}>
                    {change > 0 ? (
                        <TrendingUp sx={{color: '#4caf50', mr: 0.5}}/>
                    ) : (
                        <TrendingDown sx={{color: '#f44336', mr: 0.5}}/>
                    )}
                    <Typography variant="body2" sx={{color: change > 0 ? '#4caf50' : '#f44336', fontWeight: 600}}>
                        {change > 0 ? '+' : ''}{change.toFixed(2)}%
                    </Typography>
                </Box>
            )}
        </CardContent>
    </HolographicCard>
);

MetricCard.propTypes = {
    title: PropTypes.string.isRequired,
    value: PropTypes.string.isRequired,
    subtitle: PropTypes.string,
    icon: PropTypes.elementType.isRequired,
    color: PropTypes.string,
    variant: PropTypes.string,
    change: PropTypes.number,
    status: PropTypes.string
};

/**
 * Ultimate Trading Dashboard Component
 *
 * @description Quantum elite trading matrix providing comprehensive real-time
 * portfolio overview, advanced bot management, market intelligence, and system monitoring.
 * Features holographic UI elements, particle effects, and AI-powered insights.
 *
 * @component
 * @param {Object} props - Component props.
 * @param {(message: string, type: 'success'|'error'|'warning'|'info') => void} props.showNotification - Function to display notifications.
 *
 * @returns {React.ReactElement} The rendered ultimate dashboard component
 *
 * @example
 * // Basic usage
 * <UltimateDashboard showNotification={(msg, type) => logger.info(msg)} />
 *
 * @since 1.0.0
 * <AUTHOR> Team
 */
const UltimateDashboard = ({showNotification}) => {
    const theme = useTheme();
    const [isLoading, setIsLoading] = useState(true);
    const [dashboardData, setDashboardData] = useState({
        portfolio: {totalValue: 15420.83, dailyChange: 2.45, totalReturn: 12.8},
        trading: {totalTrades: 247, winRate: 68.4, profitLoss: 847.65, activeTrades: 7},
        market: {sentiment: 'bullish', volatility: 15.2, volume: 2840000},
        risk: {score: 4.2, maxDrawdown: 8.5, sharpeRatio: 1.85},
        assets: [],
        priceHistory: [],
        trades: [],
        bots: [],
        systemHealth: {status: 'HEALTHY', uptime: 98.7, cpu: 45, memory: 62}
    });

    const formatCurrency = (value) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(value || 0);
    };

    const fetchDashboardData = useCallback(async () => {
        try {
            setIsLoading(true);
            logger.info('Fetching ultimate dashboard data...');

            const [
                portfolioSummary,
                tradingStats,
                marketOverview,
                riskMetrics,
                priceHistory,
                assetAllocation,
                tradeHistory,
                activeBots,
                systemHealth] = await Promise.all([
                api.getPortfolioSummary(),
                api.getTradingStats(),
                api.getMarketOverview(),
                api.getRiskMetrics(),
                api.getPriceHistory(),
                api.getAssetAllocation(),
                api.getTradeHistory(),
                api.getActiveBots(),
                api.getSystemHealth()]);

            logger.info('Ultimate dashboard data fetched successfully');

            setDashboardData({
                portfolio: portfolioSummary.success ? portfolioSummary.data : {
                    totalValue: 15420.83,
                    dailyChange: 2.45,
                    totalReturn: 12.8
                },
                trading: tradingStats.success ? tradingStats.data : {
                    totalTrades: 247,
                    winRate: 68.4,
                    profitLoss: 847.65,
                    activeTrades: 7
                },
                market: marketOverview.success ? marketOverview.data : {
                    sentiment: 'bullish',
                    volatility: 15.2,
                    volume: 2840000
                },
                risk: riskMetrics.success ? riskMetrics.data : {score: 4.2, maxDrawdown: 8.5, sharpeRatio: 1.85},
                priceHistory: priceHistory.success ? priceHistory.data : generateMockPriceHistory(),
                assets: assetAllocation.success ? assetAllocation.data : generateMockAssetAllocation(),
                trades: tradeHistory.success ? tradeHistory.data : generateMockTradeHistory(),
                bots: activeBots.success ? activeBots.data : generateMockActiveBots(),
                systemHealth: systemHealth.success ? systemHealth.data : {
                    status: 'HEALTHY',
                    uptime: 98.7,
                    cpu: 45,
                    memory: 62
                }
            });

        } catch (error) {
            logger.error('Ultimate dashboard fetch error:', error);
            if (showNotification) {
                showNotification('Failed to fetch ultimate dashboard data', 'error');
            }
        } finally {
            setIsLoading(false);
        }
    }, [showNotification]);

    useEffect(() => {
        fetchDashboardData();

        // Set up real-time updates
        const interval = setInterval(fetchDashboardData, 15000); // Update every 15 seconds

        return () => clearInterval(interval);
    }, [fetchDashboardData]);

    const chartColors = {
        primary: '#00eaff',
        secondary: '#a259ff',
        success: '#4caf50',
        warning: '#ff9800',
        error: '#f44336',
        quantum: '#ff6b6b'
    };

    return (
        <Box sx={{position: 'relative', minHeight: '100vh', p: 3}}>
            <ParticleBackground tradingActive={dashboardData.trading.activeTrades > 0} intensity={1.5}/>

            <UltimateDashboardHeader
                theme={theme}
                fetchDashboardData={fetchDashboardData}
                isLoading={isLoading}
            />

            {isLoading && (
                <LinearProgress
                    sx={{
                        mb: 2,
                        backgroundColor: 'rgba(0,234,255,0.1)',
                        '& .MuiLinearProgress-bar': {
                            background: 'linear-gradient(90deg, #00eaff 0%, #a259ff 50%, #ff6b6b 100%)'
                        }
                    }}
                />
            )}

            <Grid container spacing={3}>
                {/* Elite Metrics Row */}
                <Grid item xs={12} md={6} lg={3}>
                    <MetricCard
                        title="Portfolio Value"
                        value={formatCurrency(dashboardData.portfolio.totalValue)}
                        subtitle="Total Assets Under Management"
                        icon={AccountBalance}
                        color={chartColors.primary}
                        variant="quantum"
                        change={Number(dashboardData.portfolio.dailyChange)}
                    />
                </Grid>

                <Grid item xs={12} md={6} lg={3}>
                    <MetricCard
                        title="Active Bots"
                        value={dashboardData.trading.activeTrades.toString()}
                        subtitle="AI Trading Algorithms"
                        icon={SmartToy}
                        color={chartColors.secondary}
                        variant="premium"
                    />
                </Grid>

                <Grid item xs={12} md={6} lg={3}>
                    <MetricCard
                        title="System Status"
                        value={dashboardData.systemHealth.status}
                        subtitle={`Uptime: ${dashboardData.systemHealth.uptime}%`}
                        icon={MonitorHeart}
                        color={chartColors.success}
                        variant="success"
                        status={dashboardData.systemHealth.status}
                    />
                </Grid>

                <Grid item xs={12} md={6} lg={3}>
                    <MetricCard
                        title="Total P&L"
                        value={formatCurrency(dashboardData.trading.profitLoss)}
                        subtitle={`Win Rate: ${dashboardData.trading.winRate}%`}
                        icon={Assessment}
                        color={dashboardData.trading.profitLoss >= 0 ? chartColors.success : chartColors.error}
                        variant={dashboardData.trading.profitLoss >= 0 ? 'success' : 'error'}
                    />
                </Grid>

                {/* Portfolio Performance Chart */}
                <Grid item xs={12} lg={8}>
                    <HolographicCard variant="quantum" elevation="high" sx={{height: '100%'}}>
                        <CardContent sx={{p: 3}}>
                            <Typography variant="h6"
                                        sx={{color: chartColors.primary, mb: 2, display: 'flex', alignItems: 'center'}}>
                                <Timeline sx={{mr: 1}}/>
                                Portfolio Performance
                            </Typography>
                            <ResponsiveContainer width="100%" height={350}>
                                <AreaChart data={dashboardData.priceHistory}>
                                    <defs>
                                        <linearGradient id="portfolioGradient" x1="0" y1="0" x2="0" y2="1">
                                            <stop offset="5%" stopColor={chartColors.primary} stopOpacity={0.8}/>
                                            <stop offset="50%" stopColor={chartColors.secondary} stopOpacity={0.4}/>
                                            <stop offset="95%" stopColor={chartColors.quantum} stopOpacity={0.1}/>
                                        </linearGradient>
                                    </defs>
                                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)"/>
                                    <XAxis dataKey="date" stroke="#888"/>
                                    <YAxis stroke="#888"/>
                                    <Tooltip
                                        contentStyle={{
                                            backgroundColor: 'rgba(24,26,32,0.95)',
                                            border: `1px solid ${chartColors.primary}`,
                                            borderRadius: '8px',
                                            color: '#fff'
                                        }}
                                        formatter={(value) => [formatCurrency(value), 'Portfolio Value']}
                                    />
                                    <Area
                                        type="monotone"
                                        dataKey="value"
                                        stroke={chartColors.primary}
                                        fill="url(#portfolioGradient)"
                                        strokeWidth={3}
                                    />
                                </AreaChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </HolographicCard>
                </Grid>

                {/* Asset Allocation */}
                <Grid item xs={12} lg={4}>
                    <HolographicCard variant="premium" elevation="high" sx={{height: '100%'}}>
                        <CardContent sx={{p: 3}}>
                            <Typography variant="h6"
                                        sx={{
                                            color: chartColors.secondary,
                                            mb: 2,
                                            display: 'flex',
                                            alignItems: 'center'
                                        }}>
                                <PieChart sx={{mr: 1}}/>
                                Asset Allocation
                            </Typography>
                            <ResponsiveContainer width="100%" height={300}>
                                <RechartsPieChart>
                                    <Pie
                                        data={dashboardData.assets}
                                        dataKey="allocation"
                                        nameKey="symbol"
                                        cx="50%"
                                        cy="50%"
                                        outerRadius={80}
                                        label={({symbol, allocation}) => `${symbol} ${allocation}%`}
                                    >
                                        {dashboardData.assets.map((asset) => (
                                            <Cell key={asset.symbol} fill={asset.color}/>
                                        ))}
                                    </Pie>
                                    <Tooltip
                                        contentStyle={{
                                            backgroundColor: 'rgba(24,26,32,0.95)',
                                            border: `1px solid ${chartColors.secondary}`,
                                            borderRadius: '8px',
                                            color: '#fff'
                                        }}
                                        formatter={(value, name, entry) => {
                                            const total = entry.payload.value;
                                            return [`${formatCurrency(total)} (${value}%)`, name];
                                        }}
                                    />
                                </RechartsPieChart>
                            </ResponsiveContainer>
                        </CardContent>
                    </HolographicCard>
                </Grid>

                {/* Active Bots Table */}
                <Grid item xs={12}>
                    <HolographicCard variant="default" elevation="high" sx={{height: '100%'}}>
                        <CardContent sx={{p: 3}}>
                            <Typography variant="h6"
                                        sx={{color: chartColors.quantum, mb: 2, display: 'flex', alignItems: 'center'}}>
                                <SmartToy sx={{mr: 1}}/>
                                Elite AI Trading Bots
                            </Typography>
                            <TableContainer component={Paper} sx={{borderRadius: 2, overflow: 'hidden'}}>
                                <Table>
                                    <TableHead>
                                        <TableRow>
                                            <TableCell sx={{color: '#888', borderBottom: 'none'}}>Bot Name</TableCell>
                                            <TableCell sx={{color: '#888', borderBottom: 'none'}}>Status</TableCell>
                                            <TableCell sx={{color: '#888', borderBottom: 'none'}}
                                                       align="right">Profit</TableCell>
                                            <TableCell sx={{color: '#888', borderBottom: 'none'}}
                                                       align="right">Trades</TableCell>
                                            <TableCell sx={{color: '#888', borderBottom: 'none'}}
                                                       align="right">Performance</TableCell>
                                        </TableRow>
                                    </TableHead>
                                    <TableBody>
                                        {dashboardData.bots.map((bot) => (
                                            <TableRow key={bot.id}>
                                                <TableCell sx={{color: '#fff', borderBottom: 'none'}}>
                                                    <Box sx={{display: 'flex', alignItems: 'center', gap: 1}}>
                                                        <Avatar sx={{
                                                            width: 32,
                                                            height: 32,
                                                            bgcolor: chartColors.secondary
                                                        }}>
                                                            {bot.name.charAt(0)}
                                                        </Avatar>
                                                        <Typography variant="body2" sx={{fontWeight: 500}}>
                                                            {bot.name}
                                                        </Typography>
                                                    </Box>
                                                </TableCell>
                                                <TableCell sx={{color: '#fff', borderBottom: 'none'}}>
                                                    <Chip
                                                        label={bot.status}
                                                        color="success"
                                                        size="small"
                                                        sx={{textTransform: 'uppercase'}}
                                                    />
                                                </TableCell>
                                                <TableCell sx={{color: chartColors.success, borderBottom: 'none'}}
                                                           align="right">
                                                    {formatCurrency(bot.profit)}
                                                </TableCell>
                                                <TableCell sx={{color: '#fff', borderBottom: 'none'}} align="right">
                                                    {bot.trades}
                                                </TableCell>
                                                <TableCell sx={{borderBottom: 'none'}} align="right">
                                                    <LinearProgress
                                                        variant="determinate"
                                                        value={Math.min((bot.profit / 300) * 100, 100)}
                                                        sx={{
                                                            width: 60,
                                                            backgroundColor: 'rgba(162,89,255,0.2)',
                                                            '& .MuiLinearProgress-bar': {
                                                                backgroundColor: chartColors.success
                                                            }
                                                        }}
                                                    />
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </CardContent>
                    </HolographicCard>
                </Grid>
            </Grid>
        </Box>
    );
};

UltimateDashboard.propTypes = {
    showNotification: PropTypes.func
};

UltimateDashboard.defaultProps = {
    showNotification: (message, severity) => {
        logger.info(`${severity?.toUpperCase() || 'INFO'}: ${message}`);
    }
};

export default UltimateDashboard;
