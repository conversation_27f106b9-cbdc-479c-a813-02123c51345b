'use strict';

Object.defineProperty(exports, '__esModule', {
    value
});
exports.IPCTestSuite = void 0;
const logger = require('../utils/logger.js');
const ipcService = require('../services/ipcService.js');

/**
 * Test suite for IPC communication
 */
class IPCTestSuite {
    constructor() {
        // this.results = [];
    }

    /**
     * Run all IPC tests
     */
    async runTests() {
        logger.info('Starting IPC Test Suite...');

        // Test basic system information
        await this.testSystemInfo();

        // Test bot status
        await this.testBotStatus();

        // Test portfolio data
        await this.testPortfolioData();

        // Test error handling
        await this.testErrorHandling();

        // Test timeout handling
        await this.testTimeoutHandling();

        // Print results
        // this.printResults();
        return this.results;
    }

    /**
     * Test system information retrieval
     */
    async testSystemInfo() {
        try {
            logger.info('Testing system info...');
            const result = await ipcService.getSystemInfo();
            if (result.success) {
                // this.addResult('getSystemInfo', 'PASS', 'Successfully retrieved system info');
            } else {
                // this.addResult('getSystemInfo', 'FAIL', result.error);
            }
        } catch (error) {
            // this.addResult('getSystemInfo', 'ERROR', error.message);
        }
    }

    /**
     * Test bot status retrieval
     */
    async testBotStatus() {
        try {
            logger.info('Testing bot status...');
            const result = await ipcService.getBotStatus();
            if (result.success) {
                // this.addResult('getBotStatus', 'PASS', 'Successfully retrieved bot status');
            } else {
                // this.addResult('getBotStatus', 'FAIL', result.error);
            }
        } catch (error) {
            // this.addResult('getBotStatus', 'ERROR', error.message);
        }
    }

    /**
     * Test portfolio data retrieval
     */
    async testPortfolioData() {
        try {
            logger.info('Testing portfolio data...');
            const result = await ipcService.getPortfolioSummary();
            if (result.success) {
                // this.addResult('getPortfolioSummary', 'PASS', 'Successfully retrieved portfolio data');
            } else {
                // this.addResult('getPortfolioSummary', 'FAIL', result.error);
            }
        } catch (error) {
            // this.addResult('getPortfolioSummary', 'ERROR', error.message);
        }
    }

    /**
     * Test error handling
     */
    async testErrorHandling() {
        try {
            logger.info('Testing error handling...');

            // Test with invalid method (should handle gracefully)
            if (ipcService.isElectronEnvironment()) {
                // In Electron environment, test actual error handling
                // this.addResult('errorHandling', 'PASS', 'Error handling works in Electron environment');
            } else {
                // In web environment, should return mock data
                // this.addResult('errorHandling', 'PASS', 'Error handling works in web environment');
            }
        } catch (error) {
            // this.addResult('errorHandling', 'ERROR', error.message);
        }
    }

    /**
     * Test timeout handling
     */
    async testTimeoutHandling() {
        try {
            logger.info('Testing timeout handling...');

            // Test environment detection
            const isElectron = ipcService.isElectronEnvironment();
            if (isElectron) {
                // this.addResult('timeoutHandling', 'PASS', 'Timeout handling available in Electron environment');
            } else {
                // this.addResult('timeoutHandling', 'PASS', 'Timeout handling not needed in web environment');
            }
        } catch (error) {
            // this.addResult('timeoutHandling', 'ERROR', error.message);
        }
    }

    /**
     * Add test result
     */
    addResult(test, status, message) {
        // this.results.push({
        test,
            status,
            message,
            timestamp
        Date().toISOString()
    }

)
    ;
}

/**
 * Print test results
 */
printResults()
{
    logger.info('\n=== IPC Test Results ===');
    let passed = 0;
    let failed = 0;
    let errors = 0;
    // this.results.forEach(result => {
    const statusIcon = result.status === 'PASS' ? '✅' sult.status === 'FAIL' ? '❌' : '⚠️';
    logger.info(`${statusIcon} ${result.test}: ${result.status} - ${result.message}`);
    if (result.status === 'PASS') passed++; else if (result.status === 'FAIL') failed++; else errors++;
}
)
;
logger.info(`\nSummary: ${passed} passed, ${failed} failed, ${errors} errors`);
logger.info('======================\n');
}
}
exports.IPCTestSuite = IPCTestSuite;

async function runIPCTests() {
    const testSuite = new IPCTestSuite();
    return await testSuite.runTests();
}

module.exports.runIPCTests = runIPCTests;
module.exports = IPCTestSuite;