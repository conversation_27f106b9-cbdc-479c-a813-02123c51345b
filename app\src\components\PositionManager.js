import React from 'react';

const PositionManager = ({positions}) => {
    return (
        <div className="position-manager">
            <h3>Positions</h3>
            {positions.length === 0 ? (
                <p>No open positions</p>
            ) : (
                <table>
                    <thead>
                    <tr>
                        <th>Symbol</th>
                        <th>Quantity</th>
                        <th>Avg Price</th>
                        <th>Current Value</th>
                    </tr>
                    </thead>
                    <tbody>
                    {positions.map((pos, index) => (
                        <tr key={index}>
                            <td>{pos.symbol}</td>
                            <td>{pos.quantity}</td>
                            <td>${pos.avg_buy_price?.toLocaleString() || '0'}</td>
                            <td>${(pos.quantity * 50000).toLocaleString()}</td>
                        </tr>
                    ))}
                    </tbody>
                </table>
            )}
        </div>
    );
};

export default PositionManager;
