# Function Call Graph Analysis

## Statistics

- **Total Functions**: 4589
- **Total Calls**: 3876
- **Average Complexity**: 1.00
- **Max Call Depth**: 0
- **Entry Points**: 0
- **Leaf Functions**: 4589
- **Cycles Detected**: 0

## Most Called Functions

| Function          | File                                            | Called By | Complexity |
|-------------------|-------------------------------------------------|-----------|------------|
| `ownKeys`         | app\config-overrides.js:3                       | 0         | 1          |
| `_objectSpread`   | app\config-overrides.js:13                      | 0         | 1          |
| `_defineProperty` | app\config-overrides.js:24                      | 0         | 1          |
| `_toPropertyKey`  | app\config-overrides.js:32                      | 0         | 1          |
| `_toPrimitive`    | app\config-overrides.js:36                      | 0         | 1          |
| `toggleClass`     | app\coverage\lcov-report\block-navigation.js:24 | 0         | 1          |
| `makeCurrent`     | app\coverage\lcov-report\block-navigation.js:31 | 0         | 1          |
| `goToPrevious`    | app\coverage\lcov-report\block-navigation.js:41 | 0         | 1          |
| `goToNext`        | app\coverage\lcov-report\block-navigation.js:52 | 0         | 1          |
| `k`               | app\coverage\lcov-report\prettify.js:2          | 0         | 1          |

## Most Complex Functions

| Function          | File                                            | Complexity | Calls | Called By |
|-------------------|-------------------------------------------------|------------|-------|-----------|
| `ownKeys`         | app\config-overrides.js:3                       | 1          | 0     | 0         |
| `_objectSpread`   | app\config-overrides.js:13                      | 1          | 0     | 0         |
| `_defineProperty` | app\config-overrides.js:24                      | 1          | 0     | 0         |
| `_toPropertyKey`  | app\config-overrides.js:32                      | 1          | 0     | 0         |
| `_toPrimitive`    | app\config-overrides.js:36                      | 1          | 0     | 0         |
| `toggleClass`     | app\coverage\lcov-report\block-navigation.js:24 | 1          | 0     | 0         |
| `makeCurrent`     | app\coverage\lcov-report\block-navigation.js:31 | 1          | 0     | 0         |
| `goToPrevious`    | app\coverage\lcov-report\block-navigation.js:41 | 1          | 0     | 0         |
| `goToNext`        | app\coverage\lcov-report\block-navigation.js:52 | 1          | 0     | 0         |
| `k`               | app\coverage\lcov-report\prettify.js:2          | 1          | 0     | 0         |

## Analysis Tips

### Identifying Bottlenecks

- Functions with high "Called By" counts may be bottlenecks
- Functions with high complexity should be refactored
- Long call chains may indicate design issues

### Refactoring Opportunities

- Extract common functionality from highly called functions
- Break down complex functions (complexity > 10)
- Resolve circular dependencies
- Consider caching for frequently called functions

